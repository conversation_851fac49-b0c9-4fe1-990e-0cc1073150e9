{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982a4901f77847d14ee58b39a0c1194841", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseFirestore", "PRODUCT_NAME": "FirebaseFirestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980f7b345e64c59f4a4ab5103d67bba2e2", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984f8f9c12cfd29ae671269c39b4880737", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseFirestore", "PRODUCT_NAME": "FirebaseFirestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98985988ed7dd646b742f183b594336310", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984f8f9c12cfd29ae671269c39b4880737", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseFirestore", "PRODUCT_NAME": "FirebaseFirestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9882fd366fe3b0cfb79ad525e7215c0b3c", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9870ecdd4ad82ccb4086052af2ebc32ff9", "guid": "bfdfe7dc352907fc980b868725387e98b63b97e91f5663201aa159ccb002379f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9869505f9f6cf2798f145479630f836b54", "guid": "bfdfe7dc352907fc980b868725387e98dc1b328fa6d5b9aecc613a362c7323ba", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98794fc7ae8016110780b22b23fe3fc739", "guid": "bfdfe7dc352907fc980b868725387e98fb4b1d83906d411e0953982b298f0517", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9857e502c3593c3d364956a28222056b88", "guid": "bfdfe7dc352907fc980b868725387e9814f4079f8f307d784f22239a2cf679d5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9802f83d855a50ab725cec5da3b54c3e3b", "guid": "bfdfe7dc352907fc980b868725387e98906c52ef700740c76106110e37b7e228", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b0d15feeddc46cf4fd8d67a463abe148", "guid": "bfdfe7dc352907fc980b868725387e9807643eda3cd4f4567009529f9c98ade2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9872a7d40148a4437c54665cdb16d64d50", "guid": "bfdfe7dc352907fc980b868725387e9812d1b1d68dcbf2d538b0d326fdf82ad2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98646ab249d8dd258ec503aaa3ec9c3381", "guid": "bfdfe7dc352907fc980b868725387e983424236866570f773a3844acaf6f25b4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ab25b8e2eae500eb06e102af4dce8c3", "guid": "bfdfe7dc352907fc980b868725387e98fe1600eb8d63a0e5a9358b9ecbff1fb7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b402f914ddfce940309d7c68c8774aa2", "guid": "bfdfe7dc352907fc980b868725387e9830e605a67c98650aacfbecd9c2d11ead", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98abc7a6df8f69257b93b497b7b5d67ef2", "guid": "bfdfe7dc352907fc980b868725387e98fa3fab1fdea07b688aff3f12f1356b37", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9842cf6a33ae5ab5d37d6bd5622f3a4243", "guid": "bfdfe7dc352907fc980b868725387e989bbe6b6fd5961ee0da2fe2d9b4c0355b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98495c72404b9477a3b7fc7f19fdf1bd04", "guid": "bfdfe7dc352907fc980b868725387e98a313f052a6d27a646d71c64cf1e44e85", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989bac9f6624cde918eee61c764a4ae432", "guid": "bfdfe7dc352907fc980b868725387e989d5bfc38493165f9303fdf3cca58b8ee", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe8d730a597d556a6f0110e6fea17557", "guid": "bfdfe7dc352907fc980b868725387e98c5540071d6738534d8ee591cab0d4412", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a9999faca83e1dd570c97b1b9dc58ec1", "guid": "bfdfe7dc352907fc980b868725387e9827693e2a3438014bfe51bf35bffbf625", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9810e54cf65e4d94579835fbe98ec55bab", "guid": "bfdfe7dc352907fc980b868725387e9899c8e885520c9d7e65429c85c9f0228d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9868e5912f33792c208ac9cc29c0181c97", "guid": "bfdfe7dc352907fc980b868725387e98e5fd8bcefa690ec065c13b2db32cb3a9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b053a2b06bcf6e2274508d2f9de9c7fb", "guid": "bfdfe7dc352907fc980b868725387e98293b32e3dbe9f620affe91219d0ebfa4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b66b765eefad0e00054401ec2263acb4", "guid": "bfdfe7dc352907fc980b868725387e98cb9a2dcdcd1df6410dfb44fef19899b1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b8a9ffbf46a4bc63e23cc3765ae82c56", "guid": "bfdfe7dc352907fc980b868725387e98b931e7b6d09595ade8ca860422c3ca9d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988633eb3dbd431a73132e108606ddb49f", "guid": "bfdfe7dc352907fc980b868725387e98de27aab63cc96ba074fbe277b15762f4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9875afe18113e52f0fbd978338c11cfe58", "guid": "bfdfe7dc352907fc980b868725387e98c39edb5a771d1f934d3dd7cd5bed05eb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9898f91a06b891f7fcba4c01f7b2eacc5a", "guid": "bfdfe7dc352907fc980b868725387e989d24f48efa33bdde7551d05b78f34d17", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ef69bba9c329098b114b45cc2f30610", "guid": "bfdfe7dc352907fc980b868725387e986cba8b116c58c2b5e552e9e4d0048ae5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9840274b3bcf9274f0459bc3baf175b775", "guid": "bfdfe7dc352907fc980b868725387e98c7fbb828a4180a5a8a134092e5fd18b3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a4b978ed2fcfdc943562cd484807720c", "guid": "bfdfe7dc352907fc980b868725387e9817e8d033bfced652e74bec23b87d178f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fba092f4545961ea7e9e4050baad0eae", "guid": "bfdfe7dc352907fc980b868725387e98140f562c79a5d10d07d2f5661e8ecaf2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f29ea37c8ba1e806ea6ccc8fc2c8f00b", "guid": "bfdfe7dc352907fc980b868725387e98b62c53af93d33dc1af619006b7d0573e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a1f588154f30a883cf1cf764e14c219a", "guid": "bfdfe7dc352907fc980b868725387e985674a0c503074bddd46f4f1c446abb72", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98737da3c2e0bb51747e2b4cd3eb06fe30", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9841a37584b547cc92b401cf2172ff5992", "guid": "bfdfe7dc352907fc980b868725387e984b3a638b4d9e6d335f8a4c51d4678c5e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98628e4b5b2818aaaa07b461ef905f9638", "guid": "bfdfe7dc352907fc980b868725387e98285736072ba8c53d146f48ff3146ad1d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98038a07e335d2f6e19d90219bf0c78d12", "guid": "bfdfe7dc352907fc980b868725387e98e5491dd9888d15ade7f223dc5edd41a9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e096d141f2033b688d28114080876e90", "guid": "bfdfe7dc352907fc980b868725387e98e89d78fffa6b7f0dc1f1025d2cb51681"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981da74dea4b83e3d1e838b6930da61943", "guid": "bfdfe7dc352907fc980b868725387e9829538451f70cd523241385e96f75fbe9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986ed8c96d13eb05f854741e20d6cb6c39", "guid": "bfdfe7dc352907fc980b868725387e985cf158aef7873ee78009e0c4d14e799c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987466e1c16d4f99b75b18bea7974e22ea", "guid": "bfdfe7dc352907fc980b868725387e9803b90810581f6d92c5a1916c49998a19"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9809e15375318f6bc04e94ccd783e5a3f8", "guid": "bfdfe7dc352907fc980b868725387e98145e53d80d278c164efcdaa1e9aa7b5b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98014f9099f8bf773c2555031cccf491ce", "guid": "bfdfe7dc352907fc980b868725387e989616b9442373b20d86fb30c581656ebc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c9d1d5446c08c7cc68e0612fa025071f", "guid": "bfdfe7dc352907fc980b868725387e98852da0496fe38be99f52e042e6cc3d0e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98246caa1f86505b3b84a155eca8122cd0", "guid": "bfdfe7dc352907fc980b868725387e98f21681aef80e0c61dfece6c91bc2cd05"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b2609d084fdb1e31e3a48245c1ad487e", "guid": "bfdfe7dc352907fc980b868725387e987c77d1f8cb2d904510e50b0c4ca1753d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9890c260d6c0ebbd385eb3e3073d8e5014", "guid": "bfdfe7dc352907fc980b868725387e983ed64654e75e1f04f0fbcfc90ed89039"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982b55479730ff0156d98cc9e3213ef603", "guid": "bfdfe7dc352907fc980b868725387e98d5364ceb998934bffbb66d5fd99f4221"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c8c59c4118aa31e10f4d17cd1aa01f3", "guid": "bfdfe7dc352907fc980b868725387e984945aef11c420642d02ccf2a53f7c9c0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ab32d660dc72b319ca3f03a823462a7c", "guid": "bfdfe7dc352907fc980b868725387e98680d7694575c439542090de26aab1eb4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f91d821116e2808ca435a5c64f5e4cb3", "guid": "bfdfe7dc352907fc980b868725387e98943e75d23bf72a89969b81e0363e5499"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c728d2003c6d4ece396d9451e8ea3b6b", "guid": "bfdfe7dc352907fc980b868725387e98fb8ee68ccb8dc0c418b79b23904f0076"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c422aad976a90eb652e62f21aa87ece3", "guid": "bfdfe7dc352907fc980b868725387e984c0cce409fe942a042de5547b3acaf07"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9838b0dc148652d4be9cd6361594f3a851", "guid": "bfdfe7dc352907fc980b868725387e989b6bdc7cd69ff31f92975f0611f6cc0b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981f01d0a1f0b2e54bfd03a798ce41b2ff", "guid": "bfdfe7dc352907fc980b868725387e9826e59b4c6cb4f327600f7368797d1758"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d50cf01b77c5d1bd13cf8d0d3b38a307", "guid": "bfdfe7dc352907fc980b868725387e983afe762865fc106aed3c90c20c94e13e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc342adedaa55e67653fcdd01d0d322c", "guid": "bfdfe7dc352907fc980b868725387e98bbdc02db0fedfea1c496a1f2e41af619"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984e4e7c4d54f9927f6aa53e287329004f", "guid": "bfdfe7dc352907fc980b868725387e984965450932a012b8ca3707eac135dd09"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982c160a9c6a4da2983d3d7e492b48f08c", "guid": "bfdfe7dc352907fc980b868725387e98242b46e1ddf766530a2fada5e4df8709"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ca32bc4566aba8390c6fbe31718fe782", "guid": "bfdfe7dc352907fc980b868725387e98c6aba1f13f57bec872e7e14ec999b4f8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98381ca98b999341ce7d40a9822e9d9158", "guid": "bfdfe7dc352907fc980b868725387e98c8492a47f85b14a96e45fc9eb182308f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980cd031f24c9bd37d8e1bdc433662abdf", "guid": "bfdfe7dc352907fc980b868725387e98c50993a6e631304796967f1d16ca4282"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fac7ee9f6585f06eb1b81a0b2d6af669", "guid": "bfdfe7dc352907fc980b868725387e98f4360113d850124f852553ae353e585d"}], "guid": "bfdfe7dc352907fc980b868725387e987c780efe9d1963d2bb72d648b027acbd", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9824f5d6ec5088eecbb83955c855580732", "guid": "bfdfe7dc352907fc980b868725387e98d460453a6941e2cc2daeef99c64dcf68"}], "guid": "bfdfe7dc352907fc980b868725387e9848fcda0a73943b742c908283dd46c97b", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e984688e278ed2c528d58caa0ffc9d461a8", "targetReference": "bfdfe7dc352907fc980b868725387e9823d9e39e74dec872f3c67d98403b9340"}], "guid": "bfdfe7dc352907fc980b868725387e988420bc59c60f0884794616377632705c", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e982fcb5e27d041e48b96b3ab14ce32d5f2", "name": "FirebaseCoreExtension"}, {"guid": "bfdfe7dc352907fc980b868725387e9823d9e39e74dec872f3c67d98403b9340", "name": "FirebaseFirestore-FirebaseFirestore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98919212c22943df12241906dd601cdff4", "name": "FirebaseFirestoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e982a62e2c60acb8d344a6411a0606a13d4", "name": "FirebaseSharedSwift"}], "guid": "bfdfe7dc352907fc980b868725387e98c075cc473fa5680b867d51f1363214ff", "name": "FirebaseFirestore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9809dfd848e2259e061e90089e1647f5b7", "name": "FirebaseFirestore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}