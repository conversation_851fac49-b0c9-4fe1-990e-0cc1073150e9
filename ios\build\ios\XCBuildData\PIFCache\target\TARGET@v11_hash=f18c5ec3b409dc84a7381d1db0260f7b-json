{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988d487bfbd99f656f007147b7bd34d59f", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_image_compress_common", "PRODUCT_NAME": "flutter_image_compress_common", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980fe6008bd0ea7d60a239f5cfb496f9fd", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a0dcabac8cc86d39bbb74e1c6b0e4c1c", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_image_compress_common", "PRODUCT_NAME": "flutter_image_compress_common", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984ac27f284fbf5fa4242b11e4c252631e", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a0dcabac8cc86d39bbb74e1c6b0e4c1c", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_image_compress_common", "PRODUCT_NAME": "flutter_image_compress_common", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ec29c327167a123c1352766ac15557e9", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c37761592f503157e9e856960ccd903c", "guid": "bfdfe7dc352907fc980b868725387e9840a9caeba178add7aa4dceb4c1bb75fe", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984fd74d17b168804dd1901657450c94e0", "guid": "bfdfe7dc352907fc980b868725387e98b4c61cc35fba40b4ce329132c3f61eca", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9807285dbfb267ec3ba81f6a490af47338", "guid": "bfdfe7dc352907fc980b868725387e9818da142fe82d8a78c7be9db931d371ba", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9867345cdf2bd2f4f47f060043d0a10dce", "guid": "bfdfe7dc352907fc980b868725387e98df995503faf86a7e536e8fd0813b9b8f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc14cf9908a37c58506592ae5aa1b25a", "guid": "bfdfe7dc352907fc980b868725387e984b4ae0c89031a24d1d71288e24a797ca", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981ec896e1975891018acfa97e62e98813", "guid": "bfdfe7dc352907fc980b868725387e985f28519c628b2857ebb37b7b581178d4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ce50eeba5bf7992f8279dc240dc9225e", "guid": "bfdfe7dc352907fc980b868725387e98d488e0a22f4c14aacdf4d40c2c9ddb44", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3631e72ed7b6b7e7197346c77a4193a", "guid": "bfdfe7dc352907fc980b868725387e98fe7273ad9aa9fdcb548c9c43dc93f8b4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98441c6705d28fc7f8a1a331c30188e8d0", "guid": "bfdfe7dc352907fc980b868725387e98313d9e25b0bf5f3c97ad080d07ef3914", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d316cbcc7d74c151d439f1888e3ed3fa", "guid": "bfdfe7dc352907fc980b868725387e989f407ff8592b35f500a6527a20317817", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984f2b555b82d1f3fc4658dddb0bb8cb60", "guid": "bfdfe7dc352907fc980b868725387e98e16efa990e491cc807ce6fc10625d15b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9818234141bdc608e9ed7e95aa9df5fd3b", "guid": "bfdfe7dc352907fc980b868725387e98105db008f99641fadb0caf5f7506b3c4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e8c1300fddffb1e84829c422b0691ff5", "guid": "bfdfe7dc352907fc980b868725387e98a6e4e157926dc87d2e4f2973b18d8bcc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987808d650d9c47afc48b1dc1bd308e606", "guid": "bfdfe7dc352907fc980b868725387e98d1ddc3be1007fb9ee70d27ff32e03352", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb5f34acef747da2fa4a7f3be06dd935", "guid": "bfdfe7dc352907fc980b868725387e980f430574f80b3377bafb28f408603313", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988fb0c80228f5dbb70cd55c3840540c77", "guid": "bfdfe7dc352907fc980b868725387e9811c36f63ef52dcfc9629b13ac9cd2366", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa9a0c02cf5a6cfb006473ededc79ce6", "guid": "bfdfe7dc352907fc980b868725387e98fdcde1de031b3934e8ad535d6a77715a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9811fb649175262828afe74e481ad8dee1", "guid": "bfdfe7dc352907fc980b868725387e9806312812ff309343658d470012c9949e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bc291d91df6870e782086e7159f17d90", "guid": "bfdfe7dc352907fc980b868725387e98fd547b1fbc8601ed045148719c9fcb31", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ca09a99ec4108d3d38f9422401e48a7", "guid": "bfdfe7dc352907fc980b868725387e98b8107d30d41b046eabedd15b3e204207", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9859c2bebfaed059a1b3b6905e376188c3", "guid": "bfdfe7dc352907fc980b868725387e985911bdce5650368ba78e000854a76edc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987881785f3a697923ce3d12ab0bc43696", "guid": "bfdfe7dc352907fc980b868725387e9851879a7bd58d5db494aaffdae14944fc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ca7f5027b811092914b830a98547aa1b", "guid": "bfdfe7dc352907fc980b868725387e986d5e368a2966e687c64f43486e1a14ce", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a88d308606361f31a2662077dbd1e56c", "guid": "bfdfe7dc352907fc980b868725387e9892ad5fea3cd9806e3d88c69c494e5724", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e05646fd82c278770731a22f2bab22b8", "guid": "bfdfe7dc352907fc980b868725387e982287d5443baa60a644e03eff995b0dbf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dad7ebb904dc603e09d7077620272995", "guid": "bfdfe7dc352907fc980b868725387e984b7cfa5cae6af5dd957a2e5e2c72d887", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ca62960e7dfa23066202723c5f9bb6fc", "guid": "bfdfe7dc352907fc980b868725387e981354bfb9c1f8a7cd29cda84a949c2b50", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9899e6d5947023da5bc3e53fc51ec5fae2", "guid": "bfdfe7dc352907fc980b868725387e988ea9c6211d68a85a4fa5661a00b70d12", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98934cef9bf6ee848bfe3ec38d62197835", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98eff9b5eed613c7c51bfc3ecf3b0a9188", "guid": "bfdfe7dc352907fc980b868725387e988601a4ee62b9ae3a9bcf8cd3d0ebe162"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b5b068b529036d672802633cae1aec9b", "guid": "bfdfe7dc352907fc980b868725387e98118fac16cfc7d0d72dbd7690930ee944"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984842c7b53b4d6fa138eef49357b89096", "guid": "bfdfe7dc352907fc980b868725387e988018d42a6cbf30bd26714051d751756f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986ed4c48508c44fa3ce08c3d716f3ad3d", "guid": "bfdfe7dc352907fc980b868725387e9816670788bc7f1e372bfade4c15b03b8d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9815c52a0d1bdca6d127a7ea35197072a5", "guid": "bfdfe7dc352907fc980b868725387e98f8b7fbab77fdbedd35cf16a5f9b711bb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9858378118f4d5681140506a6859e49b27", "guid": "bfdfe7dc352907fc980b868725387e9865826dabae77a65c0f8e3f8837afaccf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d53954c5058bcd0edd6e552c68c4240", "guid": "bfdfe7dc352907fc980b868725387e98b2432585b08dbc72dc97577aadf81be4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9827ae1e83b9d8a11caf1437967c5d02ec", "guid": "bfdfe7dc352907fc980b868725387e98dea84ba5702c5f8810e62a4263d9ace3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9874867a71786fedd2788d926372385066", "guid": "bfdfe7dc352907fc980b868725387e9838e63e5a34be3fcb50907fe5f2f4afed"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9822a1695c80ff8889110aaab4b7fe915e", "guid": "bfdfe7dc352907fc980b868725387e983efddb9b6fee895c6ea68388a011affc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989fa6e0b4cac54e734feb75f1bf7be47c", "guid": "bfdfe7dc352907fc980b868725387e989a76fbeb6bac5b3b1280afb834ae8aa5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986944b59caa1d7e602880124b7ae8108f", "guid": "bfdfe7dc352907fc980b868725387e98ec6c6d0bf1756896b53daee803877721"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c746a115d50e783b6191da632077938a", "guid": "bfdfe7dc352907fc980b868725387e98e74fcc69d8150f502ccf1b3e2fc5485f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981f4e7fe1b3a4d55bdf8971ed81a68728", "guid": "bfdfe7dc352907fc980b868725387e98b821742c1f6f7ea4cd9c0b273279b76f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b44601dd38b5fb4682a9e20f7e163972", "guid": "bfdfe7dc352907fc980b868725387e982dc177db9f7d9ffb96479a0947cb42c0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c1348f54050e97ac08ffb016e0842bce", "guid": "bfdfe7dc352907fc980b868725387e9804e1e82783b73939a38d0ed8e1238d7d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c85cfa9fc12e48529ce8a0027e3640fd", "guid": "bfdfe7dc352907fc980b868725387e9809531441d9135d8d86d4ea73bb600633"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986257f01127b99ae5f340eeda8e6f4e9c", "guid": "bfdfe7dc352907fc980b868725387e986708b4f0dbacc243fb99a7eb06925479"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985b37350003a5024af04bbdc175ef2bab", "guid": "bfdfe7dc352907fc980b868725387e98670d25b97004c7a93f78975edd086e14"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9867dca0e12ed59d722c0970419a5c03bc", "guid": "bfdfe7dc352907fc980b868725387e98f1a030d6e6eb0600473a3e08d860ea34"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cd97e7574385034d5df453e326eac31f", "guid": "bfdfe7dc352907fc980b868725387e98cc7934ae79503605f0492de7b872114f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988475c7b1250e4fe6e7f0ce3f08744dc0", "guid": "bfdfe7dc352907fc980b868725387e98fbf436204971d95f60e7a593d7540c8a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9835014052a2eaa1f2a2318dc09059b44e", "guid": "bfdfe7dc352907fc980b868725387e9898aaa51c1a99ce45e80eb8f93420d71e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ebaec97f7d8570e1beb840aea844e21", "guid": "bfdfe7dc352907fc980b868725387e98d0c2773f09c4cabb2cf0eebceee42fe5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982447542e3d91bdc18f7a9b957c9dda6b", "guid": "bfdfe7dc352907fc980b868725387e98da7585a197ccba31a23ef6e300e35684"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9867cebb14ffb558adcbdb33602d17fa8e", "guid": "bfdfe7dc352907fc980b868725387e9877adb990468abb172a1f126cb52f85d0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981441494b261fb704b8ee57f24d82b0a9", "guid": "bfdfe7dc352907fc980b868725387e98cf068f37d84ba0e155873865791a4c16"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9818b4886b46cbedf95d4828382ca1ee23", "guid": "bfdfe7dc352907fc980b868725387e982b47bac972003ddff7a9354e299d2485"}], "guid": "bfdfe7dc352907fc980b868725387e981f18c3bdd543f0383a05196ffefabc9e", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9824f5d6ec5088eecbb83955c855580732", "guid": "bfdfe7dc352907fc980b868725387e9808188f8afd5c22436d8f270ee53ec170"}], "guid": "bfdfe7dc352907fc980b868725387e98f59cae5e753b0a31876694ce85c47944", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98ec476f90f1f6e616e836f5cbf5a8826f", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e982742fb9f3ddc293e43c65bab714408c6", "name": "Mantle"}, {"guid": "bfdfe7dc352907fc980b868725387e98c46180aea4e87057640961e6db37df0d", "name": "SDWebImage"}, {"guid": "bfdfe7dc352907fc980b868725387e9837027570fe3a09bb9c7d0d2620332306", "name": "SDWebImageWebPCoder"}], "guid": "bfdfe7dc352907fc980b868725387e982ec175b6b4d6149d1cce89f5f0b3694a", "name": "flutter_image_compress_common", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98b928db338a2b0bf59a36f34e391aa676", "name": "flutter_image_compress_common.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}