{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9888282f89e22b7424c6cd70874244c76e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Daleel/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Daleel/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/network_info_plus/network_info_plus-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/network_info_plus/network_info_plus-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/network_info_plus/network_info_plus.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "network_info_plus", "PRODUCT_NAME": "network_info_plus", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e989faba2dd4a70405dc549a3f69166e8fd", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9837d23edf78859e05fcf15579381f5c47", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Daleel/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Daleel/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/network_info_plus/network_info_plus-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/network_info_plus/network_info_plus-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/network_info_plus/network_info_plus.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "network_info_plus", "PRODUCT_NAME": "network_info_plus", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a0c99912ea9dc6b2736cf53f826b9ccf", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9837d23edf78859e05fcf15579381f5c47", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Daleel/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Daleel/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/network_info_plus/network_info_plus-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/network_info_plus/network_info_plus-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/network_info_plus/network_info_plus.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "network_info_plus", "PRODUCT_NAME": "network_info_plus", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98aa920e808268d4339a9b5b89c77713c8", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e981bf85e508566a0600951b4c9c3feb86c", "guid": "bfdfe7dc352907fc980b868725387e984711fb9c5c305459b23bc505b209d519", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9876f96f8ea98b3036fe0a63d681a9c92e", "guid": "bfdfe7dc352907fc980b868725387e98900af028d52e294c813500edee7a6098", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec85f8e0f0b6d79b3442ad7f31d80c87", "guid": "bfdfe7dc352907fc980b868725387e980ae146560ffd9345d6b08e1937ff32fd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c499ee9a3f5590e1123b5bf7d1704653", "guid": "bfdfe7dc352907fc980b868725387e9878316a55d6a001c47d0aa0f3717f8788", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986dfd836f30b4562e3691a3039df08752", "guid": "bfdfe7dc352907fc980b868725387e9833dac0bb902efd450e812bfd79e4dd34", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985b28c1378f3a26262a55a78deda34cf6", "guid": "bfdfe7dc352907fc980b868725387e9834d2d70da987c436584987f8a84b9b9a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eff460a48c30db310b10ccf475790c7f", "guid": "bfdfe7dc352907fc980b868725387e988b386b30b89622beb27e7606cbdb3e7b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a44c7d85b31719dff0c78f80068fac8a", "guid": "bfdfe7dc352907fc980b868725387e98c10066845dc20756c6bf579a83073198", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98b636020d6f2716519963ab7b3238e603", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98585e560095896df59c9676482914733e", "guid": "bfdfe7dc352907fc980b868725387e985c0a240f2535807c5a18e41e40c0989a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d400dc2c94805474e6f9d124a59c6c3e", "guid": "bfdfe7dc352907fc980b868725387e9815c5857ea718b68d3f6e896c5e4d00f1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988b948763277352cbbd390b327e4f8f6d", "guid": "bfdfe7dc352907fc980b868725387e9851cc4099f538353e4118c1426ea26af9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b8107155ea975a36d454d6688ef3a521", "guid": "bfdfe7dc352907fc980b868725387e986f62ddfcc3dfc7be4dba73d6ebc3d00c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c0ae3678da1c05cb090e58f8522aef9f", "guid": "bfdfe7dc352907fc980b868725387e98cbe16678a595b6c17806ef4d2ee933ee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9877885a02b0229ee46daa6c60d7b1a625", "guid": "bfdfe7dc352907fc980b868725387e9805f8261c203570ad4e0e84fae6387933"}], "guid": "bfdfe7dc352907fc980b868725387e988693da0e7f3a03ff570f03851f21ae11", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9824f5d6ec5088eecbb83955c855580732", "guid": "bfdfe7dc352907fc980b868725387e98257e8d35398f9f54fe462a23bdd75ce3"}], "guid": "bfdfe7dc352907fc980b868725387e9835248f20df0a319df78eec18576a59e1", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98c3bbbfef215aac3290faf180641526de", "targetReference": "bfdfe7dc352907fc980b868725387e9847316b05e2c06d48b1a13e18b1e2085b"}], "guid": "bfdfe7dc352907fc980b868725387e98ca26a53555f40ca456d09c50b4114fec", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e9847316b05e2c06d48b1a13e18b1e2085b", "name": "network_info_plus-network_info_plus_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98cbf57ad863a4d83e4bcb3860e61a5af3", "name": "network_info_plus", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ebbb202873b59db98b5eabd407a20857", "name": "network_info_plus.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}