{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98bcf610ad6b8224c000a8b9a320387560", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_image_compress_common", "PRODUCT_NAME": "flutter_image_compress_common", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980fe6008bd0ea7d60a239f5cfb496f9fd", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983a94fdcc2e5253abae0fe6faa49e9fe1", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_image_compress_common", "PRODUCT_NAME": "flutter_image_compress_common", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984ac27f284fbf5fa4242b11e4c252631e", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983a94fdcc2e5253abae0fe6faa49e9fe1", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_image_compress_common", "PRODUCT_NAME": "flutter_image_compress_common", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ec29c327167a123c1352766ac15557e9", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e984d24f2ef7026d2f352fdf2e60ff064cc", "guid": "bfdfe7dc352907fc980b868725387e9840a9caeba178add7aa4dceb4c1bb75fe", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98befd372b339b9f4b962cfa69135e29c5", "guid": "bfdfe7dc352907fc980b868725387e98b4c61cc35fba40b4ce329132c3f61eca", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c2199d4ffa1c6c69702afefc03aefad2", "guid": "bfdfe7dc352907fc980b868725387e9818da142fe82d8a78c7be9db931d371ba", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c0bbdc61136835f60922f4383f67ae5c", "guid": "bfdfe7dc352907fc980b868725387e98df995503faf86a7e536e8fd0813b9b8f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b37843c3f9ddc60da97842ced7f03dfc", "guid": "bfdfe7dc352907fc980b868725387e984b4ae0c89031a24d1d71288e24a797ca", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f15809b9a6a06f61c119bc7924b4e4bb", "guid": "bfdfe7dc352907fc980b868725387e985f28519c628b2857ebb37b7b581178d4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988d56f2d5afcd2aa90e0a1330f889fb97", "guid": "bfdfe7dc352907fc980b868725387e98d488e0a22f4c14aacdf4d40c2c9ddb44", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a049f4207fd99c16e8c192f361e9348", "guid": "bfdfe7dc352907fc980b868725387e98fe7273ad9aa9fdcb548c9c43dc93f8b4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98597cc21da3f1308218c9e8caa1b49ebb", "guid": "bfdfe7dc352907fc980b868725387e98313d9e25b0bf5f3c97ad080d07ef3914", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98138926b2acca638afa4dd376fb14f977", "guid": "bfdfe7dc352907fc980b868725387e989f407ff8592b35f500a6527a20317817", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98127d9710335df1464539353ff4924a49", "guid": "bfdfe7dc352907fc980b868725387e98e16efa990e491cc807ce6fc10625d15b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c2bc0ab812d4a21454807b37b99058e3", "guid": "bfdfe7dc352907fc980b868725387e98105db008f99641fadb0caf5f7506b3c4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982452f639879ace036c0b3bac7db64ebc", "guid": "bfdfe7dc352907fc980b868725387e98a6e4e157926dc87d2e4f2973b18d8bcc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987242976626384e999bb03c629e7011a8", "guid": "bfdfe7dc352907fc980b868725387e98d1ddc3be1007fb9ee70d27ff32e03352", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989495b09de4e3c83255cb383012d369a3", "guid": "bfdfe7dc352907fc980b868725387e980f430574f80b3377bafb28f408603313", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98346ca1a908fde4c892895a423796d2e7", "guid": "bfdfe7dc352907fc980b868725387e9811c36f63ef52dcfc9629b13ac9cd2366", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b23cc0212b643c8d3df8b90a0aff66b4", "guid": "bfdfe7dc352907fc980b868725387e98fdcde1de031b3934e8ad535d6a77715a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a90c188cc01214e521a34f85009edaa", "guid": "bfdfe7dc352907fc980b868725387e9806312812ff309343658d470012c9949e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983645364ac189f5f6d5c0f3a2207e80fd", "guid": "bfdfe7dc352907fc980b868725387e98fd547b1fbc8601ed045148719c9fcb31", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eac20d6735a3acae8209dfd445bc3621", "guid": "bfdfe7dc352907fc980b868725387e98b8107d30d41b046eabedd15b3e204207", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9809c57afcbdacc305f5afd4e793650eeb", "guid": "bfdfe7dc352907fc980b868725387e985911bdce5650368ba78e000854a76edc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982bef656ad61b2d4b6be2579cdeb29d13", "guid": "bfdfe7dc352907fc980b868725387e9851879a7bd58d5db494aaffdae14944fc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e5d21484a3016f929094788d60395e66", "guid": "bfdfe7dc352907fc980b868725387e986d5e368a2966e687c64f43486e1a14ce", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987c90905c32395595423d5e636d7e0f9e", "guid": "bfdfe7dc352907fc980b868725387e9892ad5fea3cd9806e3d88c69c494e5724", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b4f337aeac0cc9acf42f166308546f8a", "guid": "bfdfe7dc352907fc980b868725387e982287d5443baa60a644e03eff995b0dbf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d7f32e119c2bae497efe91266bcd472d", "guid": "bfdfe7dc352907fc980b868725387e984b7cfa5cae6af5dd957a2e5e2c72d887", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dbbca4b67a583d475ac90d94ce99fad5", "guid": "bfdfe7dc352907fc980b868725387e981354bfb9c1f8a7cd29cda84a949c2b50", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9843f41e8a0f9ec702393cd49c89e96303", "guid": "bfdfe7dc352907fc980b868725387e988ea9c6211d68a85a4fa5661a00b70d12", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98934cef9bf6ee848bfe3ec38d62197835", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ba6d302ba5604b08e7043d424631e61c", "guid": "bfdfe7dc352907fc980b868725387e988601a4ee62b9ae3a9bcf8cd3d0ebe162"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cd7391160173bd934afb0088b5c893e8", "guid": "bfdfe7dc352907fc980b868725387e98118fac16cfc7d0d72dbd7690930ee944"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984256b347c52c7c991120802fc7c3e1ce", "guid": "bfdfe7dc352907fc980b868725387e988018d42a6cbf30bd26714051d751756f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985d678a931c275249ab98324b5ee12a62", "guid": "bfdfe7dc352907fc980b868725387e9816670788bc7f1e372bfade4c15b03b8d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986c139a5d29534d9f8d0b0920d3044f7c", "guid": "bfdfe7dc352907fc980b868725387e98f8b7fbab77fdbedd35cf16a5f9b711bb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ee98f1853c5c4243a84cec6b2b7270e1", "guid": "bfdfe7dc352907fc980b868725387e9865826dabae77a65c0f8e3f8837afaccf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e912befb9f751374086f2f7c0bf40546", "guid": "bfdfe7dc352907fc980b868725387e98b2432585b08dbc72dc97577aadf81be4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9875d3b50a453b328128c2173f7a21fc02", "guid": "bfdfe7dc352907fc980b868725387e98dea84ba5702c5f8810e62a4263d9ace3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f54b6522f14bd6ba8c4102d2d3b0bf2", "guid": "bfdfe7dc352907fc980b868725387e9838e63e5a34be3fcb50907fe5f2f4afed"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aeeda524677f5dd24f6c67879db7069f", "guid": "bfdfe7dc352907fc980b868725387e983efddb9b6fee895c6ea68388a011affc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989f0a40f70804eada22c2caf45086622d", "guid": "bfdfe7dc352907fc980b868725387e989a76fbeb6bac5b3b1280afb834ae8aa5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cef184bbdf5a1227098d3b60a13f7d03", "guid": "bfdfe7dc352907fc980b868725387e98ec6c6d0bf1756896b53daee803877721"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984f78eb64738db59fd71af909a913edcd", "guid": "bfdfe7dc352907fc980b868725387e98e74fcc69d8150f502ccf1b3e2fc5485f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983de79029a36e8d377352795b734de14e", "guid": "bfdfe7dc352907fc980b868725387e98b821742c1f6f7ea4cd9c0b273279b76f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9888e8b7c0983572e68a17038a92088d12", "guid": "bfdfe7dc352907fc980b868725387e982dc177db9f7d9ffb96479a0947cb42c0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986b813a05529f73f70def1d655a0b2595", "guid": "bfdfe7dc352907fc980b868725387e9804e1e82783b73939a38d0ed8e1238d7d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98581a0aac1f2d6415e08770290a7fbf61", "guid": "bfdfe7dc352907fc980b868725387e9809531441d9135d8d86d4ea73bb600633"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f3a0ed413b1840c6d5ad8a38f70711bf", "guid": "bfdfe7dc352907fc980b868725387e986708b4f0dbacc243fb99a7eb06925479"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9858bfb0b022d34db73020dfdf3499409e", "guid": "bfdfe7dc352907fc980b868725387e98670d25b97004c7a93f78975edd086e14"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98996ccd483ab8a0d980b20b2dd9c3ffdb", "guid": "bfdfe7dc352907fc980b868725387e98f1a030d6e6eb0600473a3e08d860ea34"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989b4278ac12f9fb141cd8231885f5034a", "guid": "bfdfe7dc352907fc980b868725387e98cc7934ae79503605f0492de7b872114f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988edd4d18b240da0d16cf902756c67b86", "guid": "bfdfe7dc352907fc980b868725387e98fbf436204971d95f60e7a593d7540c8a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9862ac76eaf215266cf67f0d8d1961a329", "guid": "bfdfe7dc352907fc980b868725387e9898aaa51c1a99ce45e80eb8f93420d71e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a49e035628208fa413507df99a40aea", "guid": "bfdfe7dc352907fc980b868725387e98d0c2773f09c4cabb2cf0eebceee42fe5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988b902f555771e64b310a93cfc852ce3b", "guid": "bfdfe7dc352907fc980b868725387e98da7585a197ccba31a23ef6e300e35684"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dc7372a2d17333d4852516db32ce5056", "guid": "bfdfe7dc352907fc980b868725387e9877adb990468abb172a1f126cb52f85d0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ba68f3c6c10bb5c63a21317cd59ff30", "guid": "bfdfe7dc352907fc980b868725387e98cf068f37d84ba0e155873865791a4c16"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988c7565c17140dc7a823cf2a6abb60eb6", "guid": "bfdfe7dc352907fc980b868725387e982b47bac972003ddff7a9354e299d2485"}], "guid": "bfdfe7dc352907fc980b868725387e981f18c3bdd543f0383a05196ffefabc9e", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9824f5d6ec5088eecbb83955c855580732", "guid": "bfdfe7dc352907fc980b868725387e9808188f8afd5c22436d8f270ee53ec170"}], "guid": "bfdfe7dc352907fc980b868725387e98f59cae5e753b0a31876694ce85c47944", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98ec476f90f1f6e616e836f5cbf5a8826f", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e982742fb9f3ddc293e43c65bab714408c6", "name": "Mantle"}, {"guid": "bfdfe7dc352907fc980b868725387e98c46180aea4e87057640961e6db37df0d", "name": "SDWebImage"}, {"guid": "bfdfe7dc352907fc980b868725387e9837027570fe3a09bb9c7d0d2620332306", "name": "SDWebImageWebPCoder"}], "guid": "bfdfe7dc352907fc980b868725387e982ec175b6b4d6149d1cce89f5f0b3694a", "name": "flutter_image_compress_common", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98b928db338a2b0bf59a36f34e391aa676", "name": "flutter_image_compress_common.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}