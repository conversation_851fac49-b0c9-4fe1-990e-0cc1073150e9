// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a en locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'en';

  static String m0(count) => "View ${count} replies";

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
    "AddServiceSupplier": MessageLookupByLibrary.simpleMessage(
      "Add Service Supplier",
    ),
    "CleanlinessOfClinic": MessageLookupByLibrary.simpleMessage(
      "Cleanliness of the Clinic",
    ),
    "DateOfRecord": MessageLookupByLibrary.simpleMessage("Data"),
    "DoctorService": MessageLookupByLibrary.simpleMessage("Doctor\'s Service"),
    "HomePageSearchText": MessageLookupByLibrary.simpleMessage("Search"),
    "HomePageSearchText2": MessageLookupByLibrary.simpleMessage(
      "Search For Your Doctor",
    ),
    "NameOfRecord": MessageLookupByLibrary.simpleMessage("Name Of Record"),
    "Orloginasadoctor": MessageLookupByLibrary.simpleMessage(
      "Or login as a doctor",
    ),
    "PASSWORD_MIN_LENGTH": MessageLookupByLibrary.simpleMessage(
      "Password must be at least 6 characters long",
    ),
    "ServiceImage": MessageLookupByLibrary.simpleMessage("Add Service Image"),
    "ServiceLocation": MessageLookupByLibrary.simpleMessage("Service location"),
    "ServiceName": MessageLookupByLibrary.simpleMessage("Service Name"),
    "ServicePhone": MessageLookupByLibrary.simpleMessage("Service Phone"),
    "ShareImagesPet": MessageLookupByLibrary.simpleMessage("Share Images Pet"),
    "TimeOfRecord": MessageLookupByLibrary.simpleMessage("Time"),
    "VerifyPhone": MessageLookupByLibrary.simpleMessage(" Verify Phone"),
    "about": MessageLookupByLibrary.simpleMessage("About"),
    "accept": MessageLookupByLibrary.simpleMessage("Accept"),
    "addAppointment": MessageLookupByLibrary.simpleMessage("Add Appointment"),
    "addAvailabilities": MessageLookupByLibrary.simpleMessage(
      "Add available times",
    ),
    "addComment": MessageLookupByLibrary.simpleMessage(
      "Add  Your Comment . . . .",
    ),
    "addPet": MessageLookupByLibrary.simpleMessage("Add Pet"),
    "addPetChoose": MessageLookupByLibrary.simpleMessage("Choose.."),
    "addPetService": MessageLookupByLibrary.simpleMessage("Service"),
    "addRecord": MessageLookupByLibrary.simpleMessage("Add Reminder"),
    "addReplayComment": MessageLookupByLibrary.simpleMessage(
      "Add  Reply  Comment . . . .",
    ),
    "addToSqueak": MessageLookupByLibrary.simpleMessage("Add to squeak"),
    "addYourFirstPet": MessageLookupByLibrary.simpleMessage(
      "Add Your First Pet",
    ),
    "address": MessageLookupByLibrary.simpleMessage("Address"),
    "addressCity": MessageLookupByLibrary.simpleMessage("Location"),
    "address_hint": MessageLookupByLibrary.simpleMessage("Enter your address"),
    "address_validation": MessageLookupByLibrary.simpleMessage(
      "Please enter your address",
    ),
    "alreadyHaveAccount": MessageLookupByLibrary.simpleMessage(
      "Already have account?",
    ),
    "appointmentButtonBooking": MessageLookupByLibrary.simpleMessage(
      "Book again",
    ),
    "appointmentButtonCall": MessageLookupByLibrary.simpleMessage("Call"),
    "appointmentButtonCancel": MessageLookupByLibrary.simpleMessage("Cancel"),
    "appointmentButtonEdit": MessageLookupByLibrary.simpleMessage("Edit"),
    "appointmentCanceled": MessageLookupByLibrary.simpleMessage("Canceled"),
    "appointmentDone": MessageLookupByLibrary.simpleMessage("Done"),
    "appointmentExamination": MessageLookupByLibrary.simpleMessage(
      "clinic exam",
    ),
    "appointmentModalButtonNo": MessageLookupByLibrary.simpleMessage("No"),
    "appointmentModalButtonYes": MessageLookupByLibrary.simpleMessage("Yes"),
    "appointmentModalDescription": MessageLookupByLibrary.simpleMessage(
      "Are you sure you want to cancel Appointment to",
    ),
    "appointmentModalTitle": MessageLookupByLibrary.simpleMessage(
      "Cancel Confirmation",
    ),
    "appointmentReserved": MessageLookupByLibrary.simpleMessage("Reserved"),
    "appointmentStatus": MessageLookupByLibrary.simpleMessage("Done"),
    "appointmentsAll": MessageLookupByLibrary.simpleMessage("All"),
    "appointmentsAttended": MessageLookupByLibrary.simpleMessage("Attended"),
    "appointmentsCanceled": MessageLookupByLibrary.simpleMessage("Canceled"),
    "appointmentsEndExamination": MessageLookupByLibrary.simpleMessage(
      "End Examination",
    ),
    "appointmentsFinished": MessageLookupByLibrary.simpleMessage("Finished"),
    "appointmentsReserved": MessageLookupByLibrary.simpleMessage("Reserved"),
    "appointmentsStartExamination": MessageLookupByLibrary.simpleMessage(
      "Start Examination",
    ),
    "arabic": MessageLookupByLibrary.simpleMessage("Arabic"),
    "bill": MessageLookupByLibrary.simpleMessage("Bill"),
    "birthdate": MessageLookupByLibrary.simpleMessage("Date of birth"),
    "birthdateValidation": MessageLookupByLibrary.simpleMessage(
      "Please enter date of birth",
    ),
    "birthdate_hint": MessageLookupByLibrary.simpleMessage(
      "Enter your birthdate",
    ),
    "birthdate_validation": MessageLookupByLibrary.simpleMessage(
      "Please enter your birthdate",
    ),
    "boardingBy": MessageLookupByLibrary.simpleMessage("Boarding By"),
    "boardingCage": MessageLookupByLibrary.simpleMessage("Cage"),
    "boardingPrice": MessageLookupByLibrary.simpleMessage("Initial cost"),
    "boardingType": MessageLookupByLibrary.simpleMessage("Boarding Type"),
    "boardingTypeNoteDay": MessageLookupByLibrary.simpleMessage(
      "Note that you select boarding price in days",
    ),
    "boardingTypeNoteHour": MessageLookupByLibrary.simpleMessage(
      "Note that you select boarding price in hours",
    ),
    "booking": MessageLookupByLibrary.simpleMessage("Booking"),
    "breed": MessageLookupByLibrary.simpleMessage("breed"),
    "cageSelect": MessageLookupByLibrary.simpleMessage("Select Cage"),
    "cancel": MessageLookupByLibrary.simpleMessage("Cancel"),
    "cancelFollow": MessageLookupByLibrary.simpleMessage("Cancel"),
    "changePassword": MessageLookupByLibrary.simpleMessage("Change Password"),
    "changePhoto": MessageLookupByLibrary.simpleMessage("Change Photo"),
    "checkYourEmail": MessageLookupByLibrary.simpleMessage("Check Your Email"),
    "chooseViewMode": MessageLookupByLibrary.simpleMessage("Choose view mode"),
    "chosePet": MessageLookupByLibrary.simpleMessage("Choose Pet"),
    "city": MessageLookupByLibrary.simpleMessage("city"),
    "clinic": MessageLookupByLibrary.simpleMessage("Clinic"),
    "clinicDoctor": MessageLookupByLibrary.simpleMessage("Clinic Doctors"),
    "clinicFollowedBefore": MessageLookupByLibrary.simpleMessage(
      "You have already followed this clinic before",
    ),
    "closeIn": MessageLookupByLibrary.simpleMessage("Close In"),
    "commentReply": MessageLookupByLibrary.simpleMessage("Comment Reply"),
    "comments": MessageLookupByLibrary.simpleMessage("Comments"),
    "comparePassword": MessageLookupByLibrary.simpleMessage(
      "Enter confirm your password",
    ),
    "compeleteSqueakRegister": MessageLookupByLibrary.simpleMessage(
      "Complete the register from vetIcare invitation",
    ),
    "compliance": MessageLookupByLibrary.simpleMessage(
      "Compliance with Data Protection Regulations",
    ),
    "complianceDesc": MessageLookupByLibrary.simpleMessage(
      "We comply with relevant data protection laws and regulations.",
    ),
    "confirmNewPassword": MessageLookupByLibrary.simpleMessage(
      "Confirm New Password",
    ),
    "confirmPassword": MessageLookupByLibrary.simpleMessage("Confirm Password"),
    "contactPrivacy": MessageLookupByLibrary.simpleMessage(
      "Contact Us About Privacy",
    ),
    "contactPrivacyDesc": MessageLookupByLibrary.simpleMessage(
      "If you have any concerns or questions about how we handle your data or if you would like to exercise your data rights, please contact our privacy <NAME_EMAIL>. We are here to ensure that you feel confident and secure in using our services.",
    ),
    "contactProblem": MessageLookupByLibrary.simpleMessage(
      "Contact us in case of any problem",
    ),
    "contactUs": MessageLookupByLibrary.simpleMessage("Contact Us"),
    "contactUsDesc": MessageLookupByLibrary.simpleMessage(
      "If you have any questions about this privacy policy, please contact <NAME_EMAIL>.",
    ),
    "copyright": MessageLookupByLibrary.simpleMessage(
      "© 2023 Quad Insight. All rights reserved",
    ),
    "copyright2": MessageLookupByLibrary.simpleMessage(
      "© 2024 VetICare. All rights reserved.",
    ),
    "couldNotOpenLink": MessageLookupByLibrary.simpleMessage(
      "Could not open link",
    ),
    "crNumber": MessageLookupByLibrary.simpleMessage("CR"),
    "createPost": MessageLookupByLibrary.simpleMessage("Create Post "),
    "darkMode": MessageLookupByLibrary.simpleMessage("Dark Mode"),
    "dataEncryption": MessageLookupByLibrary.simpleMessage("Data Encryption"),
    "dataEncryptionDesc": MessageLookupByLibrary.simpleMessage(
      "All data transmitted between our application and our servers is encrypted using secure protocols (such as HTTPS). Additionally, sensitive information, including your clinic\'s data and payment information, is encrypted and securely stored to prevent unauthorized access.",
    ),
    "dataRetention": MessageLookupByLibrary.simpleMessage(
      "Data Retention Policy",
    ),
    "dataRetentionDesc": MessageLookupByLibrary.simpleMessage(
      "We retain your data only for as long as necessary to provide you with our services and for legitimate business purposes, such as maintaining records for financial, legal, or compliance reasons. Once the data is no longer needed, we take steps to securely delete or anonymize it.",
    ),
    "dataSecurity": MessageLookupByLibrary.simpleMessage(
      "Data Security and Privacy",
    ),
    "dataSecurityDesc": MessageLookupByLibrary.simpleMessage(
      "Your privacy and the security of your clinic\'s data are of utmost importance to us. We implement industry-standard security measures to protect your data against unauthorized access, alteration, and disclosure. Our systems are regularly updated to address potential vulnerabilities and ensure the highest level of protection.",
    ),
    "date": MessageLookupByLibrary.simpleMessage("Date"),
    "delete": MessageLookupByLibrary.simpleMessage("Delete"),
    "deleteComment": MessageLookupByLibrary.simpleMessage("Delete Comment"),
    "deleteConfirmation": MessageLookupByLibrary.simpleMessage(
      "Delete Confirmation",
    ),
    "deletePhoto": MessageLookupByLibrary.simpleMessage("Delete Photo"),
    "doctorName": MessageLookupByLibrary.simpleMessage("Doctor Name"),
    "edit": MessageLookupByLibrary.simpleMessage("Edit"),
    "editComment": MessageLookupByLibrary.simpleMessage("Edit Comment . . . ."),
    "editCommentPost": MessageLookupByLibrary.simpleMessage("Edit Comment"),
    "editPet": MessageLookupByLibrary.simpleMessage("Edit Pet"),
    "editReplyCommentPost": MessageLookupByLibrary.simpleMessage(
      "Edit Reply Comment",
    ),
    "email": MessageLookupByLibrary.simpleMessage("Email Address"),
    "emailCopied": MessageLookupByLibrary.simpleMessage(
      "Email copied to clipboard",
    ),
    "email_hint": MessageLookupByLibrary.simpleMessage(
      "Enter your email address",
    ),
    "email_valid": MessageLookupByLibrary.simpleMessage(
      "Please enter email address",
    ),
    "email_validation": MessageLookupByLibrary.simpleMessage(
      "Please enter a valid email address",
    ),
    "english": MessageLookupByLibrary.simpleMessage("English"),
    "enterAValidEm": MessageLookupByLibrary.simpleMessage(
      "Enter a valid email",
    ),
    "enterCharNamePls": MessageLookupByLibrary.simpleMessage(
      "name more than 6 charts ",
    ),
    "enterCharPassword": MessageLookupByLibrary.simpleMessage(
      "Password more than 6 charts",
    ),
    "enterCode": MessageLookupByLibrary.simpleMessage("Enter code your sent"),
    "enterConfirmNewPass": MessageLookupByLibrary.simpleMessage(
      "Enter your confirm new password",
    ),
    "enterName": MessageLookupByLibrary.simpleMessage("Enter your Full Name"),
    "enterNewPassword": MessageLookupByLibrary.simpleMessage(
      "Enter your new password",
    ),
    "enterPetName": MessageLookupByLibrary.simpleMessage("Enter pet name"),
    "enterPetNameValidation": MessageLookupByLibrary.simpleMessage(
      "Please enter pet name",
    ),
    "enterPhone": MessageLookupByLibrary.simpleMessage("Enter your phone"),
    "enterUrAddress": MessageLookupByLibrary.simpleMessage(
      "Enter your address",
    ),
    "enterUrEmail": MessageLookupByLibrary.simpleMessage(
      "Enter your email address",
    ),
    "enterUrEmailOPhone": MessageLookupByLibrary.simpleMessage(
      "Please Enter your email or phone number",
    ),
    "enterUrEmailOPhoneValid": MessageLookupByLibrary.simpleMessage(
      "Please Enter a valid email or phone number",
    ),
    "enterUrEmailORPhone": MessageLookupByLibrary.simpleMessage(
      "Enter your email or phone number",
    ),
    "enterUrPassword": MessageLookupByLibrary.simpleMessage(
      "Enter your password",
    ),
    "enterYourText": MessageLookupByLibrary.simpleMessage(
      "What are you thinking .... ?",
    ),
    "entryDate": MessageLookupByLibrary.simpleMessage("Entry Date"),
    "exitDate": MessageLookupByLibrary.simpleMessage("Exit Date"),
    "expiredRequest": MessageLookupByLibrary.simpleMessage(
      "The follow request you\\\'re looking for is no longer available. \'\n                      \'It may have been cancelled, expired, or already accepted.",
    ),
    "failedToLoadVersion": MessageLookupByLibrary.simpleMessage(
      "Failed to load version",
    ),
    "female": MessageLookupByLibrary.simpleMessage("Female"),
    "files": MessageLookupByLibrary.simpleMessage("Files"),
    "filesAndPrescription": MessageLookupByLibrary.simpleMessage(
      "Files And Prescription",
    ),
    "filter_hint_State": MessageLookupByLibrary.simpleMessage(
      "Filter by status",
    ),
    "filter_hint_pets": MessageLookupByLibrary.simpleMessage("Filter by pet"),
    "findPotentialMates": MessageLookupByLibrary.simpleMessage(
      "Search for pets...",
    ),
    "followCode": MessageLookupByLibrary.simpleMessage("Follow Code"),
    "followConfirmation": MessageLookupByLibrary.simpleMessage(
      "Follow Confirmation",
    ),
    "followRequest": MessageLookupByLibrary.simpleMessage("Follow Request"),
    "followRequestUnAvailable": MessageLookupByLibrary.simpleMessage(
      "Follow Request Unavailable",
    ),
    "forgotPass": MessageLookupByLibrary.simpleMessage("Forgot password?"),
    "fullName": MessageLookupByLibrary.simpleMessage("Full Name"),
    "gaber": MessageLookupByLibrary.simpleMessage("testEN"),
    "gender": MessageLookupByLibrary.simpleMessage("gender"),
    "generalInformation": MessageLookupByLibrary.simpleMessage(
      "General Information",
    ),
    "goToClinicsFromPetsScreen": MessageLookupByLibrary.simpleMessage(
      "Go to clinics",
    ),
    "haveNotAccount": MessageLookupByLibrary.simpleMessage(
      "Don\'t have an account?",
    ),
    "help": MessageLookupByLibrary.simpleMessage("Support"),
    "helpShare": MessageLookupByLibrary.simpleMessage(
      "Help us to share the app",
    ),
    "howWeUse": MessageLookupByLibrary.simpleMessage(
      "How We Use Your Information",
    ),
    "howWeUseDesc": MessageLookupByLibrary.simpleMessage(
      "Your information is used to provide and improve our services.",
    ),
    "ignore": MessageLookupByLibrary.simpleMessage("Ignore"),
    "infoProtection": MessageLookupByLibrary.simpleMessage(
      "Information Protection",
    ),
    "infoProtectionDesc": MessageLookupByLibrary.simpleMessage(
      "We take data security seriously and implement appropriate measures to safeguard your personal information against unauthorized access, alteration, or disclosure.",
    ),
    "infoWeCollect": MessageLookupByLibrary.simpleMessage(
      "Information We Collect",
    ),
    "infoWeCollectDesc": MessageLookupByLibrary.simpleMessage(
      "We may collect the following types of information:",
    ),
    "inviteFriends": MessageLookupByLibrary.simpleMessage("Invite friends"),
    "invoiceNo": MessageLookupByLibrary.simpleMessage("Invoice No"),
    "itemName": MessageLookupByLibrary.simpleMessage("Item Name"),
    "labelPost": MessageLookupByLibrary.simpleMessage(
      "What\\\'s on your mind ?",
    ),
    "langMode": MessageLookupByLibrary.simpleMessage("language Mode"),
    "language": MessageLookupByLibrary.simpleMessage("Language"),
    "lastUpdated": MessageLookupByLibrary.simpleMessage("Last updated"),
    "linkAnotherPet": MessageLookupByLibrary.simpleMessage("Link another pet"),
    "location": MessageLookupByLibrary.simpleMessage("location"),
    "login": MessageLookupByLibrary.simpleMessage("Login"),
    "logout": MessageLookupByLibrary.simpleMessage("LogOut"),
    "logoutConfirm": MessageLookupByLibrary.simpleMessage(
      "Do you want to log out?",
    ),
    "male": MessageLookupByLibrary.simpleMessage("Male"),
    "message_hint": MessageLookupByLibrary.simpleMessage("Enter your message"),
    "message_validation": MessageLookupByLibrary.simpleMessage(
      "Please enter a message",
    ),
    "myFavorites": MessageLookupByLibrary.simpleMessage("My favourites"),
    "myPets": MessageLookupByLibrary.simpleMessage("My pets"),
    "name": MessageLookupByLibrary.simpleMessage("Name"),
    "name_hint": MessageLookupByLibrary.simpleMessage("Enter your full name"),
    "name_validation": MessageLookupByLibrary.simpleMessage(
      "Please enter your name",
    ),
    "newPassword": MessageLookupByLibrary.simpleMessage("New Password"),
    "no": MessageLookupByLibrary.simpleMessage("No"),
    "noAppointment": MessageLookupByLibrary.simpleMessage("No Appointments"),
    "noPetsFound": MessageLookupByLibrary.simpleMessage("No Pets Found"),
    "noPetsInVetICare": MessageLookupByLibrary.simpleMessage(
      "No pets are available in this clinic, or they have already been added to SQueak",
    ),
    "notEqualPassword": MessageLookupByLibrary.simpleMessage(
      "confirm password not equal password",
    ),
    "notSpayed": MessageLookupByLibrary.simpleMessage("Unspayed"),
    "notifications": MessageLookupByLibrary.simpleMessage("Notifications"),
    "notificationsAlert": MessageLookupByLibrary.simpleMessage(
      "Notifications Alert",
    ),
    "offlineMessagesContent": MessageLookupByLibrary.simpleMessage(
      " check your internet connection and try again",
    ),
    "offlineMessagesTitle": MessageLookupByLibrary.simpleMessage(
      "No Internet Connection",
    ),
    "openAt": MessageLookupByLibrary.simpleMessage("Open At"),
    "other": MessageLookupByLibrary.simpleMessage("Other"),
    "other_valid": MessageLookupByLibrary.simpleMessage(
      "Please enter valid title",
    ),
    "other_valid_more_than_30_char": MessageLookupByLibrary.simpleMessage(
      "the range is from 1 - 30 char",
    ),
    "ownerDetails": MessageLookupByLibrary.simpleMessage("Owner Details"),
    "paid": MessageLookupByLibrary.simpleMessage("Paid"),
    "password": MessageLookupByLibrary.simpleMessage("Password"),
    "password_hint": MessageLookupByLibrary.simpleMessage(
      "Enter your password",
    ),
    "password_validation": MessageLookupByLibrary.simpleMessage(
      "Please enter a password",
    ),
    "payment": MessageLookupByLibrary.simpleMessage("Payment"),
    "paymentType": MessageLookupByLibrary.simpleMessage("Type"),
    "personalInfo": MessageLookupByLibrary.simpleMessage(
      "Personal identification information (Name, email address, phone number, etc.)",
    ),
    "personalization": MessageLookupByLibrary.simpleMessage("Personalization"),
    "pet": MessageLookupByLibrary.simpleMessage("Pet"),
    "petDeletedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Pet Deleted Successfully",
    ),
    "petDetails": MessageLookupByLibrary.simpleMessage("Pet Details"),
    "petName": MessageLookupByLibrary.simpleMessage("Pet Name"),
    "pet_birthdate_hint": MessageLookupByLibrary.simpleMessage(
      "Enter your pet\'s birthdate",
    ),
    "pet_birthdate_validation": MessageLookupByLibrary.simpleMessage(
      "Please enter your pet\'s birthdate",
    ),
    "petsVetICare": MessageLookupByLibrary.simpleMessage(
      "Pets in vetIcare clinic",
    ),
    "phone": MessageLookupByLibrary.simpleMessage("Phone"),
    "phone_hint": MessageLookupByLibrary.simpleMessage(
      "Enter your phone number",
    ),
    "phone_or_password_hint": MessageLookupByLibrary.simpleMessage(
      "Enter your phone number or password",
    ),
    "phone_or_password_validation": MessageLookupByLibrary.simpleMessage(
      "Please enter your phone number or password",
    ),
    "phone_validation": MessageLookupByLibrary.simpleMessage(
      "Please enter your phone number",
    ),
    "pleaseSelectBoarding": MessageLookupByLibrary.simpleMessage(
      "Please select boarding Type name",
    ),
    "pleaseSelectCage": MessageLookupByLibrary.simpleMessage(
      "Please select Cage",
    ),
    "prescription": MessageLookupByLibrary.simpleMessage("Prescription"),
    "price": MessageLookupByLibrary.simpleMessage("price"),
    "privacyPolicy": MessageLookupByLibrary.simpleMessage("Privacy Policy"),
    "profile": MessageLookupByLibrary.simpleMessage("Your Profile"),
    "publish": MessageLookupByLibrary.simpleMessage("Publish"),
    "qty": MessageLookupByLibrary.simpleMessage("Qty"),
    "rate": MessageLookupByLibrary.simpleMessage("Rate"),
    "receiveCode": MessageLookupByLibrary.simpleMessage(
      "Enter the received code",
    ),
    "receiveEmail": MessageLookupByLibrary.simpleMessage(
      "Enter your email to\n Receive the instruction to reset your password",
    ),
    "register": MessageLookupByLibrary.simpleMessage("Register"),
    "reminderOtherHintText": MessageLookupByLibrary.simpleMessage(
      "Please enter your reminder type",
    ),
    "removeClinic": MessageLookupByLibrary.simpleMessage("Remove Clinic"),
    "removePost": MessageLookupByLibrary.simpleMessage("Remove post"),
    "replies": MessageLookupByLibrary.simpleMessage("replies"),
    "reply": MessageLookupByLibrary.simpleMessage("Reply"),
    "returnPolicy": MessageLookupByLibrary.simpleMessage(
      "Return & Exchange Policy: Items can be returned within 3 days of purchase with receipt. Medical products are non-refundable.",
    ),
    "reviewDescription": MessageLookupByLibrary.simpleMessage(
      "Help us to provide the best care for your beloved pets.",
    ),
    "reviewMessage": MessageLookupByLibrary.simpleMessage(
      "Your review has been submitted successfully.",
    ),
    "reviewMessage2": MessageLookupByLibrary.simpleMessage(
      "You can view your review by Appointment Screen.",
    ),
    "reviewTitle": MessageLookupByLibrary.simpleMessage("Your Feedback"),
    "save": MessageLookupByLibrary.simpleMessage("Save"),
    "search": MessageLookupByLibrary.simpleMessage("Search"),
    "searchFor": MessageLookupByLibrary.simpleMessage("Search For Service"),
    "searchValid": MessageLookupByLibrary.simpleMessage(" Add Service"),
    "securityAudits": MessageLookupByLibrary.simpleMessage(
      "Regular Security Audits",
    ),
    "securityAuditsDesc": MessageLookupByLibrary.simpleMessage(
      "We conduct regular security audits to identify and address potential security risks. Our team is dedicated to continuously monitoring and enhancing our security measures to ensure the safety and privacy of your clinic\'s data.",
    ),
    "selectBoarding": MessageLookupByLibrary.simpleMessage(
      "Select Boarding Type name",
    ),
    "selectBoardingBy": MessageLookupByLibrary.simpleMessage("Select Doctor"),
    "selectPet": MessageLookupByLibrary.simpleMessage(
      "Please select a pet to start service",
    ),
    "selectPetType": MessageLookupByLibrary.simpleMessage("Select Pet Type"),
    "send": MessageLookupByLibrary.simpleMessage("Send"),
    "sendMeNow": MessageLookupByLibrary.simpleMessage("Send me now"),
    "sentVerification": MessageLookupByLibrary.simpleMessage(
      "We have sent a verification code to",
    ),
    "service_hint": MessageLookupByLibrary.simpleMessage("Select a service"),
    "service_validation": MessageLookupByLibrary.simpleMessage(
      "Please select a service",
    ),
    "setLanguage": MessageLookupByLibrary.simpleMessage("Set the app language"),
    "settings": MessageLookupByLibrary.simpleMessage("Settings"),
    "sex": MessageLookupByLibrary.simpleMessage("Sex"),
    "shareApp": MessageLookupByLibrary.simpleMessage("Share App"),
    "sharingInfo": MessageLookupByLibrary.simpleMessage(
      "Sharing Your Information",
    ),
    "sharingInfoDesc": MessageLookupByLibrary.simpleMessage(
      "We do not sell or trade your personal information. However, we may share certain information with trusted third-party service providers to support and improve our services. These providers are required to adhere to strict data protection standards and are only permitted to use the information for the purposes outlined in our privacy policy.",
    ),
    "signOut": MessageLookupByLibrary.simpleMessage("Sign Out"),
    "signUp": MessageLookupByLibrary.simpleMessage("Sign Up"),
    "signUpAsADoctor": MessageLookupByLibrary.simpleMessage(
      "Sign up as a Doctor",
    ),
    "skip": MessageLookupByLibrary.simpleMessage("Skip"),
    "spayed": MessageLookupByLibrary.simpleMessage("Spayed"),
    "speciality": MessageLookupByLibrary.simpleMessage("Speciality"),
    "species": MessageLookupByLibrary.simpleMessage("Species"),
    "speciesOne": MessageLookupByLibrary.simpleMessage("Species"),
    "startAppointment": MessageLookupByLibrary.simpleMessage(
      "Start Appointment",
    ),
    "submit": MessageLookupByLibrary.simpleMessage("Submit"),
    "swapPet": MessageLookupByLibrary.simpleMessage(
      "Swipe left or right to select a pet",
    ),
    "taxId": MessageLookupByLibrary.simpleMessage("Tax ID"),
    "terms": MessageLookupByLibrary.simpleMessage(
      "By signing up you agree to our Terms of use and Privacy Policy",
    ),
    "time": MessageLookupByLibrary.simpleMessage("Time"),
    "title_hint": MessageLookupByLibrary.simpleMessage("Enter the title"),
    "title_validation": MessageLookupByLibrary.simpleMessage(
      "Please enter a title",
    ),
    "total": MessageLookupByLibrary.simpleMessage("Total"),
    "totalAmount": MessageLookupByLibrary.simpleMessage("Total Invoice"),
    "trademarkInfo": MessageLookupByLibrary.simpleMessage(
      "Trademark Information",
    ),
    "unfollow": MessageLookupByLibrary.simpleMessage("Unfollow"),
    "unfollowConfirmation": MessageLookupByLibrary.simpleMessage(
      "Unfollow Confirmation",
    ),
    "uniqueCode": MessageLookupByLibrary.simpleMessage("Unique Code"),
    "updateAppointment": MessageLookupByLibrary.simpleMessage(
      "Update Appointment",
    ),
    "updateNotification": MessageLookupByLibrary.simpleMessage(
      "Update Notification",
    ),
    "updateNotificationDesc": MessageLookupByLibrary.simpleMessage(
      "We may update our privacy policy from time to time. When we do, we will notify you by posting the new policy on this page. It is your responsibility to review this privacy policy periodically for any changes.",
    ),
    "updateProfile": MessageLookupByLibrary.simpleMessage("Update Profile"),
    "updateSuccess": MessageLookupByLibrary.simpleMessage(
      "Update successfully",
    ),
    "updateVersionModuleButtonIgnore": MessageLookupByLibrary.simpleMessage(
      "IGNORE",
    ),
    "updateVersionModuleButtonLater": MessageLookupByLibrary.simpleMessage(
      "LATER",
    ),
    "updateVersionModuleButtonUpdateNow": MessageLookupByLibrary.simpleMessage(
      "UPDATE NOW",
    ),
    "updateVersionModuleContent": MessageLookupByLibrary.simpleMessage(
      "Your app needs to be updated\n",
    ),
    "updateVersionModuleContent2": MessageLookupByLibrary.simpleMessage(
      "This version of the application is outdated.\nPlease go to the store to update",
    ),
    "updateVersionModuleTitle": MessageLookupByLibrary.simpleMessage(
      "Update App ?",
    ),
    "userControl": MessageLookupByLibrary.simpleMessage(
      "User Control Over Data",
    ),
    "userControlDesc": MessageLookupByLibrary.simpleMessage(
      "You have full control over the data you share with us. You can update, modify, or delete your personal and clinic information at any time through our platform. We provide clear settings and options for managing your data to ensure you are always in control of what information is stored and shared.",
    ),
    "value": MessageLookupByLibrary.simpleMessage("Value"),
    "vat": MessageLookupByLibrary.simpleMessage("vat"),
    "verification": MessageLookupByLibrary.simpleMessage("Verification"),
    "version": MessageLookupByLibrary.simpleMessage("Version"),
    "versionNumber": MessageLookupByLibrary.simpleMessage("Version"),
    "view1Reply": MessageLookupByLibrary.simpleMessage("View 1 reply"),
    "viewReplies": m0,
    "yes": MessageLookupByLibrary.simpleMessage("Yes"),
    "yourAppointments": MessageLookupByLibrary.simpleMessage("Appointments"),
    "yourClinic": MessageLookupByLibrary.simpleMessage("Clinics"),
    "yourConsent": MessageLookupByLibrary.simpleMessage("Your Consent"),
    "yourConsentDesc": MessageLookupByLibrary.simpleMessage(
      "By using our app, you consent to our privacy policy.",
    ),
    "yourPets": MessageLookupByLibrary.simpleMessage("Your SQueak Pet"),
    "yourProfile": MessageLookupByLibrary.simpleMessage("Your profile"),
    "yourUpcomingAppointments": MessageLookupByLibrary.simpleMessage(
      "Your Upcoming Appointments",
    ),
  };
}
