{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981587e1f75d3bd5b2ac803182fd3a4da1", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Daleel/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Daleel/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/video_player_avfoundation/video_player_avfoundation-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "video_player_avfoundation", "PRODUCT_NAME": "video_player_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98398bac938fb47b84c16f19633c15c92f", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98862fdd069cb245182a4577a512605515", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Daleel/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Daleel/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/video_player_avfoundation/video_player_avfoundation-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "video_player_avfoundation", "PRODUCT_NAME": "video_player_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9839d79a52f24f54321de12379511903ab", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98862fdd069cb245182a4577a512605515", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Daleel/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Daleel/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/video_player_avfoundation/video_player_avfoundation-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "video_player_avfoundation", "PRODUCT_NAME": "video_player_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9894fef8acb7de742f96aaab49fd64da62", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b87fcd57ea002fb9dc811f272a13d640", "guid": "bfdfe7dc352907fc980b868725387e982ef6da7a3a60632d8f8022694bd66dd7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98abe047c1c19b1b68cf77aaa8513fdbf3", "guid": "bfdfe7dc352907fc980b868725387e98d5b97975f2350a08cf53ffeffcac67ae", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987557fc08d7dbcee22c4270adff901fd0", "guid": "bfdfe7dc352907fc980b868725387e982a831a1daaca5b26d69021efadf726c9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989354d3dee69d9f413a527c58f7e2f0ed", "guid": "bfdfe7dc352907fc980b868725387e9893021a663ec0153aef3852a58d009c9a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aaa8008acbc1e1aab8437b738a76a3ce", "guid": "bfdfe7dc352907fc980b868725387e986796d36a2bdd3edb6b59e7129fd5a2bd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec9553ba3b1d40847a548c5e0c19af49", "guid": "bfdfe7dc352907fc980b868725387e982e3cc8a1179bcd8b86b68167afee1e90", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98734a39b06c66145c908034c896caadef", "guid": "bfdfe7dc352907fc980b868725387e98c0be046fcf8cb428c9d30ae21d942726", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980caaeca774ce7fa657611be183376702", "guid": "bfdfe7dc352907fc980b868725387e9823853b970b232b2ee98e8e4fc95cac83", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eb11c7b514e580abf13fd9bc73c56082", "guid": "bfdfe7dc352907fc980b868725387e98f79b7fe35423f642e91efea7931e4532", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf74b1dfd3fb3c353e475879f89ee115", "guid": "bfdfe7dc352907fc980b868725387e984f6b3b7d4f466465da9e74bca70c619a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988b1044a3471657089b4083ab094e0dac", "guid": "bfdfe7dc352907fc980b868725387e98a21439c050c6574e9eda2a7c51d789c8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a2d69359f3f6904ab41dfa77ce77f39e", "guid": "bfdfe7dc352907fc980b868725387e987ef5d73bb1bcb9d251b3abc88056c8c2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bfdfd76de3574074d9d8d8127107373c", "guid": "bfdfe7dc352907fc980b868725387e9834bceb04a1a4262410f8691a86e50ac0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bc062e1fe8c0275998c55ce0067c81b1", "guid": "bfdfe7dc352907fc980b868725387e9819a72032caea2c8a2a1d4d8a68690ede", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e2d5ba8715cf93d882ff2443131fa6f4", "guid": "bfdfe7dc352907fc980b868725387e989314e081c3f5bfab1279e3c2c1139a24", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e983145ad27ab9a5d703e29269b9b2b583a", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ac2e745f5925d0f4c58ee461f6ee33c2", "guid": "bfdfe7dc352907fc980b868725387e98446126075935aea39a9e1063fe80734a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ceea88169b817a447696fff48cae126", "guid": "bfdfe7dc352907fc980b868725387e9852b28a641c87eaad7787397417bf81c7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983229a8af7e3c57c9b7f1f0b060494863", "guid": "bfdfe7dc352907fc980b868725387e9850004e812d7475a48e09f9d39d1fcd0d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98adc71813f8ae023a645f03112850eda3", "guid": "bfdfe7dc352907fc980b868725387e9849968048093435d2a565774782e9f29d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9868003dafe04ba0bb9839d367c5e02c5d", "guid": "bfdfe7dc352907fc980b868725387e981550500afbebd6a16545417108df5269"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989131d7917620d2ff421b11be5741eb91", "guid": "bfdfe7dc352907fc980b868725387e989a62207c5988db84ddcac0078af53d6b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9819e0784a570abf9a1a48f1a1e9a6dae8", "guid": "bfdfe7dc352907fc980b868725387e98d0e36eae7c250ccd059d9dcc1ce416eb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988fb3f190a964ea31ecdc8997c66ce876", "guid": "bfdfe7dc352907fc980b868725387e98aaf08e1539082f8b542f01b83c7e8d18"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9872069626702f796c89a27ac1334953ad", "guid": "bfdfe7dc352907fc980b868725387e98c04b1033e4e3e2b45fd298d396b5d875"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821006853cd7d5987a53595c599de2584", "guid": "bfdfe7dc352907fc980b868725387e98b2e8dea6cd344be14bd1567b9270e89b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980eed7d1854ad8c0d2cf83ba67ac10e55", "guid": "bfdfe7dc352907fc980b868725387e98a89106363ecf1c82c77a5e97d941ae89"}], "guid": "bfdfe7dc352907fc980b868725387e9884539da3bdf2ed4ad426ca202aaf6bc7", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9824f5d6ec5088eecbb83955c855580732", "guid": "bfdfe7dc352907fc980b868725387e98c1dae7e29df157eddda815ae5462108c"}], "guid": "bfdfe7dc352907fc980b868725387e98119e723bd2257d68f10fcea70feb1342", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e981c10b7a3ff26395a834064a0172d6124", "targetReference": "bfdfe7dc352907fc980b868725387e98e4af9c7b061f7f7eb180b825e303ea04"}], "guid": "bfdfe7dc352907fc980b868725387e98a3435bfc3b1be550d78f0653eca9c848", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98e4af9c7b061f7f7eb180b825e303ea04", "name": "video_player_avfoundation-video_player_avfoundation_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e988a0a5b40b007f81bee1472e4d0fb23da", "name": "video_player_avfoundation", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98b5f237537920ce49888f6f7f73c80a6c", "name": "video_player_avfoundation.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}