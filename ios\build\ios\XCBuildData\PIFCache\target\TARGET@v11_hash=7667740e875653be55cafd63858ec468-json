{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98423ea5c4689251fa5f232d48363b8498", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98df91a7676e4ed0cceb01ce7e0304c9be", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c143945aac93cfc4cb7968819a0d8624", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e154e0d2d5eb904db77518b82ce72bb3", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c143945aac93cfc4cb7968819a0d8624", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e982a6466b28ce34849c1a036d51aab78ea", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98fe76326bb00ef676e16d1f5521c917e5", "guid": "bfdfe7dc352907fc980b868725387e98e9cc876c1c8c70a397236a96499b08b2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982b74ce0f99603f8e7b861c4650204f3b", "guid": "bfdfe7dc352907fc980b868725387e98ba6ccc08a94de977296f54a1ff199647", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98adf23eb7c4961ac12dd517ab90db9308", "guid": "bfdfe7dc352907fc980b868725387e980095a4644d3f581871686844f60d13e3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a98efb1b8d208be261de101c9873574b", "guid": "bfdfe7dc352907fc980b868725387e98c2cc42a384d773d8e1ca55569a3c5a0e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b6a4b319fa473b17b24f37a107ecc124", "guid": "bfdfe7dc352907fc980b868725387e98100ec2a0fbc98483c87f4a917e567d6b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98726298e09f3d22ba718bd4d8e8e04116", "guid": "bfdfe7dc352907fc980b868725387e98f0b63169e1c083d4e6119c7c94d79fd7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9813f2b6d914e2ec3fff43fc8ceb7f517c", "guid": "bfdfe7dc352907fc980b868725387e985e38f02f6f75812b0ea910bd02a1bb85", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fcf18bdb0c52d99707bf7905bb5b7ae4", "guid": "bfdfe7dc352907fc980b868725387e987ccd1b54dfae96a26fa73e290f4dc052", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98878381a00f109e02a47f0e44f478b444", "guid": "bfdfe7dc352907fc980b868725387e9835e0926f37a7bc18e9d5631450a3121f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9863244d584fba12cec36e3295fd524603", "guid": "bfdfe7dc352907fc980b868725387e98d8765f01fdeed4c51274f63b9a7553b8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b73b8e66439105c0f9cf2ff34751d477", "guid": "bfdfe7dc352907fc980b868725387e98d3ba7eac19352add4ca6b4f8aad807f5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9814319a6b64e3ddede2c48ac73a64d58d", "guid": "bfdfe7dc352907fc980b868725387e98f51a14bda77670b71781f3ee393487e9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b0542ab51f213d7c2883dd4cd63c7852", "guid": "bfdfe7dc352907fc980b868725387e989690db971138371abba871dc825c2984", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981df1bd958d1aac02e7a45557177642b1", "guid": "bfdfe7dc352907fc980b868725387e9869872adbedbbd30557f5e6dfd9c52fa5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9886aa1b655e18458a96843fef7c16f44a", "guid": "bfdfe7dc352907fc980b868725387e980161da8c3f8ee6e6db02243eda653de6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9878a290751834e43292b529cca26619d6", "guid": "bfdfe7dc352907fc980b868725387e989eb5684ae44d80182b8c5d8f40207bf2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983c0088508735c94202bb6f23c0f17594", "guid": "bfdfe7dc352907fc980b868725387e9838ae20bc0281d77f32f02ed4c26840cc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985bab02f3ac5118030727d8d6c42ae6fd", "guid": "bfdfe7dc352907fc980b868725387e98cdf6b25afb228cfdbf654373cc311709", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d61632494747644fe074b64223df2936", "guid": "bfdfe7dc352907fc980b868725387e9842ae4dc5c08922783e153f3d228f6da7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988fc55ed752cbb341475a1ea55b8eaa6f", "guid": "bfdfe7dc352907fc980b868725387e98b243c7496c8382be62812cb2439933a6", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e50e042a1ea325241cfd4c9597f9c5a9", "guid": "bfdfe7dc352907fc980b868725387e9842933f27f99c42527a8590c5d8afccaa", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98adb5a37f211d798b18a4649b1b10e787", "guid": "bfdfe7dc352907fc980b868725387e98b04e6175a5b4761049254422e5c88cff", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98e2b9a45f9fd84ec9d889002b581b6784", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e984024d8df05089cb008f1fa8e6f1fff6e", "guid": "bfdfe7dc352907fc980b868725387e988df00a0fec07d6851690765215416f3b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9884e7bc9b2b35880076d394267397ce6a", "guid": "bfdfe7dc352907fc980b868725387e986aa011029b4c7ce34ddeb013593a5daf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987818514db712ec5c20e8b79f4f3c7571", "guid": "bfdfe7dc352907fc980b868725387e98450e901b5e3d6700ae9cb969dbf3cfe3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986ce61404c56b7dfc0e43e0efcb246b78", "guid": "bfdfe7dc352907fc980b868725387e98aafc8a009f28771f44b4edccd8761ef6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980629f053e99baed361c801ec0c6fe4f8", "guid": "bfdfe7dc352907fc980b868725387e9844687443a8cb754badcac3df9219d5dc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ff2f83eadddb91ce088faf340179be98", "guid": "bfdfe7dc352907fc980b868725387e980210a1b1a0b2285c1c57826de4f127ec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a51bc16bd6d373e5915cba1210665fbb", "guid": "bfdfe7dc352907fc980b868725387e98396c5c029b1203bbc32e88a105ea36bb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988dd625e27cfe3b908c394fe07c77f81c", "guid": "bfdfe7dc352907fc980b868725387e98ca5a487a8489ef0620f94e1678a8ec2f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b4e35c9e814133b4000fc7da460404ad", "guid": "bfdfe7dc352907fc980b868725387e98002e83946cd4c8c85c1ceac3d8f899d7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98335df7e649906ba488c383f97abceaaf", "guid": "bfdfe7dc352907fc980b868725387e985454045337a030ee127982aeab9fdf38"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985bb3c7708ceb6d5aa1e847923cfe1247", "guid": "bfdfe7dc352907fc980b868725387e98adec2fc152782cda32116bbf2383b47b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ed259ef8ab46c20c71cde4657d7b97bc", "guid": "bfdfe7dc352907fc980b868725387e98ac57a6187ad42d6794fe46d77016c59e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f7fc15c3f142d90d014ebba5e5cd6b80", "guid": "bfdfe7dc352907fc980b868725387e988a10968aa4f6b462d8ef94190ae95a06"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9802a3b05f1306d4ba429bb6c18c44b06d", "guid": "bfdfe7dc352907fc980b868725387e988987b2dbbc1b62282d0dd333114485fb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9897c8a9bf0087cbb5456120fcfef08604", "guid": "bfdfe7dc352907fc980b868725387e98fe8e174986e1ca0ecc581231d8240b4d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a3e21ede0ce5b8962d475bdabbe95da7", "guid": "bfdfe7dc352907fc980b868725387e98e3d435536380403ff4959c2ba3d42077"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9833feb27f733346650f513ecf47e0e35d", "guid": "bfdfe7dc352907fc980b868725387e98b4ddc758342483abc55c7b0fb755b4f0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d049f14f67d968d0607342dbcecbccf5", "guid": "bfdfe7dc352907fc980b868725387e98372e025ca54350d7682f1988b6fd8293"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ba2b4c1010f99d41d920b3598e3750e", "guid": "bfdfe7dc352907fc980b868725387e985a494e47427a6335313172235c7d0466"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fa463a9781c51ca9494e3b32e12e4ed3", "guid": "bfdfe7dc352907fc980b868725387e9891bf5bf97ed4d92f92aa346ac0c797c3"}], "guid": "bfdfe7dc352907fc980b868725387e982d701d19812a18540a58536ddf73bffc", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9824f5d6ec5088eecbb83955c855580732", "guid": "bfdfe7dc352907fc980b868725387e98a91b25809077cc1543d3877f206d74c2"}], "guid": "bfdfe7dc352907fc980b868725387e983148a586d1a3b7c9efec01a91dc8b417", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e983ad914db25720597d9b54b87d633043a", "targetReference": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823"}], "guid": "bfdfe7dc352907fc980b868725387e98851715dcbc2cc6fc0a844df04a55d968", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823", "name": "PromisesObjC-FBLPromises_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e981c795e45f8d875aac88217c6a2a95faa", "name": "FBLPromises.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}