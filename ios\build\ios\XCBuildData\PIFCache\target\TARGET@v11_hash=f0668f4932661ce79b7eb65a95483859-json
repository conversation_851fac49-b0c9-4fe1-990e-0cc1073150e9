{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98bef24c887c5234514ce25948c8b60f57", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d086683d324533850474c3207b3a25d5", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982daf3f28e8ee6d9599893da3c949c0ef", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ed98bbe4c4240fadc79a3eacc440947d", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982daf3f28e8ee6d9599893da3c949c0ef", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a3273116db655ff56887dc0ca80974f0", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e5025fce17506b5b05dc735d2e129b8d", "guid": "bfdfe7dc352907fc980b868725387e98a27864226e1ce256c523c65afa3aeb33", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e983e998d6eefb4153a6b04937dfdca6a34", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e989241ec21dce7536ad5cb8348307c13e3", "guid": "bfdfe7dc352907fc980b868725387e9890613a4d896287faa6009157eaf28d75"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98845d927fb825f3d72b30bbab3636d639", "guid": "bfdfe7dc352907fc980b868725387e98376beeaa6f7f22b0e0d1129775f03314"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989dc65a603782d8535e18bbb15e3e31cc", "guid": "bfdfe7dc352907fc980b868725387e9863968d5f4deb711da21ba6ea3c4eb6c1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989940bfec0f9a465c31dcf9df873a64f0", "guid": "bfdfe7dc352907fc980b868725387e984766ac48e9a695a941f53ecf8acbffb9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a5189a7454699b6a9a765acf091f730f", "guid": "bfdfe7dc352907fc980b868725387e98991b3b0517bda6d33d1c3f4a39781d0d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cc7966e61dd0b97cc35bf0a54d16c8bf", "guid": "bfdfe7dc352907fc980b868725387e98c2212e07fbf1e513041a813c570e9a55"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984daad2a11e4b6cc3d125c27984c7fd58", "guid": "bfdfe7dc352907fc980b868725387e980b26e83b42c52e334960df83744df58f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bd8ee62f2febbdc64fc4854391964db7", "guid": "bfdfe7dc352907fc980b868725387e983e125e1373ca0677cd5b07c0e4b83b88"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fa1400c92c1a220299668f7009a57729", "guid": "bfdfe7dc352907fc980b868725387e98ad2a48d7f477161771c130e8887699c5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b858c14fb29aad1d417e37252ce64284", "guid": "bfdfe7dc352907fc980b868725387e98b78d395051f8fffac56d96073720decd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f9ce8d25316ec5c8fe140eefa9efc2a3", "guid": "bfdfe7dc352907fc980b868725387e983b5ca19ca3825fedba2b00470a75caa3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b201af19b31d8b42850a3ee7cc4cc044", "guid": "bfdfe7dc352907fc980b868725387e981fb17c33ec932f47fb6ae577b728eef5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987fcaf66b5d66eaeb43bae669a3f02340", "guid": "bfdfe7dc352907fc980b868725387e98bb171002a9b810db60f05ae7e9defbc3"}], "guid": "bfdfe7dc352907fc980b868725387e98137e01be1577b2248948c929c856cb84", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9824f5d6ec5088eecbb83955c855580732", "guid": "bfdfe7dc352907fc980b868725387e9837d0f5dabdfdb30d40b85986d54e7cd5"}], "guid": "bfdfe7dc352907fc980b868725387e987db1a428ad53d1cf48d49d10ab505bfe", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e988b2f67f262fa56c985b9d03232da5e44", "targetReference": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f"}], "guid": "bfdfe7dc352907fc980b868725387e9878f194533bab02f7dcf061f8bb70dd5e", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f", "name": "FirebaseCoreInternal-FirebaseCoreInternal_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e983d86e87924acfad2934921ce7ad9fbea", "name": "FirebaseCoreInternal.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}