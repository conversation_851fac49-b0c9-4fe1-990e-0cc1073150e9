import 'package:flutter/material.dart';

class ColorManager {
  static const Color primaryColor = Color(0xFF0D6EFD);
  static const Color secondColor = Color.fromRGBO(255, 112, 41, 1.0);
  static const Color gray = Color.fromRGBO(242, 242, 242, 1);
  static const Color white = Colors.white;
  // Optional: subtle variations
  static const Color primaryLight = Color(0xFFE3F2FD); // Light background
  static const Color primaryBorder = Color(0xFFBBDEFB);
  static const Color black87 = Colors.black87;
  static const Color black54 = Colors.black54;

  // Scaffold

  static const Color sBaseDarkColor = Color(0xff212121);
  static const Color sBaseColor = Color(0xFF1f2421);
  static const Color sWhite = Color(0xFFFFFFFF);
  static const Color faceBookIndicator = Color(0xff1877F2);
  static const Color sBlack = Color(0xFF2C2C2C);
  static const Color green = Color(0xFF6aa84f);
  static const Color amber = Color(0xFFf1c232);
  static const Color red = Color(0xFFcc0000);
  static const Color lightRed = Color(0xFFEF5859);
  static const Color lightRed2 = Color(0xffe56b6b);

  // Buttons
  static const Color bGreen = Color(0xFF4EBE9F);
  static const Color bBlack = Color(0xFF2C2C2C);
  static const Color bFacebook = Color(0xFF3C589A);
  static const Color bTwitter = Color(0xFF0FA6E7);

  // Generic
  static const Color gGrey = Color(0xFF5A5A5A);
  static const Color grey = Color(0xFFbcbcbc);
  static const Color black_45 = Color(0x73000000);
  static const Color black_87 = Color(0xDD000000);
  static const Color black_54 = Color(0x8A000000);
  static const Color blue = Color(0xFF272D69);
  static const Color shimmerBaseColor = Color(0xffadb5bd);
  static const Color shimmerHighlightColor = Color(0xffced4da);
  static const Color black_87_o = Color.fromARGB(140, 0, 0, 0);
  static const Color lightYellow = Color(0xfffafdf6);

  /// managing edit screen colors
  static const Color editScreenBaseBlueColors = Color(0xFF3a86ff);
  static const Color editScreenBaseFontColor = Color(0xFFFFFFFF);
  static const Color editScreenTextFieldBaseColor = Color(0xff212121);
  static const Color editScreenRed = Color(0xFFEF5859);
  static const List<Color> editScreenListGradient = [
    Color(0xff4895ef),
    Color(0xff3f37c9),
    Color(0xff4361ee),
  ];

  ///managing profile screen colors
  static const Color profileBaseBlueColors = Color(0xFF3a86ff);
  static const Color profileScreenRed = Color(0xFFe56b6b);
  static const Color profileScreenShin = Color(0xffb9e6ff);
  static const Color profileBaseBlackColor = Color(0xff212121);

  ///managing my pets screen colors
  static const Color myPetsBaseBlueColors = Color(0xFF3a86ff);
  static const Color myPetsScreenRed = Color(0xFFe56b6b);
  static const Color myPetsScreenShin = Color(0xffb9e6ff);
  static const Color myPetsBaseBlackColor = Color(0xff212121);
  static const Color myPetsShadowLightColor = Color(0xFF424242);
  static const Color myPetsShadowDarkColor = Color(0xDD000000);
  static const Color myPetsWhite = Color(0xFFFFFFFF);

  ///managing get appointment screen colors
  static const Color getAppointmentBaseBlueColors = Color(0xFF3a86ff);
  static const Color getAppointmentScreenRed = Color(0xFFEF5859);
  static const Color getAppointmentScreenShin = Color(0xffb9e6ff);
  static const Color getAppointmentBaseBlackColor = Color(0xDD000000);
  static const Color getAppointmentShadowColor = Color(0xff393d3f);
  static const Color getAppointmentWhite = Color(0xFFFFFFFF);

  ///managing home screen colors
  static const Color homeBaseBlueColors = Color(0xFF3a86ff);
  static const Color homeScreenRed = Color(0xFFe56b6b);
  static const Color homeScreenShin = Color(0xffb9e6ff);
  static const Color homeBaseBlackColor = Color(0xff212121);
  static const Color homeShadowLightColor = Color(0xFF424242);
  static const Color homeShadowDarkColor = Color(0xDD000000);
  static const Color homeWhite = Color(0xFFFFFFFF);

  ///managing followers screen colors
  static const Color followersBaseBlueColors = Color(0xFF3a86ff);
  static const Color followersScreenRed = Color(0xFFe56b6b);
  static const Color followersScreenShin = Color(0xffb9e6ff);
  static const Color followersBaseBlackColor = Color(0xff212121);
  static const Color followersShadowLightColor = Color(0xFF424242);
  static const Color followersShadowDarkColor = Color(0xDD000000);
  static const Color followersWhite = Color(0xFFFFFFFF);

  // Material Color
  static const MaterialColor mGreen = MaterialColor(0xFF4EBE9F, {
    50: Color(0xFF4EBE9F),
    100: Color(0xFF4EBE9F),
    200: Color(0xFF4EBE9F),
    300: Color(0xFF4EBE9F),
    400: Color(0xFF4EBE9F),
    500: Color(0xFF4EBE9F),
    600: Color(0xFF4EBE9F),
    700: Color(0xFF4EBE9F),
    800: Color(0xFF4EBE9F),
    900: Color(0xFF4EBE9F),
  });
}
