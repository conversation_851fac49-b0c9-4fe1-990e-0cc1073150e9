{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98423ea5c4689251fa5f232d48363b8498", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9896150ec8a6e5f20a7ade217f13c3df13", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c143945aac93cfc4cb7968819a0d8624", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ee9ed2c3a3877a466e256bd7358e7d9f", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c143945aac93cfc4cb7968819a0d8624", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e983ae2f3457760f1684575c421cbcb49e8", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98fe76326bb00ef676e16d1f5521c917e5", "guid": "bfdfe7dc352907fc980b868725387e98e4ed5ef5b160118907caae7630187af8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982b74ce0f99603f8e7b861c4650204f3b", "guid": "bfdfe7dc352907fc980b868725387e98d95b12171102f3ee401f49ec5542e606", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98adf23eb7c4961ac12dd517ab90db9308", "guid": "bfdfe7dc352907fc980b868725387e98d1a402f1fabf26d6cafa12a4d6cc4a55", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a98efb1b8d208be261de101c9873574b", "guid": "bfdfe7dc352907fc980b868725387e987cc5763c6af6f730ad82c113aefcfb3d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b6a4b319fa473b17b24f37a107ecc124", "guid": "bfdfe7dc352907fc980b868725387e984769cb9e14c22303f8e6ee591b2a79b0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98726298e09f3d22ba718bd4d8e8e04116", "guid": "bfdfe7dc352907fc980b868725387e98a84e1516c21092dd27bb89b4c6d644cf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9813f2b6d914e2ec3fff43fc8ceb7f517c", "guid": "bfdfe7dc352907fc980b868725387e9820de8f8815e591f77395350ec9ec8bdd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fcf18bdb0c52d99707bf7905bb5b7ae4", "guid": "bfdfe7dc352907fc980b868725387e98265781bb411e12410be52cc35fc4ba87", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98878381a00f109e02a47f0e44f478b444", "guid": "bfdfe7dc352907fc980b868725387e98828becb898ba7f64ab36f07d6c14936d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9863244d584fba12cec36e3295fd524603", "guid": "bfdfe7dc352907fc980b868725387e984a3c835155df8e48f4efd2dec1446945", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b73b8e66439105c0f9cf2ff34751d477", "guid": "bfdfe7dc352907fc980b868725387e98d7e34dedb6cd3f6636ab3034222ca499", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9814319a6b64e3ddede2c48ac73a64d58d", "guid": "bfdfe7dc352907fc980b868725387e98aa292b4539f2444aea23ce4556e40cf3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b0542ab51f213d7c2883dd4cd63c7852", "guid": "bfdfe7dc352907fc980b868725387e987d4ead99a5a4dc700c3168519b4c8247", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981df1bd958d1aac02e7a45557177642b1", "guid": "bfdfe7dc352907fc980b868725387e98cef7f03e458a597ab53ad1361fe409e7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9886aa1b655e18458a96843fef7c16f44a", "guid": "bfdfe7dc352907fc980b868725387e9809e99b09c89510f5dedb9d0c68b4def1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9878a290751834e43292b529cca26619d6", "guid": "bfdfe7dc352907fc980b868725387e983c1950b13a8dea9f22bd2d503a0a5533", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983c0088508735c94202bb6f23c0f17594", "guid": "bfdfe7dc352907fc980b868725387e988552ac639795280722d1007d2c0e6bdf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985bab02f3ac5118030727d8d6c42ae6fd", "guid": "bfdfe7dc352907fc980b868725387e98a9fae4a869fd9175fcc5cc89ccfc28a9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d61632494747644fe074b64223df2936", "guid": "bfdfe7dc352907fc980b868725387e988c60d4c886d7b03af8102451405a0662", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988fc55ed752cbb341475a1ea55b8eaa6f", "guid": "bfdfe7dc352907fc980b868725387e989cab9fcf3c79d00f88668774676d6be4", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e50e042a1ea325241cfd4c9597f9c5a9", "guid": "bfdfe7dc352907fc980b868725387e981285310e51db32876d2be11b3159a025", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98adb5a37f211d798b18a4649b1b10e787", "guid": "bfdfe7dc352907fc980b868725387e98e675c2d13739871e2e592e6bb562dd1f", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e982fdfd8f6e3f5b96b9049a3b3069ad32c", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e984024d8df05089cb008f1fa8e6f1fff6e", "guid": "bfdfe7dc352907fc980b868725387e98343e12d73b4e85e16351c2f2c49acad1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9884e7bc9b2b35880076d394267397ce6a", "guid": "bfdfe7dc352907fc980b868725387e98889c8fdac4088ce8f11998374454ead7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987818514db712ec5c20e8b79f4f3c7571", "guid": "bfdfe7dc352907fc980b868725387e98b0cc853fc04bb7ca9879e01c25029517"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986ce61404c56b7dfc0e43e0efcb246b78", "guid": "bfdfe7dc352907fc980b868725387e983f2af9d282f24ee1985213c08b69c0a2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980629f053e99baed361c801ec0c6fe4f8", "guid": "bfdfe7dc352907fc980b868725387e9865bda8b4c3c3d8d7d277efe6b92da944"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ff2f83eadddb91ce088faf340179be98", "guid": "bfdfe7dc352907fc980b868725387e980bc4c51cf9a5094a7643dfa349a0615d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a51bc16bd6d373e5915cba1210665fbb", "guid": "bfdfe7dc352907fc980b868725387e981d861e34a7112ae6bca0898d26b1a909"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988dd625e27cfe3b908c394fe07c77f81c", "guid": "bfdfe7dc352907fc980b868725387e98e2b18bf42a76c1586ebfde7ff5e6be61"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b4e35c9e814133b4000fc7da460404ad", "guid": "bfdfe7dc352907fc980b868725387e9848f495e70fdca987ecf88f9d1c3c570f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98335df7e649906ba488c383f97abceaaf", "guid": "bfdfe7dc352907fc980b868725387e98065e8f3e4d5d7367433a6d497b7416ca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985bb3c7708ceb6d5aa1e847923cfe1247", "guid": "bfdfe7dc352907fc980b868725387e985cb6ebdaf7913bcae428df6b9a198d53"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ed259ef8ab46c20c71cde4657d7b97bc", "guid": "bfdfe7dc352907fc980b868725387e98ef8c1074d45c42252e5f1646f697b973"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f7fc15c3f142d90d014ebba5e5cd6b80", "guid": "bfdfe7dc352907fc980b868725387e98cc90cc5cd32cbbef4fd19bed4ddd90c5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9802a3b05f1306d4ba429bb6c18c44b06d", "guid": "bfdfe7dc352907fc980b868725387e98eceeaec041f65e8f4f30e3627bda5bce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9897c8a9bf0087cbb5456120fcfef08604", "guid": "bfdfe7dc352907fc980b868725387e98e3654dedb5a4ff7449fee74b5768005a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a3e21ede0ce5b8962d475bdabbe95da7", "guid": "bfdfe7dc352907fc980b868725387e986b66d5d7e2be529f20291568ec134898"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9833feb27f733346650f513ecf47e0e35d", "guid": "bfdfe7dc352907fc980b868725387e9803c5e7e25a228d642371875c72d808eb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d049f14f67d968d0607342dbcecbccf5", "guid": "bfdfe7dc352907fc980b868725387e982ddab54bb5192f8d6c00dc51e1811c77"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ba2b4c1010f99d41d920b3598e3750e", "guid": "bfdfe7dc352907fc980b868725387e985ef9dcf7f59b26c8a198a24655f688f5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fa463a9781c51ca9494e3b32e12e4ed3", "guid": "bfdfe7dc352907fc980b868725387e984db51fa45a769ce55625f8f33d203b98"}], "guid": "bfdfe7dc352907fc980b868725387e98c037af1c5e69bbfaf52d58747f0e243a", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9824f5d6ec5088eecbb83955c855580732", "guid": "bfdfe7dc352907fc980b868725387e989e6b3f59077c364bc4faa2a399a08312"}], "guid": "bfdfe7dc352907fc980b868725387e98846e2277106b63cf313d1413352e1deb", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98e5968d7a511f88136c92f98d7b001b4b", "targetReference": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823"}], "guid": "bfdfe7dc352907fc980b868725387e98fc4b9d4562eb897c52330ae602a43f4d", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823", "name": "PromisesObjC-FBLPromises_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e981c795e45f8d875aac88217c6a2a95faa", "name": "FBLPromises.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}