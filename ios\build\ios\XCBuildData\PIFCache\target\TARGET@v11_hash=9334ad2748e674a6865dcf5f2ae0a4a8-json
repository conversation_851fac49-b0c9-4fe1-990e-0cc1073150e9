{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98479ca55ddc7724c467bf63148eaf4c01", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98519c0f1aabf34eacbf6f755b813f1496", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984efd3afbb3240499cf56a1947b4dfa04", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ec62137b0ee28ca4265944856877be27", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984efd3afbb3240499cf56a1947b4dfa04", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9832ebf16d88dad2729e444c32094aa46a", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e989eeb62b69d338e7440efe5df1eedb417", "guid": "bfdfe7dc352907fc980b868725387e980eb9d23368b95b4f0923cd46a43136d3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba72debbb7bb52f000601058ccba7218", "guid": "bfdfe7dc352907fc980b868725387e98f457cef344e960dcd5e86a14876a724f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f6e512ffefc43859a6896baacec7737b", "guid": "bfdfe7dc352907fc980b868725387e98448d0ae29de8f3c224c2dcfdec1456de", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98633d9bd15a3b1adaf64184a5bdaf340f", "guid": "bfdfe7dc352907fc980b868725387e98813b7079aa7de27e6db954b27ad09619", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9863078f1e3310bf8df7c85532299964f7", "guid": "bfdfe7dc352907fc980b868725387e981ea8f363f0722babcaed14c60409bf02", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d83183aa3f32fa51e74d566f29ef86d3", "guid": "bfdfe7dc352907fc980b868725387e98e7ac745c3ef27b02fac012aa73444821", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985eecb6a746bfc0e18d315194b8b8b2f6", "guid": "bfdfe7dc352907fc980b868725387e985678ecb94e589405999c12157a408775", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9804c17ddcb5db795df92bbefebbfa39b3", "guid": "bfdfe7dc352907fc980b868725387e98cd87b467ebfeb07dfeb1385499e6cd77", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986b9c8b8d4b9d2aa6d22e23a52ffdb14f", "guid": "bfdfe7dc352907fc980b868725387e9871c163114fc9cc28ecff5b1b2d0a4de2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987645dc37783009aae36481d6daeb2421", "guid": "bfdfe7dc352907fc980b868725387e981978a187afff967da6262086dc0e6fc3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984601c67cb99edd95d8d161ce8eb95e05", "guid": "bfdfe7dc352907fc980b868725387e9826c907de3610a0743fa9d834fe3a26ab", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9865ff4abeb5f2f612982bab33dc361bbb", "guid": "bfdfe7dc352907fc980b868725387e980aab54a36f4355218c04e0f9e0c22709", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98363b5eeaea674afb7cfad98621c20c55", "guid": "bfdfe7dc352907fc980b868725387e98dc02dbc796bf1a1c79ffc8c7bf3cea72", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98be40b6e9b9758ff651b5035631d9a53c", "guid": "bfdfe7dc352907fc980b868725387e98d24acab5f9f5227c374c09fc08c9e695", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98229deea0c503369de5ebe9c277f63d91", "guid": "bfdfe7dc352907fc980b868725387e98eed3f758e63ab4fdfc010074b9de804b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f5628d45f2d582c52725946025f4334e", "guid": "bfdfe7dc352907fc980b868725387e981f08693bd23a6d005b3df15e8e699fde", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe7c15542ef65c12ef9ae4a1299716ec", "guid": "bfdfe7dc352907fc980b868725387e98bf1362e319e49dc96debe2ff1acb9025", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b4318daddf72c4766452081c4e5204cd", "guid": "bfdfe7dc352907fc980b868725387e985b2090fddc5dd999ec75f69196ebf8ad", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98345c42e63131bf94d921c95f2ccb649a", "guid": "bfdfe7dc352907fc980b868725387e98232fcc544ef53e9da9ad40243b28e5a2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f59647c511379e1e83873956167e589", "guid": "bfdfe7dc352907fc980b868725387e98a63597065bd83c36166c1f6aeac7172a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9859f90057902cf5f117f86c4de8a760b3", "guid": "bfdfe7dc352907fc980b868725387e9829db91ca625511289316787d6d10ea44", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eb7a495b2d7e46ea0789a023cfe3fd0e", "guid": "bfdfe7dc352907fc980b868725387e98c0b06c2b33df0dfa02bfa09f9932011c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b241d218a0a9cdbbbfe5da28b83716cc", "guid": "bfdfe7dc352907fc980b868725387e9851ba8fa6ddb817e605d2665626d9d641", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e986a03fa67e33d48dce94be2b8eb2259ec", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98013d6bfd439d383ed5c755f922916556", "guid": "bfdfe7dc352907fc980b868725387e98277468ca209872376e5f206f84332670"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a3469b3fd8c628ea236e570f6b0d3fe0", "guid": "bfdfe7dc352907fc980b868725387e98e9a6f3c2565cf9a05dd3d353eaa7a4c8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9871d8203b9b2ee0a1d22beddbcb7ffbb3", "guid": "bfdfe7dc352907fc980b868725387e986111772b572857d7961a008265fc355c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf53489803aec8bfc5f758a39fc1eb06", "guid": "bfdfe7dc352907fc980b868725387e983996f9c15e1fbf682fcc40770fd256d7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983e02275e222c11c8a67a91bd8dd8638b", "guid": "bfdfe7dc352907fc980b868725387e9858a8497e775e00416ce705748e767ece"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e13d8701cb22ec0653e15ac30de4d282", "guid": "bfdfe7dc352907fc980b868725387e98c6bee8141090191321d7771c3a1f978b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98528a6c4649485f486802dbbfea534914", "guid": "bfdfe7dc352907fc980b868725387e981cebb2289f1bb2bf7dee62eea371ce0c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986b5f6350e5830f07d7f9453e6f82dbb3", "guid": "bfdfe7dc352907fc980b868725387e98f1f6bd36d03eee3c0bdc3bc98e83ea19"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982affa15f8cac726c038782404b5cd47c", "guid": "bfdfe7dc352907fc980b868725387e98a32f894597a07a2a5504a8b9bcfdabdc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9840aca45e86ff93715ddd74f235378373", "guid": "bfdfe7dc352907fc980b868725387e9800ac44543ad413e32dda38d808c2afef"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa50de11f82117e1d4ce176d3e028844", "guid": "bfdfe7dc352907fc980b868725387e980a3b558904dbc56f2153ae21439d6437"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a930af10c496c841de5e3f450c345482", "guid": "bfdfe7dc352907fc980b868725387e9803ab6e9df07f67a9975e5c61b861ce4a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c4a601e2d24e6449396e8103db81dba6", "guid": "bfdfe7dc352907fc980b868725387e981850b344567e5543096f0a83d9eac0b2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982bc14928383ba64034cc06cbc48e5933", "guid": "bfdfe7dc352907fc980b868725387e984a502a85d82ab7ae770f404ac3bb3b9c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e30ed4f6aa3662071b141c967c72d42", "guid": "bfdfe7dc352907fc980b868725387e984c79dfb7fcb3ff07f9037801d36c7134"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a082233cbc9d5ee5e542a5a27d783439", "guid": "bfdfe7dc352907fc980b868725387e98832efc148e497d7aa81cf3800da27235"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e677e31cb763bb52b8dd789947601859", "guid": "bfdfe7dc352907fc980b868725387e988b056d871bf720cc83d9b2c7291eec07"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e52cb04de6b3f78cbb2084344b9d4f41", "guid": "bfdfe7dc352907fc980b868725387e98afb0626ba205a690e15081763b4adef1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9819517a34a05b5d6595bca5ee06323344", "guid": "bfdfe7dc352907fc980b868725387e98cf6232b3e1482ebfb128206cef66a17c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c99f106af3df7a8b48c1dca8228cb676", "guid": "bfdfe7dc352907fc980b868725387e98c76565777e1dccbddf6eca764d227a41"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98487e6e3983289c351ee0d43d6b349e37", "guid": "bfdfe7dc352907fc980b868725387e98dba12151ab617cdd659014f8477b0bfb"}], "guid": "bfdfe7dc352907fc980b868725387e98be6229230a4715433df2f8e74fbafc5b", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9824f5d6ec5088eecbb83955c855580732", "guid": "bfdfe7dc352907fc980b868725387e980797c2152e50219ee4196549bb34f857"}], "guid": "bfdfe7dc352907fc980b868725387e984d290968aff9eafa4ed5b85c80a8c610", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98fa0d11ed0b4e1a85c13d68e37d1547e0", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e9802f35ab680609a626ebd2ddd692a3822", "name": "permission_handler_apple-permission_handler_apple_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98ef10255b706f98e1e88fae00855b0968", "name": "permission_handler_apple", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f8f53f8ba4165e76c7481b24262177ed", "name": "permission_handler_apple.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}