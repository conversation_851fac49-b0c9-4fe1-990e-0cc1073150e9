{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98bf8a972b847efa3f2a52460d6ad4c53e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e145aea6e60f959dd61971f2a3198cf5", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b6ed03b993f6937daf75b09a08d3c4d7", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e983f2cecc58f90b8bd5be501dbb33fd583", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b6ed03b993f6937daf75b09a08d3c4d7", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985cf379eb4ac0765565ded3cf3a912d4f", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9859b00a7757ea036059af35093f548ea9", "guid": "bfdfe7dc352907fc980b868725387e982c18f37a58bab6b1016b318c37afd1e8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dadf722c8d02b3d271f01a7b3d377fde", "guid": "bfdfe7dc352907fc980b868725387e98d7bdbdd45cd7f1d9f155338640ffe33c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9886760e827c72776e97f3d01fd24ac971", "guid": "bfdfe7dc352907fc980b868725387e980222205e8544149fda1c9cb9b5c57e46"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988daeea01edb15457eb52279ff45db967", "guid": "bfdfe7dc352907fc980b868725387e983e0aadc83aa3c03b215f986035a382ba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983d62a7ce05eef7a8a6f6c0e856056bc8", "guid": "bfdfe7dc352907fc980b868725387e9826091a35998dc7b42e2a45aba33cdabb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c98b19c84ee21538d33ec5b03775503", "guid": "bfdfe7dc352907fc980b868725387e98f705ff4567bc7e301a60214e915cfd69"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d7e922e08bf3b61ba74f5d0dd708f93", "guid": "bfdfe7dc352907fc980b868725387e988ef6d44e13391915919ababcdaa15dc6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9843128f0b98319aeb547ab4d3a65808e6", "guid": "bfdfe7dc352907fc980b868725387e9824b60ffadbb7603e17b319972a36cfbb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d5bbdf5d6669f669ba25e0218e72daa", "guid": "bfdfe7dc352907fc980b868725387e983e4c618c4f36e22c9eb9939ad2885997"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f1b9ac150829f76ac3bb33159ae6a134", "guid": "bfdfe7dc352907fc980b868725387e989909c5436ee37de362556b3edd4560ae"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988a8658423e67ee6d7c3ee1fe5dfd1c93", "guid": "bfdfe7dc352907fc980b868725387e983c7a39f39c99348c2994f65bce91f5e2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fdd39035a7bba7bd0d0a60080698fefd", "guid": "bfdfe7dc352907fc980b868725387e98362358fb0865d52205a650274c7be1c5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b451eb4224eb6b84c5e7355559c0bfae", "guid": "bfdfe7dc352907fc980b868725387e9850a38b2a47f6d6c66a6cd2af889d11d8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98909161009b827e1177e2054cc050c3c6", "guid": "bfdfe7dc352907fc980b868725387e983af4d8715f3038e88f72e6782a0762bc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987bd611cdf8ab04552a94071b61928b56", "guid": "bfdfe7dc352907fc980b868725387e9857729037b330080608afdb7ffa805080"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9856b5321acf59cb68fc59527751e9b00b", "guid": "bfdfe7dc352907fc980b868725387e9816ed2a0e05c4a27df7abd49c56730eff", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986967f63d7ba70132ee7c70cc66d6c877", "guid": "bfdfe7dc352907fc980b868725387e98c689669d54659a4a75d965cfab8ec1de"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9852b92c7f35c1b48ac09c0cc6b097cfe2", "guid": "bfdfe7dc352907fc980b868725387e98d0c76957b92239474d05aec1d948a0d9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fdcb937af0de58cecfd2b6d185743ccb", "guid": "bfdfe7dc352907fc980b868725387e98a631e9abf7e2de63e3c81de4f3d04364"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981ac837ed95d4ae96202afccadcdfd02d", "guid": "bfdfe7dc352907fc980b868725387e9812eb62957e2e636e736a9d8094358147"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c79d383ab5995c4d95e00f9acd42d85c", "guid": "bfdfe7dc352907fc980b868725387e98abfc10ba82c4191773a17d0ee6f2438e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a80dc405a4a5b6fbe279efab829ee900", "guid": "bfdfe7dc352907fc980b868725387e98169b4554887d14295c310e617d9122e4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ef5b63312daa658a9cf0dc9d9461f73", "guid": "bfdfe7dc352907fc980b868725387e986d29eee57f2c4c2872bd72c475f6caa1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9840e021c99d6af9dbc02200727e634a1b", "guid": "bfdfe7dc352907fc980b868725387e98c29d6f96b0400e045f7fe5708733e519"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98490ebfd917e11e8828b27c127c39e48f", "guid": "bfdfe7dc352907fc980b868725387e98580ef720741377b28484272f5f57a5c6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988eaaccd19e243e7e168d13019a455974", "guid": "bfdfe7dc352907fc980b868725387e98fb5d67db8150d5e93a28cf582c12469b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d919073a2c7d365a2c0a6ebc383f8c02", "guid": "bfdfe7dc352907fc980b868725387e9801f9c6e2bf8d201749d08158892a1887"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982e2fd1c4b0f6dee0b71cb32d3655534a", "guid": "bfdfe7dc352907fc980b868725387e9813065f2873180ed473c3de0c73cd7243"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987828a2bd7e000fd3422c05d91bd6f362", "guid": "bfdfe7dc352907fc980b868725387e9884077d4ee81c46bd655e3c121a7be124"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f678fb2ccd0c69b577d3f61da6d8067b", "guid": "bfdfe7dc352907fc980b868725387e981de8bec092554ff76a9020ef3ee1ca2c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9894f48be72a141562f0712b6a73ae2061", "guid": "bfdfe7dc352907fc980b868725387e984c6de541e83f5bbcb5ac51bb74ec3905"}], "guid": "bfdfe7dc352907fc980b868725387e98844b10e319ea099dd60a39efd0422073", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ba4f21b824d59917ec0adbb4b5a03583", "guid": "bfdfe7dc352907fc980b868725387e98694fa85286ebc73dc93d3870159450d8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e48816177b0910f91b9fc69921e6124e", "guid": "bfdfe7dc352907fc980b868725387e983c592d75cf7fe59212d8ba8b4cb536d7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a0424309ab73a8e9b1b510d5491a3aae", "guid": "bfdfe7dc352907fc980b868725387e98c580ce6740b9fc4b8004f32105af86da"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988d13c2b1e50aed5309884e36bd22cd35", "guid": "bfdfe7dc352907fc980b868725387e98f64d14c06f2d7e0289e11d21bce6f76d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c2b2a55baef62dc2a3923366927b1ba3", "guid": "bfdfe7dc352907fc980b868725387e98e54f93ff88a6cc0e9a5f541f50dc4df5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9884ccea8585ee08c53ec0786af2f925d4", "guid": "bfdfe7dc352907fc980b868725387e980d90a0dac4baca27dd7f1e3f166cae43"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a1304eb3e5cf4a8553be7cff8ed85d30", "guid": "bfdfe7dc352907fc980b868725387e98ee8f53aabd5222365bf642c379781c66"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc46e98c2ef8af8c417c76d34c5b4268", "guid": "bfdfe7dc352907fc980b868725387e987bbd842a99658d562c4365cbd0715b66"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ffaf0980654c59eea42a7566cc1e9e2b", "guid": "bfdfe7dc352907fc980b868725387e989c2df83442c2dff71d14919e617a8c6c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982307d38aa4ab9a16deac51f35dd7cb5b", "guid": "bfdfe7dc352907fc980b868725387e98a6312609e426ba925a12c14ed13de002"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98db67c40f4bfca057ab6f6b61dd6616a3", "guid": "bfdfe7dc352907fc980b868725387e989152ec7acbb8745b4daef622e1dc86f8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ff03b097c23face12619b4026d88d697", "guid": "bfdfe7dc352907fc980b868725387e985a14d12064b237431f0cb9ba67312973"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a343ad957f7886e446a26353c9e46bf4", "guid": "bfdfe7dc352907fc980b868725387e985e5eac17a84c008b3042cd42c4abfdcc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e0f034507badfc21f984dccba0914e5c", "guid": "bfdfe7dc352907fc980b868725387e985fb5000cf3618f2d9e8429303fa9ae65"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bcc7b202c4ca25e2ba18200111144ecb", "guid": "bfdfe7dc352907fc980b868725387e98fcc98dd98e26fc9512093861dbabf45e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9801c346f998e4acd4695e52caf999fc9b", "guid": "bfdfe7dc352907fc980b868725387e98d0326efbf478eb18d489c4e5793bb54c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f9d3f16265bcae20750c1118d4a61996", "guid": "bfdfe7dc352907fc980b868725387e980ca400d1b6b461c9f2d92ad5166233a6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eb0ccf8fd96505f9830b2fa51a4d7b0c", "guid": "bfdfe7dc352907fc980b868725387e983db170b04f583b4324a65e6940984bca"}], "guid": "bfdfe7dc352907fc980b868725387e98577ce0bb6c7f42c2e1e456b06c48b9fc", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9824f5d6ec5088eecbb83955c855580732", "guid": "bfdfe7dc352907fc980b868725387e988ed89ed8ab9fdd6306a87efec8bd7a8a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ee33ff2b9f8b1d223313a7a24cd5501", "guid": "bfdfe7dc352907fc980b868725387e98f477cb1a07fc3acf4cef3f431770261c"}], "guid": "bfdfe7dc352907fc980b868725387e98b38875a3d809b399e3c9155991c5a02d", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98ef987b6884d37b29cb07c7e85cfcf34c", "targetReference": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc"}], "guid": "bfdfe7dc352907fc980b868725387e980a68f22990eba1c7200af0703ebe7a4f", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc", "name": "FirebaseInstallations-FirebaseInstallations_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}], "guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9860819b8e327bf41b291e92315614a812", "name": "FirebaseInstallations.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}