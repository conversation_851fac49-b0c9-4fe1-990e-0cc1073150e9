{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98923fffa5bfc8e59a950578bc7259b037", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/cloud_firestore/cloud_firestore-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/cloud_firestore/cloud_firestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/cloud_firestore/cloud_firestore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "cloud_firestore", "PRODUCT_NAME": "cloud_firestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e988739fe12e2029ec33474d51e1c1de486", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982005f6061c49d893c112c844e3bae40a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/cloud_firestore/cloud_firestore-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/cloud_firestore/cloud_firestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/cloud_firestore/cloud_firestore.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "cloud_firestore", "PRODUCT_NAME": "cloud_firestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e7e194b3c7a0dcadb37a08a8959714bb", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982005f6061c49d893c112c844e3bae40a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/cloud_firestore/cloud_firestore-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/cloud_firestore/cloud_firestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/cloud_firestore/cloud_firestore.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "cloud_firestore", "PRODUCT_NAME": "cloud_firestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ad06093d4c8fed0ad01765d765863350", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a2df96c89baa2a559c147208f4c18b98", "guid": "bfdfe7dc352907fc980b868725387e982f7568700678161c9dd366d7739bf413", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98093b1f71d6000048484c8c64c6c643a8", "guid": "bfdfe7dc352907fc980b868725387e98ef0935b2d993b83a2487918735faec60", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b30e7b6afeb07b79a009ff27af3d3d11", "guid": "bfdfe7dc352907fc980b868725387e989e5b74a37eb98237da8a8bf483a010d1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98802d5e9f8d2ad62ac88f1c04bc9d4356", "guid": "bfdfe7dc352907fc980b868725387e984252221125eb526ea75b4c97629f279d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98938a0dcb8898cb231a57870e8482c985", "guid": "bfdfe7dc352907fc980b868725387e985e2d86f84de499c9e18a75602b2a54b6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d0de689154bb4467a349003d5e7b882e", "guid": "bfdfe7dc352907fc980b868725387e98cdf497b140c1a106bd2729a017789eba", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ea67c304eedb74a4707caf9ac6b10b55", "guid": "bfdfe7dc352907fc980b868725387e98dd9a2419407a06ac6e932c347a44ef4f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b492d1e731658c151691b5e54c7a9526", "guid": "bfdfe7dc352907fc980b868725387e9861351a26c223b1aa873d3ee4e896f182", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988fa9e398ca1a92ad62e1647aad76b887", "guid": "bfdfe7dc352907fc980b868725387e98c22385ab26d4925595094a8582af79f3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98585c16ede8acf670c8b70a25b7b6ae01", "guid": "bfdfe7dc352907fc980b868725387e98eacf9a2ced2fab76a9a3cb3459fc75ec", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988793b2df246e1278195b7a32c2771d32", "guid": "bfdfe7dc352907fc980b868725387e988823f3119bbb1d6d316efa2e45a0f043", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98606a0667787a794fe1c0dfd4c3f5ae84", "guid": "bfdfe7dc352907fc980b868725387e983aaa7670969755f4a6bc1b1d983b586b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982e572f59ebeab726732a7b2ab50b22c0", "guid": "bfdfe7dc352907fc980b868725387e9816eb28f899938e46e9321229aa9b1aa6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987172b3cdb32abb8820483d099b585509", "guid": "bfdfe7dc352907fc980b868725387e98d51e3594a544ffe8cb40291905dcea69", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98527db3c40639cc6759f0e8015001fafc", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e988d890532858d9db93a1d45a7feb53d5a", "guid": "bfdfe7dc352907fc980b868725387e98475fc8e8b3ec4b7dd01534402dce2e25"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d774bd2dbf095fb2fe198c6585f66e3a", "guid": "bfdfe7dc352907fc980b868725387e9898f6304ca68c945571b0f89d92fd28df"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba64c0f09e4b8461dba86dfaf2468548", "guid": "bfdfe7dc352907fc980b868725387e98931f0cfaafe21aa977f1e022561a7619"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b0f04130f4fbda8b13204e94b8de1ac1", "guid": "bfdfe7dc352907fc980b868725387e98e89ddbb79c81aff8a9cff8880ec5bdec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d71d280dbb1d6843d62e87b02644b922", "guid": "bfdfe7dc352907fc980b868725387e9878da95613697f0ac6561c9a76369a40b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984b352fec3bb8eca2cefbc901d1fc4517", "guid": "bfdfe7dc352907fc980b868725387e98f26d9408e974f7c840998ee559e6ed5f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821e697368aff10cd89fc646b04707796", "guid": "bfdfe7dc352907fc980b868725387e98d8268cd85cfbe9fbaf984300ee5df86c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f0944001a39be26db96412cd2aa8b8e1", "guid": "bfdfe7dc352907fc980b868725387e98e32330167abcc065dd1d3d2360d71378"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa45efc7a0258f70bee823a49412a01e", "guid": "bfdfe7dc352907fc980b868725387e98a6ce22dc01bfb379c8ed98f787fdb369"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a29451447c1712e7295efa49c3844ee3", "guid": "bfdfe7dc352907fc980b868725387e9833f919612edcea89e1c5dc84e13d3a88"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98509426c02cdaca7ab5e2177e32bb72f8", "guid": "bfdfe7dc352907fc980b868725387e9843acf7327c7b606e40d2e31dbbc0d844"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b1d04873871548fc0b0bd726392c2934", "guid": "bfdfe7dc352907fc980b868725387e98acd93eba72b16d32fd58e65341d2e949"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e8baf694893b6915f05f0cb4ea709672", "guid": "bfdfe7dc352907fc980b868725387e9876400f08eb3594c57a1619bbd2111836"}], "guid": "bfdfe7dc352907fc980b868725387e98445c13b134f6717c552fb845dd07cf6c", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9824f5d6ec5088eecbb83955c855580732", "guid": "bfdfe7dc352907fc980b868725387e987208fa6207d938fe537d0e8359bf5699"}], "guid": "bfdfe7dc352907fc980b868725387e98ee8cf58f2eaf1185d54859fdaf1a4231", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98e1b65cad46427b34676adbcb934284e0", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98d57b8bce60a0f11113f4cff532db68d3", "name": "Firebase"}, {"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e987f74324bfc5c78140e34d510e26e00c1", "name": "firebase_core"}], "guid": "bfdfe7dc352907fc980b868725387e989840e8244cb75f43b3efe8cd6dec5ec5", "name": "cloud_firestore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98321793542cff8793cba84baa893d5044", "name": "cloud_firestore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}