{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f33453c53b0aaf96bf66a2668f555165", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98aa5778bfd6e1bb0c6b3d28f1747c18ee", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98fc14d961b3a212eb497399d66a0b074e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98605e660aa3b25726c76026e028014668", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98fc14d961b3a212eb497399d66a0b074e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9840776ea171b8fdcf739a8327632521e3", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e981d33ebed6dfe4d8f507d9f9b92e16c1f", "guid": "bfdfe7dc352907fc980b868725387e98099751cc12d8357965f36cf164645c43"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983a7b7c39c23a776756df9dfd62570104", "guid": "bfdfe7dc352907fc980b868725387e9855eacc43c80c8b2ae1222de7ed095963", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9857bc0b7c702c8e77115ccb308bffdbff", "guid": "bfdfe7dc352907fc980b868725387e987ab859130010325b256c6bb636950a75"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b6a2a9a60e492d3ed3aa33a22a50bed8", "guid": "bfdfe7dc352907fc980b868725387e98c0da0ce461a5705b966584a4bfbf8145"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9838fb87b5874dbbd20eefd3a9672aeb3c", "guid": "bfdfe7dc352907fc980b868725387e98149fdbace379807c287c836b02115bdf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98962d7f80b2094268508e2ce9e4b59529", "guid": "bfdfe7dc352907fc980b868725387e986aceb04fa13d77a34f50857605d9ce4b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98119fb70fcd96fdc105ee0ace81cdc7e4", "guid": "bfdfe7dc352907fc980b868725387e98304dd95caf20294ccdbead85d5ed3d26"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f2e260b3e61cd720523ac0fc65319e13", "guid": "bfdfe7dc352907fc980b868725387e9821fe7a4705df8422b7c07aa6171083bb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fdefbb55d025e2bcc95975cc9dd4e488", "guid": "bfdfe7dc352907fc980b868725387e9896530cdb9cf751f7cf147ce3127164e5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9851a5d1a36db43bc20141a23fc5cf91a4", "guid": "bfdfe7dc352907fc980b868725387e980c197d5345a7afd3380d64c866a47c2b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c160e5d3522b64c52690c2649346e13f", "guid": "bfdfe7dc352907fc980b868725387e9827016b8d3d5f61105b77523167f1da2d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989f66d72f50352cc9bb1fa415379fca44", "guid": "bfdfe7dc352907fc980b868725387e98022c7212da294bdd357aea577b980492", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d41b9e8b6125546a0269c7a382ae12ca", "guid": "bfdfe7dc352907fc980b868725387e98f2b1a5df1ecb41ca128bacdc9512edd3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989e49aca0daa05c6509913cf03765ba42", "guid": "bfdfe7dc352907fc980b868725387e983adb3027640c5eda84bc5260479cf0f7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98334fe46511474fb54f58bdbf53a0e662", "guid": "bfdfe7dc352907fc980b868725387e98b109da080ab5dde910d35c5e56cccd6a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9852b947d1e766546c9c0091bd8565dfe7", "guid": "bfdfe7dc352907fc980b868725387e985be64098ceeb80be3c17a1f78c84b5c2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe8beb3ce6b311d3aeff74153f20a719", "guid": "bfdfe7dc352907fc980b868725387e9884f0f25890f376472e3c0b311d825b7c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9836dbabfd68322235aa0b67ee78cd5fcc", "guid": "bfdfe7dc352907fc980b868725387e98c046e32884896d66e00c6e5ec5026a31", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c581b2ef63a7577918ccd35ce2dd2a7", "guid": "bfdfe7dc352907fc980b868725387e98517eb91220719cc00cd1db694bace287", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9811802c9b19ab4cd7776a4922f8e7bd0f", "guid": "bfdfe7dc352907fc980b868725387e98f25abd8479e600171bcbb9449ed6435b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c66b5a1fcc34883041267b882da5e4a9", "guid": "bfdfe7dc352907fc980b868725387e98d8a439acb04e10cfcc737d2e5cc8cac8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98190d87f55351cdba10ce13aa29408b95", "guid": "bfdfe7dc352907fc980b868725387e98d785296f940fc9728603e3cb9619dbb6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d13ba752d05b6968df2ab0d40935837b", "guid": "bfdfe7dc352907fc980b868725387e98df8b734de54528fb6a3fad5177fcbf6c", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98dc234616eca28906a5ed8aef87acfa66", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98606d988246c2062a692324a230111013", "guid": "bfdfe7dc352907fc980b868725387e98f16cd3882607e4cae04cbffd33f1b995"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b14d2cfd1ffe0a314cd3832912b7674e", "guid": "bfdfe7dc352907fc980b868725387e981f1f5aec11e9cbbb21b3d58753ab36dd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d4c0366aa32f7f4b57583dff501e5723", "guid": "bfdfe7dc352907fc980b868725387e983eacf307693d07ef9548d2186d39b2e8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980c1f64b2f278d6034e6a8fbf0700cdef", "guid": "bfdfe7dc352907fc980b868725387e98909de08c4173266a6c9d03ca9ddd4bd9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980813ce621f7e37301cd2b6ee2a76c729", "guid": "bfdfe7dc352907fc980b868725387e9879b294b516101c63a73581de9dd0a48a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9870a11ce5105b4d3a55259ff82a985794", "guid": "bfdfe7dc352907fc980b868725387e98afcdee52721333b69472a63765b1d404"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98767068ba23a76f9315808a29deef831c", "guid": "bfdfe7dc352907fc980b868725387e98536b5a368b79b2ec79738e0c66ee69ee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98403c0ad85db409776a1eec47580ebf87", "guid": "bfdfe7dc352907fc980b868725387e98ed7b391999b454dc4c658a588180e6c2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9811e4d9933d873f105a0eddacb4fe9be5", "guid": "bfdfe7dc352907fc980b868725387e98e514ccd3806df00afd735841bd733498"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988bd0695308095a9d945db23823640426", "guid": "bfdfe7dc352907fc980b868725387e98758509819b19f0b32f7c1758538e39e0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c83e097953e264a6ddb9fa1a3a39cd1", "guid": "bfdfe7dc352907fc980b868725387e98d20c01ec33c83946fd63aa0838391964"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98796a0ba5ba98ea4e7e9bda406b8502b5", "guid": "bfdfe7dc352907fc980b868725387e98dff891719c09326f671821de9621093d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980807e9a75a397f5948eb9c7a203753c6", "guid": "bfdfe7dc352907fc980b868725387e98b2591fc397d0025576165b02c6141b4e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9839d758e33b7e4065f111750bde30980c", "guid": "bfdfe7dc352907fc980b868725387e98ada5e917862c3c7a5111d3c1f7c45dd8"}], "guid": "bfdfe7dc352907fc980b868725387e98087cc2b0004cde27467a9740cc64f2e7", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9824f5d6ec5088eecbb83955c855580732", "guid": "bfdfe7dc352907fc980b868725387e98faa69ce14b60b5972c7c14a427994158"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9848a7bf5f5ee928f3be52fdff6a2dc80a", "guid": "bfdfe7dc352907fc980b868725387e98a5921bf957306effbaca680bdc521ab0"}], "guid": "bfdfe7dc352907fc980b868725387e985631722067f0f5111b71bfdf2b4b5680", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98bc76d56ba410ff7205928091bd88e7b7", "targetReference": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14"}], "guid": "bfdfe7dc352907fc980b868725387e98646f099108d1cbad4721b5317c6884a8", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14", "name": "FirebaseCore-FirebaseCore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e988ae261e418baab0fdd0a48d117fe7fa2", "name": "FirebaseCore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}