{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987faa96b07309521cd10e0ad0a4c3869e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/cloud_firestore/cloud_firestore-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/cloud_firestore/cloud_firestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/cloud_firestore/cloud_firestore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "cloud_firestore", "PRODUCT_NAME": "cloud_firestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9841460123e5c7e8847071fb1612f31005", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988b1135c20e9797b5bbd4603b6f20a60e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/cloud_firestore/cloud_firestore-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/cloud_firestore/cloud_firestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/cloud_firestore/cloud_firestore.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "cloud_firestore", "PRODUCT_NAME": "cloud_firestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98075bb58718cbb4226e000603e9effc4c", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988b1135c20e9797b5bbd4603b6f20a60e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/cloud_firestore/cloud_firestore-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/cloud_firestore/cloud_firestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/cloud_firestore/cloud_firestore.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "cloud_firestore", "PRODUCT_NAME": "cloud_firestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e552683f4e12838e5b86eb01b9f1b768", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b7567baf497b8f48536d3c0511447b0d", "guid": "bfdfe7dc352907fc980b868725387e982f7568700678161c9dd366d7739bf413", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98486d12e182a24139542dceb25dfcaa1e", "guid": "bfdfe7dc352907fc980b868725387e9825281f02500bfbd7a032b7d86fe95e02", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98176916f922154669e989753f5f505d8a", "guid": "bfdfe7dc352907fc980b868725387e98d92ed8cb55c21a1ac822b6efe82d786d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98106b36d59462a3b235f1ed129802830a", "guid": "bfdfe7dc352907fc980b868725387e986c29759f78019cb8c63a34cfff88f146", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cca4f56245d6e468bc8ac71766b8c349", "guid": "bfdfe7dc352907fc980b868725387e98d052bd405f9f01b6121ca5d6065bbf9e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98067551f4ecc00e5195d7d3b3bdc9970c", "guid": "bfdfe7dc352907fc980b868725387e982915052d5a8cc0cdbbc101028db90033", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982b8d3f8e1ea9d0a7e11271f6859ebd2f", "guid": "bfdfe7dc352907fc980b868725387e98c7dbec9f2f7dd27ddebf8331055ad464", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dd8ec153cd3a37c684bb60d9fed4a218", "guid": "bfdfe7dc352907fc980b868725387e9805e28a799124a56b29e21db383dec89c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e85dce6e967c96b88da23f81c6ef3478", "guid": "bfdfe7dc352907fc980b868725387e9826bf27993c8901cb30ee5322909384f0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984a86e2f3276b6b32409d31337aed06e3", "guid": "bfdfe7dc352907fc980b868725387e985fda2b97ddd8c1c547ddad8db5cf04b1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba4855660942a63f2814abfb0dc47d5e", "guid": "bfdfe7dc352907fc980b868725387e98f53bd00da32df1c859a4a10eb14d8094", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983e83967d1e95b87811b59f05b4c87b0a", "guid": "bfdfe7dc352907fc980b868725387e98b3463842051897bf169ecce8fc1652c2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f9aa862c366d669287028b56b32ce74c", "guid": "bfdfe7dc352907fc980b868725387e982a361e92b84f0aa509afdd552b0ba498", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f9d8c1c1523b57d87433660f5a5e7521", "guid": "bfdfe7dc352907fc980b868725387e98b32684faa5a865f8e28b2f05e2461a68", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98527db3c40639cc6759f0e8015001fafc", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e988be106737e18d54684df8af7ddd8cdcd", "guid": "bfdfe7dc352907fc980b868725387e98475fc8e8b3ec4b7dd01534402dce2e25"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c29cafa6b0a8f94cc28371e4e4d2820f", "guid": "bfdfe7dc352907fc980b868725387e983092f0975d6fc0b544478e71e6da0508"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b66eab47357a2f23db56b4d41954a870", "guid": "bfdfe7dc352907fc980b868725387e98d8161dd5f839ed12eab22d807188945a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987b6297cef94284b8538f04836948055d", "guid": "bfdfe7dc352907fc980b868725387e98069b6ca278c968a3e3b22e9c39df2040"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983e99d82b83ca183958a25eb89c1fb657", "guid": "bfdfe7dc352907fc980b868725387e987d8650992639ce39a6286c5b9998da7b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d0136890aadb8f1395c24a67bf4760fc", "guid": "bfdfe7dc352907fc980b868725387e9859339c0c49fb8771369a9da416773c21"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9894ad3fba820db56dba21c1317184357f", "guid": "bfdfe7dc352907fc980b868725387e98bd4b2c5c8f9d1620347482dd88594147"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dbce4ca7ffec53571304416d092164a5", "guid": "bfdfe7dc352907fc980b868725387e98baddd97189c698dcaf804dc0668e3b94"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a27eb4e82d0b2b93e811bbab2009f2d6", "guid": "bfdfe7dc352907fc980b868725387e988ecf091a19d29f37e4d2a37ea85056f3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984e42bf679ffc67392fafe14154288f73", "guid": "bfdfe7dc352907fc980b868725387e98a2656ad28a52df4d8907854e209006d7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9816a480d2cff5fbc8dbb01faf184484ec", "guid": "bfdfe7dc352907fc980b868725387e989c36b47b8a75fb954ae4e5a73418c1e5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98042441946a12302e07bd0655df562d62", "guid": "bfdfe7dc352907fc980b868725387e9829f54e016819bb60601dd14baecc6d4f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cce364385df03105c19b6995c3171395", "guid": "bfdfe7dc352907fc980b868725387e98e73a5d55b57ea99b62b638137a9fc6f4"}], "guid": "bfdfe7dc352907fc980b868725387e98445c13b134f6717c552fb845dd07cf6c", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9824f5d6ec5088eecbb83955c855580732", "guid": "bfdfe7dc352907fc980b868725387e987208fa6207d938fe537d0e8359bf5699"}], "guid": "bfdfe7dc352907fc980b868725387e98ee8cf58f2eaf1185d54859fdaf1a4231", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98e1b65cad46427b34676adbcb934284e0", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98d57b8bce60a0f11113f4cff532db68d3", "name": "Firebase"}, {"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e987f74324bfc5c78140e34d510e26e00c1", "name": "firebase_core"}], "guid": "bfdfe7dc352907fc980b868725387e989840e8244cb75f43b3efe8cd6dec5ec5", "name": "cloud_firestore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98321793542cff8793cba84baa893d5044", "name": "cloud_firestore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}