{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98aaf1b3426d836bf5803e74b1d10cfbcd", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/pointer_interceptor_ios", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "pointer_interceptor_ios", "INFOPLIST_FILE": "Target Support Files/pointer_interceptor_ios/ResourceBundle-pointer_interceptor_ios_privacy-pointer_interceptor_ios-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "pointer_interceptor_ios_privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98aae5ae6349ca77437fa6ae18a234eca2", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ebce57c46eaa18716d64b9b414253523", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/pointer_interceptor_ios", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "pointer_interceptor_ios", "INFOPLIST_FILE": "Target Support Files/pointer_interceptor_ios/ResourceBundle-pointer_interceptor_ios_privacy-pointer_interceptor_ios-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "PRODUCT_NAME": "pointer_interceptor_ios_privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e986646a00c64d0a1fb97e344f22a8aa494", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ebce57c46eaa18716d64b9b414253523", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/pointer_interceptor_ios", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "pointer_interceptor_ios", "INFOPLIST_FILE": "Target Support Files/pointer_interceptor_ios/ResourceBundle-pointer_interceptor_ios_privacy-pointer_interceptor_ios-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "PRODUCT_NAME": "pointer_interceptor_ios_privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e9839b20e821f5b48e114368eca2f4bb1c1", "name": "Release"}], "buildPhases": [{"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98c5e89475ea0af0f8f2032502218a899b", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98e53f99753ba9ed6d392a1c5e6696e7e6", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98005a287bdefbe53c5449d1c73835522b", "guid": "bfdfe7dc352907fc980b868725387e982b734f2bc24362798011f5dab9d4047e"}], "guid": "bfdfe7dc352907fc980b868725387e985a5adb1ba9579c9ef9faa70055d3b2e4", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e989f9161ff8e5b778c00c0f4202dcaadc9", "name": "pointer_interceptor_ios-pointer_interceptor_ios_privacy", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f49a561b1f656669a5bf019886c6571e", "name": "pointer_interceptor_ios_privacy.bundle", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.bundle", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}