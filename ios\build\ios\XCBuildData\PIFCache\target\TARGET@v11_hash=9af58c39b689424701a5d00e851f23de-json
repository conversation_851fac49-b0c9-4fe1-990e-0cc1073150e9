{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9889e44aea1a6b19eba9f1cdb1922a3538", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9809e0b790b1d6247b74cef132e910e1c2", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98da00a5db0ab2f5251a0c7bde72e3043d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e989768c20858c64b92e8c52f51608aabab", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98da00a5db0ab2f5251a0c7bde72e3043d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9892fdaa2b71a1f6a8c2c940b2dee715d4", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d0cdeebc5301016cefcce86d74cf5cb0", "guid": "bfdfe7dc352907fc980b868725387e98bf73ea42a167fa46e58df3bc83e3a002"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983cf5f80389214187dd9e1c33c8ebec05", "guid": "bfdfe7dc352907fc980b868725387e984501eca9a6a8cc0ab33fe36f60facab1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a57a8ca1ce63c1316fde2f0cec2edb13", "guid": "bfdfe7dc352907fc980b868725387e98f39a6b6c5f60939fc55463ab72fa874c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981ad3ac0eee7223075a818526efaefbd9", "guid": "bfdfe7dc352907fc980b868725387e98cd8a0f5c1077c069a805ce8e483fd60e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980afb4f62a351402a5e0b6c0f7aaaf12d", "guid": "bfdfe7dc352907fc980b868725387e98ae3d54649efa6204f12c8dcf85c90212"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98177d68ca9430de96483a96c770e23c12", "guid": "bfdfe7dc352907fc980b868725387e985500694295c894c191e479cc21fecb8f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f23f1d1898a8a71186f31e7e28b63e5", "guid": "bfdfe7dc352907fc980b868725387e9886fa4c2649c6182facbfef33859adc56", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989a1b95cb9aa3abe7100e6fdbb1330938", "guid": "bfdfe7dc352907fc980b868725387e987fa7270ca47c37c240082242cc45900b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98723493f1f02b2917d8b5834011642934", "guid": "bfdfe7dc352907fc980b868725387e984615e1c05e24fc9cae10f989f74407a8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9836e5c5de74a8cbf54f25a688fa033922", "guid": "bfdfe7dc352907fc980b868725387e9827db646088e825d417faa1d543bc2444"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982ba541ed57542b9c8de65ece721f2a07", "guid": "bfdfe7dc352907fc980b868725387e98aef3589fdd6a9a052a512ca56e81616a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9831720f7f2b4be6741afcf829cff26e4d", "guid": "bfdfe7dc352907fc980b868725387e98d6b469ab1ae5e6fdfea27f91ed3795bc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985de2aaf0f0ce9b19f0b5140b545ed750", "guid": "bfdfe7dc352907fc980b868725387e987d20ea405c922c62d9b1f7d4fc0dc472", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98031094f27f9eb342c3c1280cb7c7fe5f", "guid": "bfdfe7dc352907fc980b868725387e98d2d0a36d8106ed1b8c71641ad5b7789c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984186ff7e1c03749da6a2d69f3fabfdd6", "guid": "bfdfe7dc352907fc980b868725387e98da260638f02d0d03cda30cdb2aaecfbb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98578fb52c4161c2412bd86290398fb4ae", "guid": "bfdfe7dc352907fc980b868725387e9881c753a94568b57174328b8faade39ea", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae2d6d5fab6d09908b8fa098e9841cc7", "guid": "bfdfe7dc352907fc980b868725387e98d3209fd07e0ba45d033c9561d3c472d6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9848793a73a25b597757ec103bbb607ecf", "guid": "bfdfe7dc352907fc980b868725387e982ff70b7b911992ea394355203df5aaf4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eefa1605b11f06116aeb7c9dfa53f8cc", "guid": "bfdfe7dc352907fc980b868725387e98840cd54825e4471f33ff3e207f9f5911"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e6f508cb47c6ef934f946de26648b4ee", "guid": "bfdfe7dc352907fc980b868725387e9854b5d562860387c93a403274de304a8a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c736832d72f2219f74f0c44c9cf461e", "guid": "bfdfe7dc352907fc980b868725387e98bd9ca5a7ca5668ab3c6191497664516b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b7c0918069571ba1b6561195045af037", "guid": "bfdfe7dc352907fc980b868725387e9807ec038f17ee5088e0e6c4d75271372b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984a4bd927e5948ef431905558bee1bc74", "guid": "bfdfe7dc352907fc980b868725387e98e5008a8db6109795715a08bcce9f6f62"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d80f8a3f64876793ee47d3369477a23d", "guid": "bfdfe7dc352907fc980b868725387e98c54883d0c748701ccc479dbd5c8d3a7f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9829f0f1b328b491fcca2d56df481c45f6", "guid": "bfdfe7dc352907fc980b868725387e98f8dcba0b26737d69def25dda8880d735"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cac6c1b9211e409fd32714e5b3aca6f2", "guid": "bfdfe7dc352907fc980b868725387e9878d989bdcadf5f62f13cae1b425d488a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ff8ff5d233a60d05fcfd45c922da9505", "guid": "bfdfe7dc352907fc980b868725387e9890de3a78f7f2ced0c1da5286d8a4bbd5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a12e179566f336daa7ecf2478f51d3c", "guid": "bfdfe7dc352907fc980b868725387e98b791b8efa09ed2958b87f758290d0236"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb42a3835fc44a50a2e61b2c33d07e2b", "guid": "bfdfe7dc352907fc980b868725387e98aff799a9052bc7cee2bd04a4bfe473a5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985052f875f6d3de490d9ee5a997fb6226", "guid": "bfdfe7dc352907fc980b868725387e98c5661a2b05f9fc940ffefb65a56fac9b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c0af286f40c597e0f577ae71ce1a40f9", "guid": "bfdfe7dc352907fc980b868725387e980f5f5b2664599484db51976807400757"}], "guid": "bfdfe7dc352907fc980b868725387e98e9ea43e88df81fccf3c8a3cb294db69a", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a607c14e0c7516051711886594d0622e", "guid": "bfdfe7dc352907fc980b868725387e986ed0a5cbfac4a08fe89cb1d190390196"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981568b7f8026790fb4ea3f2dd912633d1", "guid": "bfdfe7dc352907fc980b868725387e98abc281e958df93bf0e994420e72960be"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9880e056c61429ac8f2fd0f2f3dafc3c04", "guid": "bfdfe7dc352907fc980b868725387e98b89ddd116d3dc82a1de3e62fa03d11d1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f4e3fccb25690809cb93b62fe6135d63", "guid": "bfdfe7dc352907fc980b868725387e989cd3ac1171cfaf9fe72d9202fee8c8d3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9814c4b93c4690844332486c482ef62b57", "guid": "bfdfe7dc352907fc980b868725387e98179f01898f67803af445f32387f79ab8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cde0d10644c917cc03e7c54bc0d96e7d", "guid": "bfdfe7dc352907fc980b868725387e9870d537856d578228298887c714ffe237"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987cc9cc32c204410ea3b6bc780af109ff", "guid": "bfdfe7dc352907fc980b868725387e98de0684e2651f5f3317043ad50c5bf410"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988067f69d213e752d5f2f682d4d7153d1", "guid": "bfdfe7dc352907fc980b868725387e98044612ac0d858a8f2acc2de459892052"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983abb44791dd477ee5e4bc940091c1845", "guid": "bfdfe7dc352907fc980b868725387e98bfc6c64781493f7ffd4260e024e7bbc7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ecd61207c47307b5ad0ec7b121b5048f", "guid": "bfdfe7dc352907fc980b868725387e9811ef61462a53c82ade5d5bb732ac2fe0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981060315f71340973a2c6a829ddda2d06", "guid": "bfdfe7dc352907fc980b868725387e98559896f729d990d229646a2b31e8c765"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e052dfc2b306055ccc005117d913e6e3", "guid": "bfdfe7dc352907fc980b868725387e9812fc943b8348b42091e63560f2cf45f0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9881e9a4e6a567bb20e14bfd9e3a706115", "guid": "bfdfe7dc352907fc980b868725387e98fad6d64d9f0416b9eb3dc5074a6cc822"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983fbb2a12ef914214d7059f73658d2618", "guid": "bfdfe7dc352907fc980b868725387e987c592b5aa3e3fd9731218bc80c719d51"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98729096c358129f45d0feb88695865203", "guid": "bfdfe7dc352907fc980b868725387e9864b3eb7df7171195b01e45659d1c18c1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987155b5a06f48d4b6e347711aa948fa41", "guid": "bfdfe7dc352907fc980b868725387e98ba80b21dc4544bc21396c0fb2c2df729"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a5f35588431d004950d6cbd01b57ad3", "guid": "bfdfe7dc352907fc980b868725387e982a1fa594972a0585c6a13c1377e3ea6e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f23f7c3f0958703847ba94a70bfa4a42", "guid": "bfdfe7dc352907fc980b868725387e9838a85381d5af7a5738b2d3dcca9c8105"}], "guid": "bfdfe7dc352907fc980b868725387e984ceab5e22439e172ad9e24b83996a1b6", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9824f5d6ec5088eecbb83955c855580732", "guid": "bfdfe7dc352907fc980b868725387e98f217a9263a6d20bbffa9c892bded2224"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ee33ff2b9f8b1d223313a7a24cd5501", "guid": "bfdfe7dc352907fc980b868725387e98337a42ed891f83c6b58ae8646648be72"}], "guid": "bfdfe7dc352907fc980b868725387e9808a12fc16424801357d2fa267202c50d", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9873332da9f84b255c0d16228f588bf11f", "targetReference": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc"}], "guid": "bfdfe7dc352907fc980b868725387e98637b8f2f96958a95945ef886988bdc0a", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc", "name": "FirebaseInstallations-FirebaseInstallations_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}], "guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9860819b8e327bf41b291e92315614a812", "name": "FirebaseInstallations.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}