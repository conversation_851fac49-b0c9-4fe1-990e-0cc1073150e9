{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98733e5f1864022b704aa5dea890310033", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9854278478f7a9750e8f3242438fb3fbb2", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9843b53bebe6db1496fbb9c2dbdf3f1731", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f9e1b3aea9a89be247514e2b61c64f0c", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9843b53bebe6db1496fbb9c2dbdf3f1731", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e986a9c304185a04e151c515b5799b5f7b5", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98586ae4730266e6532a50a361e451b045", "guid": "bfdfe7dc352907fc980b868725387e9895e724a88c3ac120c803667b1cec0dcb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d3b906a1f2aed1d531b86732230dc7b7", "guid": "bfdfe7dc352907fc980b868725387e98900ee94b40d59667029a2937a5b3c212", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980680473120be1647015ef87c9ff789f5", "guid": "bfdfe7dc352907fc980b868725387e9819917952a62bce927fa6391dec63e626"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982b6afa434b512900ebb431ce334460b2", "guid": "bfdfe7dc352907fc980b868725387e985b9c9de09f160d03475513fc84342192", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c45731e90eea40be4c95f06a4b7b0f95", "guid": "bfdfe7dc352907fc980b868725387e98220b87c303fc4477986824e9ad944887", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98578f4922e9153b11fc65d880af676454", "guid": "bfdfe7dc352907fc980b868725387e98ddd63479b4cba71efd90cfa6c4f02215", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9844215a1c4223d215e30a01ba72735794", "guid": "bfdfe7dc352907fc980b868725387e986f22e96995750c51bc2c0c1a2771eb7c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98841d8de44f35b36912bb70b7853f863f", "guid": "bfdfe7dc352907fc980b868725387e98da03540ba7bdfbd417f796e3f124b6d9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984f72c07adfa4f8499b3036cf1d3d9db6", "guid": "bfdfe7dc352907fc980b868725387e98e3800ed4ddba525931655e8ea599b373"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b648167867ed02329c744d1b80207a63", "guid": "bfdfe7dc352907fc980b868725387e982a94272f1a461d97b9e3842c17978fcc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c9aaa1dfccdeb9364523b615d15ccbcc", "guid": "bfdfe7dc352907fc980b868725387e987a26b1626eab23baa252299288258187", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985f067bc54d022a5defb40a66b70111f3", "guid": "bfdfe7dc352907fc980b868725387e98e96581b6dcb1a20993f90243faa84e0c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98379c223404463368c5692e2bd1c01368", "guid": "bfdfe7dc352907fc980b868725387e9847a423810c241ec6eb2ff500dd50efe5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a0ad154450bff91505ab1f48eef99da6", "guid": "bfdfe7dc352907fc980b868725387e98b1405bad91b2d107433e9361febaaced", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f2fc0088e54ad030a1b3d2837cb1e19", "guid": "bfdfe7dc352907fc980b868725387e98e629a955e740a01975e88d79d3c7042b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98405fdecb2d84b78a78e1b1181b6761eb", "guid": "bfdfe7dc352907fc980b868725387e984c7b6c081bd6a582e73513bcaf5e4246", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c023c1ba82658d37494414dec72e8da", "guid": "bfdfe7dc352907fc980b868725387e9881f757d55b8edadba01adb8ba46dcea6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9827492b1d6245468f134706227ad5caac", "guid": "bfdfe7dc352907fc980b868725387e983bab240395c9f92250a90865a03d977f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9890af78648245603e0d2d74a37ceff744", "guid": "bfdfe7dc352907fc980b868725387e987734d9755b20579008bc0a8a07e3d690", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9893ada2f3b709307b8b2bbdac40c02e0a", "guid": "bfdfe7dc352907fc980b868725387e981ac6d4ef3e478be7c57228bb859ab9a3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981da23cf00d8eb7566faee23c7898ca26", "guid": "bfdfe7dc352907fc980b868725387e9838c6dace300122a32c0b7a4acc52b64a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b470afe3ed3fa1c0eac42f02462ee869", "guid": "bfdfe7dc352907fc980b868725387e98b5f6cea1df84aa6919df1510571cfc98"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d77806e941c84ad25c34b28450e93d76", "guid": "bfdfe7dc352907fc980b868725387e9857eb8b0491a0fa187751d690ac589d9d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eb7b6a6b6d4240c8795391c5393a8ac3", "guid": "bfdfe7dc352907fc980b868725387e9828d10865887c6cc61968cb13e77c82fe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98048871e48a6dd4288e9408d8fecd5ec4", "guid": "bfdfe7dc352907fc980b868725387e98cb20389c02cac4b9ecaf4c8565c7b63c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98375fe46a77cfdc1e29513c5cf09430e0", "guid": "bfdfe7dc352907fc980b868725387e98e02b28168356dadc27c276aacfe6b5e7"}], "guid": "bfdfe7dc352907fc980b868725387e981cb994885bd4e007a7605b878187b071", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98bc85f0f686cfaa2f5223d45058e6d435", "guid": "bfdfe7dc352907fc980b868725387e985b690657c1d00eb2d42b9e6d7f67ed01"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae0f42f4456d843d443ae219ec4ccaf1", "guid": "bfdfe7dc352907fc980b868725387e983ab038759aacdd97ed1bf742da6ba95e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c93e759305bf7ac10afea64e6cc5179f", "guid": "bfdfe7dc352907fc980b868725387e98102355e8671bb1445906cbd12955d1b7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eecc46c42a12e0186788d4999ae5db2c", "guid": "bfdfe7dc352907fc980b868725387e98ee930a0477b4d786a8c16ec66ff64f23"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f53fb485be8ba5b5939e6d11b30c70ff", "guid": "bfdfe7dc352907fc980b868725387e987ec8bf264ed60a9cc5f0f807d6c4211a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ecc1f872fc14b375fe98c046c100f376", "guid": "bfdfe7dc352907fc980b868725387e98f4c9a3b257365290d314d5306f410f41"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985849358f8d5611e4a6689983983e169d", "guid": "bfdfe7dc352907fc980b868725387e98b5ee352b77adab0eaddc32628dfb54cd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9814f8e95cdb5fbe605e3e6d72277eb465", "guid": "bfdfe7dc352907fc980b868725387e985d08d2b81f9e82278af5d5ce41bde20c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98258cf753a31bb1ee791c4c4fa64bfac7", "guid": "bfdfe7dc352907fc980b868725387e98d7bdc81061e8b1891bf227577b88d0e8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989e16bb80403635e2a9883787f34eae3d", "guid": "bfdfe7dc352907fc980b868725387e986ad32bade5927bd187371664f7c2f3bb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821bde6ecb73d02564dbde4c65d1aa5cf", "guid": "bfdfe7dc352907fc980b868725387e9853fc72d5e8a9fa1d9724cb89f6e133a6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982c97a42854e13172141379443431b676", "guid": "bfdfe7dc352907fc980b868725387e98282099aff363651d0ba71b21f66f04e1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980c7f0074e41de7dd89a01b8fa0328e84", "guid": "bfdfe7dc352907fc980b868725387e98f83aa6b00fa75556d8af2de12701ba9a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e1084f537403fa802b640285ada5c968", "guid": "bfdfe7dc352907fc980b868725387e98e3af06a8e0b6f2781d43c7234f432696"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a52a1b105a2a11cce8489164e8a55d1e", "guid": "bfdfe7dc352907fc980b868725387e9893760b28c9b975d853daec4fc122f745"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9804958b40ff054fd2ab48e0cc3968d786", "guid": "bfdfe7dc352907fc980b868725387e9838ff3463ed7e4110db14807c96d77065"}], "guid": "bfdfe7dc352907fc980b868725387e989cdb808be5fa24645351ba75f84ec695", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9824f5d6ec5088eecbb83955c855580732", "guid": "bfdfe7dc352907fc980b868725387e98120514a2bd2282cc70ca2cbd74d6acc5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ee33ff2b9f8b1d223313a7a24cd5501", "guid": "bfdfe7dc352907fc980b868725387e98d136cfd7e0e4fa721230ef1eab5bafde"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987cbcfbbfed448f33604b899a4f76dac9", "guid": "bfdfe7dc352907fc980b868725387e98a06c925899523f45ca2fe5bc7eb11821"}], "guid": "bfdfe7dc352907fc980b868725387e9883521bcbe835b0d4194fc2f37118f64d", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98bc1c1ec66c3d691283ba207ce0bbc663", "targetReference": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4"}], "guid": "bfdfe7dc352907fc980b868725387e989cecc0badc5ee06cd49fc74a5aad3727", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4", "name": "GoogleUtilities-GoogleUtilities_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ca49ca851f2777b997a3e74ccb860358", "name": "GoogleUtilities.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}