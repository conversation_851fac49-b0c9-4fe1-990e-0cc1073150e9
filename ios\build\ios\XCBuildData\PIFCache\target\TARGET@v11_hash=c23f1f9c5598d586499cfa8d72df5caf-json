{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9867ce52f2c0faa18bf83199c870608de7", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/geolocator_apple/geolocator_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/geolocator_apple/geolocator_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/geolocator_apple/geolocator_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "geolocator_apple", "PRODUCT_NAME": "geolocator_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984936d7cb8feef48513f6131e211caece", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b350e68dc27ee21035c79cf564fc7ae0", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/geolocator_apple/geolocator_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/geolocator_apple/geolocator_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/geolocator_apple/geolocator_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "geolocator_apple", "PRODUCT_NAME": "geolocator_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98beaaf634ac9c45a28ea8b53ae8cea921", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b350e68dc27ee21035c79cf564fc7ae0", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/geolocator_apple/geolocator_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/geolocator_apple/geolocator_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/geolocator_apple/geolocator_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "geolocator_apple", "PRODUCT_NAME": "geolocator_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9879f34f1e012c285b844492b857411475", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b094bbc54cd634e3c37cb7abad3d67ae", "guid": "bfdfe7dc352907fc980b868725387e988bda4932df5efc2b6a23aa92871c4b12", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98758cfc51136a4c74b56403b8ff90efb9", "guid": "bfdfe7dc352907fc980b868725387e985c2cd1a6303951e59c109455499dc286", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dff761050fde745860af0ffd4084a0ba", "guid": "bfdfe7dc352907fc980b868725387e98304978ba378245c1bf41bc896be49369", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9848e8126fcf225d54500e6ffd7688878f", "guid": "bfdfe7dc352907fc980b868725387e9862938a8134c50d095589911c806ba66e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cd0ea74eee71c1d22ee23c8cda15b8af", "guid": "bfdfe7dc352907fc980b868725387e9865c32d2826fc3692b75d9927408d1d16", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cabb34d8b9f0b19567aada04a9f55774", "guid": "bfdfe7dc352907fc980b868725387e98d1e124a352bf99529f75a1c43ffc8a79", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98545f3aa48d2e0c904f3fe8f7f88bbfe7", "guid": "bfdfe7dc352907fc980b868725387e98d9f4fae6ed180ee1af3380a3e1ee4db1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b7792109ac1c99bfcfe8cc99d67f983b", "guid": "bfdfe7dc352907fc980b868725387e98518c0c2319a816567d27c1dbfe6aa2b7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985d3e5b53fc00652896b841c6c719fa6e", "guid": "bfdfe7dc352907fc980b868725387e988460fbb3a6eddf301d0ef1bdb99529b0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b0e5a30ce7aa9bb2a70b3f328a43ce46", "guid": "bfdfe7dc352907fc980b868725387e9818e9c2019b2227cd283caebfeb9f06cc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98847b02cd479e163e3188660241462e83", "guid": "bfdfe7dc352907fc980b868725387e9818a1f20123ec6bcc625a0c2862fa2fce", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c3589d8f56a3bf2789af69e3cbdda774", "guid": "bfdfe7dc352907fc980b868725387e982c6bf0bb399a42ffd1ebaf4bf336f9bc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987056e51f11bb8f999339ad8bd4b89dba", "guid": "bfdfe7dc352907fc980b868725387e983a0b186130f1fecb9cd54d64f962df09", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f6dcf6c6c1fc99ae3fda5f31758289d5", "guid": "bfdfe7dc352907fc980b868725387e9863c36fd4154d637e6c6e8f0b47a48d35", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9874bfa01938900b563904d84d0a5e1885", "guid": "bfdfe7dc352907fc980b868725387e98c64c18dcedee9db98f766e1cac385d96", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984b3a1583eb05f55f0f2e8cb28ea842ba", "guid": "bfdfe7dc352907fc980b868725387e988cae0e18566c3c196d1160178a15a048", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9833069fc857c960cf7415929d70440d92", "guid": "bfdfe7dc352907fc980b868725387e98bc7f02db8393aee452d19da7caa29724", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e986576ae2c21c60200c6d4fa060d500570", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98fbd0ea511110b960c091399a44794fd0", "guid": "bfdfe7dc352907fc980b868725387e988b116d05e3db59a404f01b0d7efcd373"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eb591a34585e93d2eab60ee284b0877f", "guid": "bfdfe7dc352907fc980b868725387e980109ff862403f55b2c30930af05b7977"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ee0f9d8952695cef0b20043cf97ea4f6", "guid": "bfdfe7dc352907fc980b868725387e98d27fba0427cb118d978aa71f2241d9af"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d0f84450fdb7994312cedb77951ce8d2", "guid": "bfdfe7dc352907fc980b868725387e987163553222304b0f095d5c026ae7ad21"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c4ef0dc85b94f004d13a836540a17a8b", "guid": "bfdfe7dc352907fc980b868725387e98d9f834483786fc0c92f7593bcd6dd83c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b71398b326cf9cb300b21ada143301d", "guid": "bfdfe7dc352907fc980b868725387e98deeac623a228ba2c5026f05da86b7137"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d0bf15e7853ce7ffb586ff88819cf986", "guid": "bfdfe7dc352907fc980b868725387e98c85978fa1a5664714c48471cdab2a44f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c114a08f23a13e88d26a4590310108e6", "guid": "bfdfe7dc352907fc980b868725387e983b8a93c5a3b80220ac53879f2ca4bd2c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98503fe3ca84ed6f09caf40050ad460aa5", "guid": "bfdfe7dc352907fc980b868725387e98f8c12a58c55123cb0abe5a18ce030c24"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c9b8f16c3d59d01c75b364bc2ff2592e", "guid": "bfdfe7dc352907fc980b868725387e981efeb6243929d10aa69d3bede69d297e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988423969186796d4f4579acbe420192a9", "guid": "bfdfe7dc352907fc980b868725387e98f9f6196685860748c800da186ea77b74"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d32ec9dfdf8b467b012165b56d87aefb", "guid": "bfdfe7dc352907fc980b868725387e984f14c0d4ca36de08cf4bb84348e73b16"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eb0f2d0f7f2e4881b5ea7f9cc2bbbd5a", "guid": "bfdfe7dc352907fc980b868725387e986856c8e7b83dc7bd367daf2666e5be72"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c39a1e1eb74cb4a1ff9600985d5a43ab", "guid": "bfdfe7dc352907fc980b868725387e98a2a8518814ae13c9fd76091e38db98a9"}], "guid": "bfdfe7dc352907fc980b868725387e989d91e5abbad637a65716b7fef9466ccd", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9824f5d6ec5088eecbb83955c855580732", "guid": "bfdfe7dc352907fc980b868725387e9847d3b3fdbf8c6c03a84819a79f7ae5aa"}], "guid": "bfdfe7dc352907fc980b868725387e988ec00323e57392a3c283b014e048e33b", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98c4b351c20ac090098b0dcaf5fd3c18e2", "targetReference": "bfdfe7dc352907fc980b868725387e98e1aba8ff8dc833f2269ce0a7182533b3"}], "guid": "bfdfe7dc352907fc980b868725387e98053f546dc39b1661db608be4eb007f0a", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98e1aba8ff8dc833f2269ce0a7182533b3", "name": "geolocator_apple-geolocator_apple_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e9821d372cc1e7c7587a12aeda843619e39", "name": "geolocator_apple", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e986ff8f87e011522b1b6328c84d9533927", "name": "geolocator_apple.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}