{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d69224056ede8467aec02a17fdcdce77", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_inappwebview_ios/flutter_inappwebview_ios-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_inappwebview_ios/flutter_inappwebview_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_inappwebview_ios/flutter_inappwebview_ios.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_inappwebview_ios", "PRODUCT_NAME": "flutter_inappwebview_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b89882dc28a6d5c860523e82f554cc84", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982d2047119e686b55eab58042460b0cf2", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_inappwebview_ios/flutter_inappwebview_ios-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_inappwebview_ios/flutter_inappwebview_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_inappwebview_ios/flutter_inappwebview_ios.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_inappwebview_ios", "PRODUCT_NAME": "flutter_inappwebview_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e29cd749ce03643e9abbafc9d7ee4c45", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982d2047119e686b55eab58042460b0cf2", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_inappwebview_ios/flutter_inappwebview_ios-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_inappwebview_ios/flutter_inappwebview_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_inappwebview_ios/flutter_inappwebview_ios.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_inappwebview_ios", "PRODUCT_NAME": "flutter_inappwebview_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e982fd3c6a7f4d84d77d3348746052d187a", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9855f19e326418d0a94d90e69bf120ac79", "guid": "bfdfe7dc352907fc980b868725387e98807bebc10b9b2347b3c38ab76dcd39c8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986937ad994cdffabf63c5f955a7dc415d", "guid": "bfdfe7dc352907fc980b868725387e98830df83e0ec997753ec519538ef6f60c", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98e2f978f0bcd85da9c3f3f0ea66389fce", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98564d55e447939c20fec8655e104b1820", "guid": "bfdfe7dc352907fc980b868725387e98ccdfea99e0cc660b9001c8a5d5adb33a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987c520fba7d7d43560cd94141f16f55d9", "guid": "bfdfe7dc352907fc980b868725387e98dc5de76e264055f43c861d8493e01121"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98360075b2e0e6b19580ae72d725369912", "guid": "bfdfe7dc352907fc980b868725387e9871791bf8d2f68b9f58e2fe1d470f4886"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980223638eaed6a2d1a8b5e825ff0ca7c4", "guid": "bfdfe7dc352907fc980b868725387e98250472815ebabfbe2170b8bbf902db8f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98071b6487df817ec3c0b95095d6b3c62f", "guid": "bfdfe7dc352907fc980b868725387e9875bed1f957181399dbba774b75f2fadd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9869f9db8d816e2be3fd1936e7e86db298", "guid": "bfdfe7dc352907fc980b868725387e9883888c5b2799e1a0437df6bc3e5fbba7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989515c1fdbc920fced87de297c77b4d23", "guid": "bfdfe7dc352907fc980b868725387e98e6c0000aa9967ac168d5c88601b76b16"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f698642a19f691febb79ce0cac593f63", "guid": "bfdfe7dc352907fc980b868725387e9808eca2f1587502e4fb0253dd70590083"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981344444aea5aa2d21f9e09d8877a9eda", "guid": "bfdfe7dc352907fc980b868725387e98a72d821a485fdcedd26adedfb0a2b23d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9895bc54386a9f4e2f25da24f270a1273b", "guid": "bfdfe7dc352907fc980b868725387e98ca4096acac43a317bda5ba0c47bb8bda"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d60e25ab116f8254979baad846873eaa", "guid": "bfdfe7dc352907fc980b868725387e982ad692615143ac287df3354fda01c162"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984651b3c612be5a02046b10e99fb67db3", "guid": "bfdfe7dc352907fc980b868725387e9864b3dbf6d6ced697454ea174127f7c6a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f3c74a2b75276cbd4269f9a10a4b20a1", "guid": "bfdfe7dc352907fc980b868725387e986fa224351bf07d506906474ad5f24fc3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989f088625bcb3e0af64553c17f688d6e0", "guid": "bfdfe7dc352907fc980b868725387e980c4b58a8297e745a2a277b5c4d9247fc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9873805a20aea08b593ef7356409c09f4d", "guid": "bfdfe7dc352907fc980b868725387e9812491126bbc5062ff02880e7b428066a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986b6c97585134223fd7b2f6fc4c109f0e", "guid": "bfdfe7dc352907fc980b868725387e987c7759cedb074971adacad0051eaa2c5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b31c5fcfd48f9c04c512039992d28955", "guid": "bfdfe7dc352907fc980b868725387e98ded215c4320567521f37c44a3f5f8d43"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98039d7d6376f07304ceb024ffc8504ce7", "guid": "bfdfe7dc352907fc980b868725387e98d1f5a1d290b761132be798590f0e63cb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e224b91d76c952d2f044cfcf4879a20d", "guid": "bfdfe7dc352907fc980b868725387e982fca89ef90efb3c5d03b926ccf48972b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980be2f4dc7ede490e0fc96647986d269f", "guid": "bfdfe7dc352907fc980b868725387e98724a5382285ba30611c6c80c7223a6cf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9812991e02f549b6ea3ab46e8edcc27d52", "guid": "bfdfe7dc352907fc980b868725387e98314e1d0592601eec0305df273487779b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984f3f6bbe4078cc99468932ade3d42128", "guid": "bfdfe7dc352907fc980b868725387e982da34c67b6738b27c0b8164aa29b2f6b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98344ce4ef0bc9c4eb3c19a33b08c8e082", "guid": "bfdfe7dc352907fc980b868725387e983f12e6082182025f7e041ef080820aca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988943d1eac5f1932968f9ff79cb29f250", "guid": "bfdfe7dc352907fc980b868725387e98cbe2590344c31ed3a21c3773426e3fe4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98adcdd116a586fd0b27334f1d1977aba2", "guid": "bfdfe7dc352907fc980b868725387e98b35df1c3f81bed4424b8b446948a1c70"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98204b9c4a85cd9efae233aa1f85006f30", "guid": "bfdfe7dc352907fc980b868725387e989eb0d23d507b408da8575a7e972a2cf2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981719ef568ea1932fd46503fdc6418a27", "guid": "bfdfe7dc352907fc980b868725387e985359bf3f299bad8a26efcc1796d7c1cd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c037c0c1cc0483f6fd920d0fca78ea5", "guid": "bfdfe7dc352907fc980b868725387e98462265d7e73c937aba0c1a0c7a05f7e6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9862bee9b6247e84f21f4899a9970f4098", "guid": "bfdfe7dc352907fc980b868725387e989cc343ddd199dcedcf3100c93f46534f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fac27e6faef8fa704c698b734c246ac3", "guid": "bfdfe7dc352907fc980b868725387e98ab5fcee13c8c4444d60bddb6a295be5e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e5c20922b3d00f4ad7243d875b8d752b", "guid": "bfdfe7dc352907fc980b868725387e981f3185aeafddcb4f6ad2369d0f8b8b8a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98469ef6e78396fdd649809eca316c7a47", "guid": "bfdfe7dc352907fc980b868725387e988129f147513ddb8d2cd36defbe96ac9b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988aeec665c005c7cec4e681a1da9a1dcb", "guid": "bfdfe7dc352907fc980b868725387e98a160350763ead235ae8e93e475392e1f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b2e3f07d68758dfd91638284fe72c33e", "guid": "bfdfe7dc352907fc980b868725387e98164e73f85bbfb94e6fd4ff8e92faf005"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe8d5c6444f8e48a7438199c3400cee5", "guid": "bfdfe7dc352907fc980b868725387e982ca5c8128c4301080ce2ab76a87de2a5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98df5f440bdde19edfaf393273ccb13f85", "guid": "bfdfe7dc352907fc980b868725387e98a3aa5c4326493008dfe5627384906f85"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984aa01e2ccfa138bbe49ec54416cf4466", "guid": "bfdfe7dc352907fc980b868725387e983ef3a1fa9beea16913e59206e92a27fa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98804555438627e2f20ae246762c56a4d8", "guid": "bfdfe7dc352907fc980b868725387e9894d4fc4cdf98039d9ddf9524d12d795d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984ddbdd9844fa5081658f535b757bf85b", "guid": "bfdfe7dc352907fc980b868725387e98db847b3345cc1944e6e7659ddef1e272"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c7468dd499f4dcbbef20c8ca19c47659", "guid": "bfdfe7dc352907fc980b868725387e98ce1c4d48c89aab4d580616508036dd9d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987cbd9adb73bf465ec730e73637781f29", "guid": "bfdfe7dc352907fc980b868725387e9819971fed787079059c7146de03897424"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9818e24e158ce7c248f2cc666b6a822dd0", "guid": "bfdfe7dc352907fc980b868725387e985694432eba2505c3088b40c4577e6121"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9838a6302249445f116f2ecae961cc74a5", "guid": "bfdfe7dc352907fc980b868725387e98e100eee6ab91604739cd83bc225305cd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f0d320a60ce2f371ef3a27b15f269945", "guid": "bfdfe7dc352907fc980b868725387e98f05e2e6f5f20f2097468207b2b08f057"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985a9c55f0d27cb46d339994d22e8d9a5a", "guid": "bfdfe7dc352907fc980b868725387e9829d94119edcc321aea742c339acb7459"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f6db641614898dc03fb063b03b026afd", "guid": "bfdfe7dc352907fc980b868725387e980facc7b14738d485db14bdf99debf547"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983d6b44a62a75d9288c0689481b80e9e1", "guid": "bfdfe7dc352907fc980b868725387e9875aab928f91efc50bca0b3a4b53323ed"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98639686cf59e7ee9c5242ec233349dee4", "guid": "bfdfe7dc352907fc980b868725387e98cdd19bdba7752c5cd9ec9adbc95dfd75"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bda118d6e906513db88eb51ef8a08275", "guid": "bfdfe7dc352907fc980b868725387e98e5de14b5482be462d4bb44c08a0b4e92"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ccd431733cb24517c911d5a0498a574e", "guid": "bfdfe7dc352907fc980b868725387e983cea68a78c71ced38e5925a70d2e4d0a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98987bcd09dda3fae6f2aa4b28f92e7b0b", "guid": "bfdfe7dc352907fc980b868725387e98811167c16fb4f59c13d4f45c4b0bf823"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b497ba673ae1cf273f543a0dddb63b40", "guid": "bfdfe7dc352907fc980b868725387e982fae6087faf90913c54ca9ba872a78b1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c27ee8e198c4c33222d256136ff754c5", "guid": "bfdfe7dc352907fc980b868725387e981dda166fea1d00755baa7c2e29f91792"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c1b0510efd2c9604780f921dcf5c901", "guid": "bfdfe7dc352907fc980b868725387e98c27886c8f0e54a0a2e68beffe517c4c0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98858e1bb09ef36523026646ee4fb9c8a5", "guid": "bfdfe7dc352907fc980b868725387e989da49c25cceb0f746dc7b6d52a264248"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982977cde790fa83b9c3f10d493f0e81b4", "guid": "bfdfe7dc352907fc980b868725387e98c2daa5cb5599c651b6c709acea822f42"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9843e9f621ab3ddf0a79fcbfef0e0680fd", "guid": "bfdfe7dc352907fc980b868725387e98f995a9f501a217d614b8f4b2cecf3390"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98575389661dcdcf9120bf95df0385020d", "guid": "bfdfe7dc352907fc980b868725387e98d2ff23ba143638be7746ca5b910f7369"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b2ee4937e83ad5d0d6b02582bc98f4b1", "guid": "bfdfe7dc352907fc980b868725387e98bbffa023bc2ad159c246eb78792c3849"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f8b0e6f84f446ae317bc48f07af38cfa", "guid": "bfdfe7dc352907fc980b868725387e98aa8696a741ccd4b6741719a926fea194"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983d579bdfa39d645321cee63270b3f424", "guid": "bfdfe7dc352907fc980b868725387e980b0a11f669d8564f8815d6d3a179b046"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984f5b88e4d85e8bb028ccdd72f935617a", "guid": "bfdfe7dc352907fc980b868725387e98ce47c641f0458341bf50d7b25f1c6a2f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fa89f5776fb05a056cde2fdd8e71665b", "guid": "bfdfe7dc352907fc980b868725387e98f9e52fc7467b0174f0e055e0eba47775"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a0089f912d2db6db726615b6c9d5b4b0", "guid": "bfdfe7dc352907fc980b868725387e98d2b0721788df1c5d87ce43d2ad84dff9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9872a726d81c1c139dbca07a0eaeb1c29b", "guid": "bfdfe7dc352907fc980b868725387e98a22b91bc30195e74d25116f730383a0f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c8d123d42abea5fe47e26a1db8bbec3", "guid": "bfdfe7dc352907fc980b868725387e98cf64948e2e73b70435172d32ba723704"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985bb577231d39b76857a3be2ec80f14bc", "guid": "bfdfe7dc352907fc980b868725387e981ad231c682034a6053a672fe7e0b2c42"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d7b46df33717116cc53594fa67852a88", "guid": "bfdfe7dc352907fc980b868725387e989ccdfd00db0fd04284ddf335fafa86f5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987895503765c20fc53c88cc405814a3d7", "guid": "bfdfe7dc352907fc980b868725387e986aa0ed2968449bb085643a863f0937ad"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9878783c6f14baf06613269a1e9a3a7f2c", "guid": "bfdfe7dc352907fc980b868725387e98e588ceaedef830278f4fcdfa03d515c1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985f81bf3c0b0bbcc3556bf1f64ba32887", "guid": "bfdfe7dc352907fc980b868725387e98fc667a1b02350a9be46c6509f23aef81"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98415ab36a14a47494a5a9f5d9941ed50a", "guid": "bfdfe7dc352907fc980b868725387e98162e2a5689a129dba61a30062c656c93"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98935d7f013ecb21409678927571e7e91f", "guid": "bfdfe7dc352907fc980b868725387e9874c29c73a36beaebd65b0cb0fb9225af"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9899417f97a789d40829da96b4c2896bb2", "guid": "bfdfe7dc352907fc980b868725387e98d432c3f46b28ab7c360d9d055f1a6ac9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986476859c669e4f8a272af134880cb750", "guid": "bfdfe7dc352907fc980b868725387e98a94054347508856afb9f5fd629bd1851"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9892755a505f0222d7717e372c81f213e9", "guid": "bfdfe7dc352907fc980b868725387e98eeaa4a3d7df0ff0514f95cf919a6efc0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98449c4c4c8534c19e3fe0242c68460a17", "guid": "bfdfe7dc352907fc980b868725387e985b9dbcf06d35ab48c4feae2b62c0a9b5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e41832288da91a85ea9dff7a5214949a", "guid": "bfdfe7dc352907fc980b868725387e98f0cb55ef7a1593e9205541db820b22c1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bdeb3e74a50ce819f673f06397e8e62f", "guid": "bfdfe7dc352907fc980b868725387e98193b45a979ab1a1e15db8ad8f20b7024"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9823d36f15a4fa96e849a213fe4ea95ae2", "guid": "bfdfe7dc352907fc980b868725387e98b33dff1b8addf2e809060b9f0431620b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987b6646e31f2924e3e6f21fd0c3aa5571", "guid": "bfdfe7dc352907fc980b868725387e98c37a16228db23d06e86e00dd29019c33"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c85ef5b0f0c151ce6cd44f721de4cba3", "guid": "bfdfe7dc352907fc980b868725387e981e2e1346a1633ce1ee4eb4cb55b57c7d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3f823c15318ba6fd4c42446d37a7234", "guid": "bfdfe7dc352907fc980b868725387e985d9ebef983dfa659514f8975005289dd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b57df7e021116bbb9bf56771570441d6", "guid": "bfdfe7dc352907fc980b868725387e984c9519493ccdac0b918d35fb0cf523af"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98db4ec6409bd1c03b120b6b62c3f05e58", "guid": "bfdfe7dc352907fc980b868725387e98e80213b22df3304548a02d49a2d8e3f3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9883728290e7d176482eac94068ede51d5", "guid": "bfdfe7dc352907fc980b868725387e98f08ee4bfef07c27371e0706f6620feb2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982bb9cc121bfe8fc982c24a80a8a3d7d0", "guid": "bfdfe7dc352907fc980b868725387e9875e616075cc95c8cd36fbd21fd6b0440"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989320abc57f285cb5ffc5fbbab2fce571", "guid": "bfdfe7dc352907fc980b868725387e989658d9408f6bba8d3ca6ca5e9dbc8c90"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9867cd8293190da8e6a44395f20eb694e4", "guid": "bfdfe7dc352907fc980b868725387e981a727837450fac18a64627732dc82897"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9851aaa6f6b1232f143b38c2e809fbb4a4", "guid": "bfdfe7dc352907fc980b868725387e98be4bfcca04bf0482d361a80c377ffc42"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec4e8d1d1bf28a33ce0449bd6f9dfc98", "guid": "bfdfe7dc352907fc980b868725387e9885397fa3e0a6ac3fe2c9b9c7b909826c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9820060987bc3cbfe8c76fdbf3fdef26b3", "guid": "bfdfe7dc352907fc980b868725387e98d61c6522170340c11f804d594d516906"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c813ca8ce6c81b98418d13c7adf0a53", "guid": "bfdfe7dc352907fc980b868725387e984e695630728ab22705d1b42df7f49906"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980cbe66c3a7610b0dea1f3b6d66543dad", "guid": "bfdfe7dc352907fc980b868725387e9805b0bcf4ef09ecebd888d0e223785cff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9884718b2bc56c8c4f7cefda6f8bc66a79", "guid": "bfdfe7dc352907fc980b868725387e98a601a1a8dda4277e4adcc83c0e68fe33"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98132c24933dd5878bb2fe15d3a212e553", "guid": "bfdfe7dc352907fc980b868725387e98e207a2aef02fa50a6ec65bb21c712e24"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983429bb8216ac8e45f2c737c9676dba05", "guid": "bfdfe7dc352907fc980b868725387e98472398837d89d36c21b64cc493a94a41"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ab4c95ff68f62a749828205a19ae58a9", "guid": "bfdfe7dc352907fc980b868725387e98e72d95288ea46299e61700c842e7b4da"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e78d717d74096743f3fbfa7677642aa", "guid": "bfdfe7dc352907fc980b868725387e98d0061c648c5ae49d828560fcae9a0534"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b2b507bf4c881c8fe153b858b93c6080", "guid": "bfdfe7dc352907fc980b868725387e98225b5fe7185fdcd8126bbe8d691c29ef"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a4e3b11cf3ad5c42646dc1f03e38bcac", "guid": "bfdfe7dc352907fc980b868725387e98262a5ae95bec7b8f6379fdf1257d87a8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9898c790e059cdf0138013e9d23171d4a2", "guid": "bfdfe7dc352907fc980b868725387e98e3e492a5e14907a00207c7702af8d212"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980d7c7238c980aeed1b4b213bec34a000", "guid": "bfdfe7dc352907fc980b868725387e98cda12c54392375845b2f8302144b7245"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821ba52aefc9633627fd4c32eeccf6c22", "guid": "bfdfe7dc352907fc980b868725387e98af0decb17c191742bee91d1b04138c8e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bcd272b97de678a3fbe70b4ae7b3d223", "guid": "bfdfe7dc352907fc980b868725387e98ba174d72bbb3036585382e9428ba9077"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986ff8c6f08bfb1dc8df622140429b3ace", "guid": "bfdfe7dc352907fc980b868725387e98be57fb2fa056f7111daf382f49270451"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f9ba09385ae2d74e4a4aaa46dd6f1961", "guid": "bfdfe7dc352907fc980b868725387e98c14d819d66ed9375efcc6a539b85b7ec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9804b74ef8bec856b7d0b5d7d9cd157a05", "guid": "bfdfe7dc352907fc980b868725387e98e308fbcee206f934ffb00475cc523a36"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ab2822061cea9a5246f94445875b1af8", "guid": "bfdfe7dc352907fc980b868725387e98b3132a0220e94aab07f737db82c3cfe4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a18b3fc552412058d93e8b57029d53c8", "guid": "bfdfe7dc352907fc980b868725387e988893378490c2052a8a464aa57090531f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b09f62c72a2ca9d2580c9fbffb18152", "guid": "bfdfe7dc352907fc980b868725387e98154b1d25eb1a439b8767d19ef39e1ae6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98635067cfb83ac37fbde3dc0e01564266", "guid": "bfdfe7dc352907fc980b868725387e98b0674a3442b1a2a48f3f4262ef7237b7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ce97c41252d8c0bc1c4644388f8b891", "guid": "bfdfe7dc352907fc980b868725387e98ebf7b3e19e4965170d45392782771cb7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dbad357c5f0316f8f28ae0cf7ff8c1d1", "guid": "bfdfe7dc352907fc980b868725387e98852d58f5e479ee1b5233ae013ab7002a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988150d80c840aac5b49dc9378e7e22c12", "guid": "bfdfe7dc352907fc980b868725387e981fe38c3f705316063a59f18ad70ac314"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98adf33d8721ce3f79e5ffb34bd356436d", "guid": "bfdfe7dc352907fc980b868725387e983c25e1b3ec34195ddd1a48092e7d5c26"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c69c25a5f419f5d98f1da4bacaaebeb2", "guid": "bfdfe7dc352907fc980b868725387e98a68eaafbd6e7d960af0028a8386b3330"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ff76fd8346f1614069431eb825f1f67f", "guid": "bfdfe7dc352907fc980b868725387e983f3d27c22745d72b49edccf768470f8a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ff819da342bb1c743783bcfd08ba72a0", "guid": "bfdfe7dc352907fc980b868725387e98e8f28ae119a17ae79c19b0db5ad09cbb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984efb23344c77a4d1ca211cf5d34c747f", "guid": "bfdfe7dc352907fc980b868725387e98bc6a9ba2dd8868dbc2fb45beea927d56"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981d8cc2160ba485d8302caf43a9989291", "guid": "bfdfe7dc352907fc980b868725387e98d7d69d6c5dce8ac5c9d18c643e30de06"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981039064e26636fa842d6c327b991431a", "guid": "bfdfe7dc352907fc980b868725387e98e3d136553c1a88705b7f7d040ca3a677"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b01268cb2d88fe59bdaf42b4692158e0", "guid": "bfdfe7dc352907fc980b868725387e98ee9a4010577dcfb314226dce4f361e27"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d662a79b7aaf997d42541e642f443d98", "guid": "bfdfe7dc352907fc980b868725387e98e41af1ee7dc191c44cc159e5c2478f05"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d07639b35500bb4b9820f1bb5d329f68", "guid": "bfdfe7dc352907fc980b868725387e981410763db0b310f0024e0807fdafd422"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ea924e57a45fefd3c71d73b032978821", "guid": "bfdfe7dc352907fc980b868725387e98b96aa6334ad332103ecc32edb480d0f1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985a49bfe885e4cfdf561a0bf5c1cb6d2a", "guid": "bfdfe7dc352907fc980b868725387e98693ab963407f58e1d2533433981cc8a9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9826b6daa2e0f0a652a106af59338ef9e6", "guid": "bfdfe7dc352907fc980b868725387e98530872d8b12748c7cc42dfe53eb608f3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98326237444a6b8b90dd75c2f6ab5a6919", "guid": "bfdfe7dc352907fc980b868725387e986550f864d69eb5b789ef74c2f45eaf60"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9810f3ddd2d0f8585561fae75dea03bb88", "guid": "bfdfe7dc352907fc980b868725387e984ca6c988a2f9d3d9b69702cd0b293872"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9869b4e8d560c715ec5e20015e8ffbeff6", "guid": "bfdfe7dc352907fc980b868725387e98c0aaaa1042ab55ac68bc3d509c3c0620"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a950cc7ba3114e31eca91c16b6c70baf", "guid": "bfdfe7dc352907fc980b868725387e9887ca8566a609060d63341628c7144b4b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d9f146595247e31212fd31176ae6bfbd", "guid": "bfdfe7dc352907fc980b868725387e98c83e1006198954f0fa914a878343b98c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ea58e4dd92b9041d27526aa28a1408f", "guid": "bfdfe7dc352907fc980b868725387e98315f7552f7ef320c380e76f948f8d0ed"}], "guid": "bfdfe7dc352907fc980b868725387e98cac5758aaab041b306b3055c248885f4", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9824f5d6ec5088eecbb83955c855580732", "guid": "bfdfe7dc352907fc980b868725387e98027d34dd1e1770c79dd54f5404987fd6"}], "guid": "bfdfe7dc352907fc980b868725387e98d8f70b5ff677ed02b77232961e037957", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98b7d49ad1fe66522e6eab9248dd2331d6", "targetReference": "bfdfe7dc352907fc980b868725387e98feffba4bc77d9f3d84a98c192cefdc8b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ba156c151c55dd8d9d7ae0b7acfbae4", "guid": "bfdfe7dc352907fc980b868725387e98ec5268a1792f6b5cd4753bb67be7b719"}], "guid": "bfdfe7dc352907fc980b868725387e98ba929dd1b11c60f9a4f8fd06e4eaa3e7", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e985f0ec3a68eeed5241cb87afb05bcc380", "name": "OrderedSet"}, {"guid": "bfdfe7dc352907fc980b868725387e98feffba4bc77d9f3d84a98c192cefdc8b", "name": "flutter_inappwebview_ios-flutter_inappwebview_ios_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98a562549a031aeda8bf3440b79b3420bc", "name": "flutter_inappwebview_ios", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9810acd6d3a97e7ef91b90dda5618dc5c0", "name": "flutter_inappwebview_ios.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}