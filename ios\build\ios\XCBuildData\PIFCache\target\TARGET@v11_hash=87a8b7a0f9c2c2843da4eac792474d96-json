{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987ea6d71b4fed56920588ca202b700f08", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e988b27973a59b163eb35df53fc76810542", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9892fc5747a0e7166219edb5edabfab326", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98eff71e8d39556b4cf9c8e2162935c220", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9892fc5747a0e7166219edb5edabfab326", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9894358ba46536a314cbc2f0401dcb6216", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e988b7c136b249402f1e8d0398941ce02de", "guid": "bfdfe7dc352907fc980b868725387e98248fc826ec3e709e96821d622b3fa010", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981f5086d8e60fb6eb84ffd230e4744f39", "guid": "bfdfe7dc352907fc980b868725387e983e4d443917d97790037695dcdd1ecc6f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98956a71a310de5c8679e0e4f75dea63e1", "guid": "bfdfe7dc352907fc980b868725387e985c7494bed692077ff143a01b9e38c262"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987a9b8625638f2b08fa3ddbe77cfed2c4", "guid": "bfdfe7dc352907fc980b868725387e98e6c3a752dba335e0ba2216c4ced69707", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98012a5c371f7e0de63249f8c6db45cd1a", "guid": "bfdfe7dc352907fc980b868725387e9884de4f549c5d7936e5a8e10341af40d1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9823944b8d7e69e3a6f836cf3beb0c3ed4", "guid": "bfdfe7dc352907fc980b868725387e987d05a01df54d1aac0b9d3fb810501d54", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988b2d8b3ea27b7394175becec30a6e7be", "guid": "bfdfe7dc352907fc980b868725387e9859c005fc73ef697cafe2c671b137a94a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ff52b0864dcc53eca16b4da4744d03ac", "guid": "bfdfe7dc352907fc980b868725387e98968db905c6b49244771299435c2a69d1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989cbf3742313eb26a7992d4ef632379d0", "guid": "bfdfe7dc352907fc980b868725387e98c36437af1b173ba65e5f88797cc6dbd1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985da6f4d30fffd2f8d1c3086a131d54ab", "guid": "bfdfe7dc352907fc980b868725387e98a6ffa7a7ca24a6b602e73f987607f147", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b7fceba655485c4fb34b66d4176dc2ab", "guid": "bfdfe7dc352907fc980b868725387e98bb5aab3b239dcbc1960cde995904c6aa", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989f615fbd42ba2ec1fc9848fc1127dd34", "guid": "bfdfe7dc352907fc980b868725387e9822cd9f5b6686a50bd8bff2c3d4cccb5f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988791f9c5c09c3c1809468be73fedc949", "guid": "bfdfe7dc352907fc980b868725387e98ae5929a6349243ee5e788ef9ff8b3aef", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983c2ac6024993df43f983c875d59d5208", "guid": "bfdfe7dc352907fc980b868725387e9897600ad9894f25bee20287e5d8f206cf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b0988e3c09659605b5fb285297b97ef", "guid": "bfdfe7dc352907fc980b868725387e9851794a9908b41061cd7ced83d54e01a2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98019863fada5d106ba10c018ac7a99f2f", "guid": "bfdfe7dc352907fc980b868725387e982ca2f289aa1af68ce39f3b14bba5219f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f5122446b2e86142d42e63d24e0757de", "guid": "bfdfe7dc352907fc980b868725387e9815e669415602f00e93fc4bc90fd1fa4b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bd96f5f9717c2901c5e03f50c6dd6618", "guid": "bfdfe7dc352907fc980b868725387e9800f6c13d3962ee55bb771318f0985af6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980c5c10d0cf9c7dff7edb0d80f285978b", "guid": "bfdfe7dc352907fc980b868725387e98208cee11b290d19573d8812b85143ecb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98982d0835627385449147d85ab3c54f36", "guid": "bfdfe7dc352907fc980b868725387e9879225a994eafbb57d7dc14a1c47366fb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821f15c39e6dc182f32a938aa9af3fae3", "guid": "bfdfe7dc352907fc980b868725387e9814e01101feb7266f748ee22e0e041967"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9829d2255c5dc92c73a238e17804dbf623", "guid": "bfdfe7dc352907fc980b868725387e987d61d588887190e87df14363d3e6158a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98937014aabfc488848aef931da2ae2a3d", "guid": "bfdfe7dc352907fc980b868725387e9819048f633da5bff3807f0ff2db3e01f3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98de18ec670de9f055069175171b1b8091", "guid": "bfdfe7dc352907fc980b868725387e9851c589e9e4d009ed269f7c6ca2ba3372"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98911cc19942ecb0d185a7484f5fb7681b", "guid": "bfdfe7dc352907fc980b868725387e98cff3cc2a87c285267a1edda8f1c2fb7e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986fd34d4ae419af365711e996069383ec", "guid": "bfdfe7dc352907fc980b868725387e9894c6f1e444ea8931b8e0cc25813d94fb"}], "guid": "bfdfe7dc352907fc980b868725387e98428723633cd9bf41bf91928e1489497c", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f5ac87662a84ce4c3d970c3f57c9b327", "guid": "bfdfe7dc352907fc980b868725387e98c24ebc67346f8ef9a0f6fa9f065a5ee0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec1971d19721dc3e02f5be00e2b6d09e", "guid": "bfdfe7dc352907fc980b868725387e98cb61e2eddb36ffc7012c4c9058bdf8e7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ebb9da1423dd7ce62cf93527b4774bf1", "guid": "bfdfe7dc352907fc980b868725387e982ca6337f8679b67f42696bf7f168f5e0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985052305f17d5de0e00a1e0bac8c047fd", "guid": "bfdfe7dc352907fc980b868725387e980704d38e430f64743083a6119f7586e5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989da0f5cac1d52aa674aa1a9b657168da", "guid": "bfdfe7dc352907fc980b868725387e98cfd2c08171ae7c4b4c93c4db73501059"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d8aabf4724bd0ec0b99bda8dd9c1b1a", "guid": "bfdfe7dc352907fc980b868725387e989e03709de95e7bb4615209c36da5ff4a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ed4b2add8b16c4849f3636ffcb632d9", "guid": "bfdfe7dc352907fc980b868725387e98f453227cbe8dfcd48153b23ab806d9ea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981e6f71e254c084987e4049ecb7452d07", "guid": "bfdfe7dc352907fc980b868725387e9804a9a625224d2e44b08cec58d386ed2d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98549625c901dbabed683a68511a7de358", "guid": "bfdfe7dc352907fc980b868725387e987dbb7caa8bcd1b2cf6124dcd90e727f1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bade4c1b31902a9be7200ce3459bd8d1", "guid": "bfdfe7dc352907fc980b868725387e98f5a9d76ba36d261a5b7513f0be8c8d37"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984025c65319dc53442dbd138d065d09ad", "guid": "bfdfe7dc352907fc980b868725387e98c44d10ce64f65e4413341064c88dc7ba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9856adc82db4b31a370d4e27a6389abed1", "guid": "bfdfe7dc352907fc980b868725387e982d1cf33121629f3e3bdfc69b35bdfccb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985b6718cc948839696885f1a4c11682d5", "guid": "bfdfe7dc352907fc980b868725387e98eba595e00fa00d3b43aa49c449a706b0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d17a11aba796d9c79ef6fc854a81918b", "guid": "bfdfe7dc352907fc980b868725387e98eec63fff4e8a3b2b0dfd18d47d2ceaeb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989ffb047a45a571096680f1dd3861f21c", "guid": "bfdfe7dc352907fc980b868725387e9881b2ec85f6adb849b9968b28442bd9c4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983cf667d3e9d5f1a1cec2b7ed335e1fc6", "guid": "bfdfe7dc352907fc980b868725387e988f4bc34a04db1a5257719e535bcc0ad5"}], "guid": "bfdfe7dc352907fc980b868725387e987fbdd20cee8e68ad9caf1f9305b772cd", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9824f5d6ec5088eecbb83955c855580732", "guid": "bfdfe7dc352907fc980b868725387e98cd047ed01be90a97b41c8a3a99563f2d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ee33ff2b9f8b1d223313a7a24cd5501", "guid": "bfdfe7dc352907fc980b868725387e9843e15f3c3160cba76fe696c2b7409000"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987cbcfbbfed448f33604b899a4f76dac9", "guid": "bfdfe7dc352907fc980b868725387e986ae1b8fa41d37efe85ed17b954358e91"}], "guid": "bfdfe7dc352907fc980b868725387e982c9737e19f5dd12b60ed163cd11c2da7", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98ead7ed888333ab36971c636e12bebbfd", "targetReference": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4"}], "guid": "bfdfe7dc352907fc980b868725387e9833a890451b422b80aa56b462d81af33a", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4", "name": "GoogleUtilities-GoogleUtilities_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ca49ca851f2777b997a3e74ccb860358", "name": "GoogleUtilities.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}