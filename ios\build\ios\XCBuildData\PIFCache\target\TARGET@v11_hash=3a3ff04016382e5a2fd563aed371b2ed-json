{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9889e44aea1a6b19eba9f1cdb1922a3538", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9834d923db9c5b913f036583542f2cdffe", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98da00a5db0ab2f5251a0c7bde72e3043d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9899e0279f0994fcf7889c8b4119e9e3d4", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98da00a5db0ab2f5251a0c7bde72e3043d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9863860fa1dcc248bb235807164d197899", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d0cdeebc5301016cefcce86d74cf5cb0", "guid": "bfdfe7dc352907fc980b868725387e98539fa942a5ee01f3316b7ef19ed880a5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983cf5f80389214187dd9e1c33c8ebec05", "guid": "bfdfe7dc352907fc980b868725387e989089d3ec5733d0b592a9f05f6d51cb45"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a57a8ca1ce63c1316fde2f0cec2edb13", "guid": "bfdfe7dc352907fc980b868725387e98e07a06dcfa2b4f377024cb98e2574250"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981ad3ac0eee7223075a818526efaefbd9", "guid": "bfdfe7dc352907fc980b868725387e98162866d1777e6a37eea2d824d6847f3d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980afb4f62a351402a5e0b6c0f7aaaf12d", "guid": "bfdfe7dc352907fc980b868725387e986c17b2daf25d97d9accc765133fce82c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98177d68ca9430de96483a96c770e23c12", "guid": "bfdfe7dc352907fc980b868725387e985c469b3d6a844117098caf7735e28830"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f23f1d1898a8a71186f31e7e28b63e5", "guid": "bfdfe7dc352907fc980b868725387e98c62d3f639149e3c94026d49586d6a267", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989a1b95cb9aa3abe7100e6fdbb1330938", "guid": "bfdfe7dc352907fc980b868725387e98bf7c53c1a1ad445778dafdf6a1635aef", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98723493f1f02b2917d8b5834011642934", "guid": "bfdfe7dc352907fc980b868725387e98b9f61357e317be1b5d6ab90415ca66cf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9836e5c5de74a8cbf54f25a688fa033922", "guid": "bfdfe7dc352907fc980b868725387e982c1909aba35767409636e7adfb8d7726"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982ba541ed57542b9c8de65ece721f2a07", "guid": "bfdfe7dc352907fc980b868725387e98cfd11c06866e9f90a58165c4c8fc169f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9831720f7f2b4be6741afcf829cff26e4d", "guid": "bfdfe7dc352907fc980b868725387e98bac3bffc860f21c4af4f316dd927515e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985de2aaf0f0ce9b19f0b5140b545ed750", "guid": "bfdfe7dc352907fc980b868725387e98e21a720873e1192d1a315b4aaee807ee", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98031094f27f9eb342c3c1280cb7c7fe5f", "guid": "bfdfe7dc352907fc980b868725387e98ec84e4694df14f5b9549570ced903c82"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984186ff7e1c03749da6a2d69f3fabfdd6", "guid": "bfdfe7dc352907fc980b868725387e98c513b7761ecc5dc437d555d8a33770cf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98578fb52c4161c2412bd86290398fb4ae", "guid": "bfdfe7dc352907fc980b868725387e988ac24c472a215af198f03daa69032599", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae2d6d5fab6d09908b8fa098e9841cc7", "guid": "bfdfe7dc352907fc980b868725387e98b576be42212aa416b8c5a76f808e1b33"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9848793a73a25b597757ec103bbb607ecf", "guid": "bfdfe7dc352907fc980b868725387e98187a55444a8d69383fbf3fa841bff114"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eefa1605b11f06116aeb7c9dfa53f8cc", "guid": "bfdfe7dc352907fc980b868725387e985df350c3f6623c60e5687939ad0c6168"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e6f508cb47c6ef934f946de26648b4ee", "guid": "bfdfe7dc352907fc980b868725387e988f774416e4c61eefe24a049a6b38e124"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c736832d72f2219f74f0c44c9cf461e", "guid": "bfdfe7dc352907fc980b868725387e98ea5e72ba41cc3b6c8094a9c076c0fc8f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b7c0918069571ba1b6561195045af037", "guid": "bfdfe7dc352907fc980b868725387e9829d615b8d64321af6800936677110902"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984a4bd927e5948ef431905558bee1bc74", "guid": "bfdfe7dc352907fc980b868725387e98054628eff32c6b0bd21ad93888eefd80"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d80f8a3f64876793ee47d3369477a23d", "guid": "bfdfe7dc352907fc980b868725387e98a05c172a54b12f9c4187ced162bacb76"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9829f0f1b328b491fcca2d56df481c45f6", "guid": "bfdfe7dc352907fc980b868725387e9854784af9afbb5a1d9fe5804dd5d8cd4e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cac6c1b9211e409fd32714e5b3aca6f2", "guid": "bfdfe7dc352907fc980b868725387e982467ab3aeee0e2c4b8f49ad72f36dbdd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ff8ff5d233a60d05fcfd45c922da9505", "guid": "bfdfe7dc352907fc980b868725387e98a9fe39ece97db7d3ecdc29bd1c42f7f2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a12e179566f336daa7ecf2478f51d3c", "guid": "bfdfe7dc352907fc980b868725387e98efae62b84cb67cb8454ddd8079ded5cd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb42a3835fc44a50a2e61b2c33d07e2b", "guid": "bfdfe7dc352907fc980b868725387e98722b0e9a4b9977f6b060c85951c1bb81"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985052f875f6d3de490d9ee5a997fb6226", "guid": "bfdfe7dc352907fc980b868725387e9893226d30e288d0c330496659cdf5d60d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c0af286f40c597e0f577ae71ce1a40f9", "guid": "bfdfe7dc352907fc980b868725387e98d8299db5ea6d8f5c0ec65cb2baaa10cf"}], "guid": "bfdfe7dc352907fc980b868725387e988d8f6894023c737e7b9df2015db83518", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a607c14e0c7516051711886594d0622e", "guid": "bfdfe7dc352907fc980b868725387e98edb1c0230e67e5e034a2d032339ed58a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981568b7f8026790fb4ea3f2dd912633d1", "guid": "bfdfe7dc352907fc980b868725387e980d6fdd72accdce41bbc53eebfbc25617"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9880e056c61429ac8f2fd0f2f3dafc3c04", "guid": "bfdfe7dc352907fc980b868725387e985042854f5142a96875a34dcdc0ae84b7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f4e3fccb25690809cb93b62fe6135d63", "guid": "bfdfe7dc352907fc980b868725387e989fa4eb41624a6d363f29ffa55d09a59e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9814c4b93c4690844332486c482ef62b57", "guid": "bfdfe7dc352907fc980b868725387e981fe5cbb97e41308b68bbe7b352d7fb64"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cde0d10644c917cc03e7c54bc0d96e7d", "guid": "bfdfe7dc352907fc980b868725387e98e3bfb046fcd88b7b0979fcacc00ca6b0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987cc9cc32c204410ea3b6bc780af109ff", "guid": "bfdfe7dc352907fc980b868725387e987a6e9eb6f32ae0700dbbbd998b4882a2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988067f69d213e752d5f2f682d4d7153d1", "guid": "bfdfe7dc352907fc980b868725387e98f231629700d8ef190638e5b2c8eec31e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983abb44791dd477ee5e4bc940091c1845", "guid": "bfdfe7dc352907fc980b868725387e98cdbe64d14c30e9d9488730ed08579248"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ecd61207c47307b5ad0ec7b121b5048f", "guid": "bfdfe7dc352907fc980b868725387e9894821c4adbc69f1abfa96bb4377308b8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981060315f71340973a2c6a829ddda2d06", "guid": "bfdfe7dc352907fc980b868725387e9859853c3c5a078e24b41315a4cc610631"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e052dfc2b306055ccc005117d913e6e3", "guid": "bfdfe7dc352907fc980b868725387e985f412e4dd53abfcfde12f9683d19312f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9881e9a4e6a567bb20e14bfd9e3a706115", "guid": "bfdfe7dc352907fc980b868725387e98e272805f9264695f87d6ec6166a54400"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983fbb2a12ef914214d7059f73658d2618", "guid": "bfdfe7dc352907fc980b868725387e98a38f8e54b6a5e0734de668a2221ed44d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98729096c358129f45d0feb88695865203", "guid": "bfdfe7dc352907fc980b868725387e98bf215e4fd1655f818955b84d3e56ce23"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987155b5a06f48d4b6e347711aa948fa41", "guid": "bfdfe7dc352907fc980b868725387e98627188ad5389ccc2a0844f94b5f81eb3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a5f35588431d004950d6cbd01b57ad3", "guid": "bfdfe7dc352907fc980b868725387e989361ff0a484f207ef7c5e5696ed44063"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f23f7c3f0958703847ba94a70bfa4a42", "guid": "bfdfe7dc352907fc980b868725387e98b85b83f3f5e7bc18155654a723991f46"}], "guid": "bfdfe7dc352907fc980b868725387e988f98502d9de6d70f3344eef02c059cbf", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9824f5d6ec5088eecbb83955c855580732", "guid": "bfdfe7dc352907fc980b868725387e980ebf7eb0d610c67017e78f8380ef95b2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ee33ff2b9f8b1d223313a7a24cd5501", "guid": "bfdfe7dc352907fc980b868725387e985ffed6793f9ceffe6c52d0daa93f7355"}], "guid": "bfdfe7dc352907fc980b868725387e986c168642a6f39961ee51411bf1e16a9d", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98e79b4eeaaeedc5d27ad411fd9b007b6b", "targetReference": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc"}], "guid": "bfdfe7dc352907fc980b868725387e98b97197a992eeb88e651d870fcac739ee", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc", "name": "FirebaseInstallations-FirebaseInstallations_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}], "guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9860819b8e327bf41b291e92315614a812", "name": "FirebaseInstallations.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}