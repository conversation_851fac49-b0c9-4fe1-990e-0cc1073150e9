{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988d487bfbd99f656f007147b7bd34d59f", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Daleel/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Daleel/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_image_compress_common", "PRODUCT_NAME": "flutter_image_compress_common", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980fe6008bd0ea7d60a239f5cfb496f9fd", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a0dcabac8cc86d39bbb74e1c6b0e4c1c", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Daleel/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Daleel/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_image_compress_common", "PRODUCT_NAME": "flutter_image_compress_common", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984ac27f284fbf5fa4242b11e4c252631e", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a0dcabac8cc86d39bbb74e1c6b0e4c1c", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Daleel/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Daleel/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_image_compress_common", "PRODUCT_NAME": "flutter_image_compress_common", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ec29c327167a123c1352766ac15557e9", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e988170fa303118b3ad6fa978e8e1d37fb3", "guid": "bfdfe7dc352907fc980b868725387e9840a9caeba178add7aa4dceb4c1bb75fe", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9874d5c7d3f9a6567cc970a6794abd6854", "guid": "bfdfe7dc352907fc980b868725387e98b4c61cc35fba40b4ce329132c3f61eca", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c9caefaefec59cb404b0eabb77beef49", "guid": "bfdfe7dc352907fc980b868725387e9818da142fe82d8a78c7be9db931d371ba", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9867345cdf2bd2f4f47f060043d0a10dce", "guid": "bfdfe7dc352907fc980b868725387e98df995503faf86a7e536e8fd0813b9b8f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ce61a24381d387779d0eb2731b418885", "guid": "bfdfe7dc352907fc980b868725387e984b4ae0c89031a24d1d71288e24a797ca", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988b8225bf956bca4278f814d4a7c6d22e", "guid": "bfdfe7dc352907fc980b868725387e985f28519c628b2857ebb37b7b581178d4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983e560b850d1d5324754f0d874ada15c6", "guid": "bfdfe7dc352907fc980b868725387e98d488e0a22f4c14aacdf4d40c2c9ddb44", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98859d52b9ae0f9d6f213eba27a679497f", "guid": "bfdfe7dc352907fc980b868725387e98fe7273ad9aa9fdcb548c9c43dc93f8b4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985cbae361e22bcf02ab25236f6ac872ee", "guid": "bfdfe7dc352907fc980b868725387e98313d9e25b0bf5f3c97ad080d07ef3914", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dffae4626c0d81dbe38833ab22b02a95", "guid": "bfdfe7dc352907fc980b868725387e989f407ff8592b35f500a6527a20317817", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d4ff4a3de103b489d9689d96778d68d9", "guid": "bfdfe7dc352907fc980b868725387e98e16efa990e491cc807ce6fc10625d15b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989543b47271da5c3c1544a3fab628dcce", "guid": "bfdfe7dc352907fc980b868725387e98105db008f99641fadb0caf5f7506b3c4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983af8539e312972e87dc15117f876865c", "guid": "bfdfe7dc352907fc980b868725387e98a6e4e157926dc87d2e4f2973b18d8bcc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983c2c6ff5bfc4947f9b9781bbef239396", "guid": "bfdfe7dc352907fc980b868725387e98d1ddc3be1007fb9ee70d27ff32e03352", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9832a24f29fde25b64a022371b9bb77683", "guid": "bfdfe7dc352907fc980b868725387e980f430574f80b3377bafb28f408603313", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980cf0c82d6a96def6caa76cd55d1514cc", "guid": "bfdfe7dc352907fc980b868725387e9811c36f63ef52dcfc9629b13ac9cd2366", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984e4b04aeb197e6b7b6f76601b71d2055", "guid": "bfdfe7dc352907fc980b868725387e98fdcde1de031b3934e8ad535d6a77715a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983e81941a4ff2b1921a4183b96768695b", "guid": "bfdfe7dc352907fc980b868725387e9806312812ff309343658d470012c9949e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e254f07961b89824714b07ad780aabf6", "guid": "bfdfe7dc352907fc980b868725387e98fd547b1fbc8601ed045148719c9fcb31", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981e34c4451da884d92a6ef6a8d449c48a", "guid": "bfdfe7dc352907fc980b868725387e98b8107d30d41b046eabedd15b3e204207", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98392219b86e2a6ee62e8b15ac4f979c05", "guid": "bfdfe7dc352907fc980b868725387e985911bdce5650368ba78e000854a76edc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ce3453cf8b4e1f536534c950b6c85d29", "guid": "bfdfe7dc352907fc980b868725387e9851879a7bd58d5db494aaffdae14944fc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982247691db987f63bab763ae8cfa74b21", "guid": "bfdfe7dc352907fc980b868725387e986d5e368a2966e687c64f43486e1a14ce", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a28c57785efa7ccc3c944fe5511ae0cb", "guid": "bfdfe7dc352907fc980b868725387e9892ad5fea3cd9806e3d88c69c494e5724", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98db1154bc254117e0af83ed09cf1cc7da", "guid": "bfdfe7dc352907fc980b868725387e982287d5443baa60a644e03eff995b0dbf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ee9440c3d8fe71b2623ab04092d12cd", "guid": "bfdfe7dc352907fc980b868725387e984b7cfa5cae6af5dd957a2e5e2c72d887", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983724dd3be3d4d32c899cfd82a15d919d", "guid": "bfdfe7dc352907fc980b868725387e981354bfb9c1f8a7cd29cda84a949c2b50", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c017fed5f645fad22ac408f6ecd97abe", "guid": "bfdfe7dc352907fc980b868725387e988ea9c6211d68a85a4fa5661a00b70d12", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98934cef9bf6ee848bfe3ec38d62197835", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9897af816e50ed4c9f6f90032448969735", "guid": "bfdfe7dc352907fc980b868725387e988601a4ee62b9ae3a9bcf8cd3d0ebe162"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987b2a15b659f4647cc4d0c68a0c2d6233", "guid": "bfdfe7dc352907fc980b868725387e98118fac16cfc7d0d72dbd7690930ee944"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a3898347d41006cf048dd01e4b1a2927", "guid": "bfdfe7dc352907fc980b868725387e988018d42a6cbf30bd26714051d751756f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986ed4c48508c44fa3ce08c3d716f3ad3d", "guid": "bfdfe7dc352907fc980b868725387e9816670788bc7f1e372bfade4c15b03b8d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d7240bcaf7ef8b80b8b12dd222075aab", "guid": "bfdfe7dc352907fc980b868725387e98f8b7fbab77fdbedd35cf16a5f9b711bb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f18f8389e60143d8c9d240ba4b9590e4", "guid": "bfdfe7dc352907fc980b868725387e9865826dabae77a65c0f8e3f8837afaccf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9884fbe54a21173f7bbd56b4fa0f9b11a2", "guid": "bfdfe7dc352907fc980b868725387e98b2432585b08dbc72dc97577aadf81be4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e94096146555b8f6e79226e261835027", "guid": "bfdfe7dc352907fc980b868725387e98dea84ba5702c5f8810e62a4263d9ace3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985b428a221f92ccd4a968d98b30a0cb10", "guid": "bfdfe7dc352907fc980b868725387e9838e63e5a34be3fcb50907fe5f2f4afed"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986fdcad82b83e05eae44a9a1e843f3b1f", "guid": "bfdfe7dc352907fc980b868725387e983efddb9b6fee895c6ea68388a011affc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9851723ccfcdcbeb8524be630dd184975e", "guid": "bfdfe7dc352907fc980b868725387e989a76fbeb6bac5b3b1280afb834ae8aa5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c2907c395435e0150ad1a38b01c372f1", "guid": "bfdfe7dc352907fc980b868725387e98ec6c6d0bf1756896b53daee803877721"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9863bac8476ddfafdb2f43d7803e41c4bc", "guid": "bfdfe7dc352907fc980b868725387e98e74fcc69d8150f502ccf1b3e2fc5485f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d25847212767ba86f09e41b23395abe2", "guid": "bfdfe7dc352907fc980b868725387e98b821742c1f6f7ea4cd9c0b273279b76f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9814dc2b4bb76cf999ee28763d430260ef", "guid": "bfdfe7dc352907fc980b868725387e982dc177db9f7d9ffb96479a0947cb42c0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987aeee6fb390b9440a40de49399114aa6", "guid": "bfdfe7dc352907fc980b868725387e9804e1e82783b73939a38d0ed8e1238d7d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f26ae2c84525e0a9727477a52c43085c", "guid": "bfdfe7dc352907fc980b868725387e9809531441d9135d8d86d4ea73bb600633"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98230efbe2b18886dd6e8aa1600e81d299", "guid": "bfdfe7dc352907fc980b868725387e986708b4f0dbacc243fb99a7eb06925479"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c670a63aa9d2c8e56d37fc244352be3", "guid": "bfdfe7dc352907fc980b868725387e98670d25b97004c7a93f78975edd086e14"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b65ebb6745d41a6da308b87742449574", "guid": "bfdfe7dc352907fc980b868725387e98f1a030d6e6eb0600473a3e08d860ea34"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d7b4a237b2116002c72f4bb59f923aeb", "guid": "bfdfe7dc352907fc980b868725387e98cc7934ae79503605f0492de7b872114f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9820f4458af31c7de72f93c0337a40ba5b", "guid": "bfdfe7dc352907fc980b868725387e98fbf436204971d95f60e7a593d7540c8a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a76a97f29c82370f30acd2f1464c68d7", "guid": "bfdfe7dc352907fc980b868725387e9898aaa51c1a99ce45e80eb8f93420d71e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d9becd8d87d88e4aa7166dcb1f887c4", "guid": "bfdfe7dc352907fc980b868725387e98d0c2773f09c4cabb2cf0eebceee42fe5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9840610d088d3a2920c16b73f3bc130aa7", "guid": "bfdfe7dc352907fc980b868725387e98da7585a197ccba31a23ef6e300e35684"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982ea7480a83329a8d1263542a34df3e88", "guid": "bfdfe7dc352907fc980b868725387e9877adb990468abb172a1f126cb52f85d0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d9dc05b2770c2d1164b9309e05374fe3", "guid": "bfdfe7dc352907fc980b868725387e98cf068f37d84ba0e155873865791a4c16"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f1b2adb0d2795f9e678969d79298a60", "guid": "bfdfe7dc352907fc980b868725387e982b47bac972003ddff7a9354e299d2485"}], "guid": "bfdfe7dc352907fc980b868725387e981f18c3bdd543f0383a05196ffefabc9e", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9824f5d6ec5088eecbb83955c855580732", "guid": "bfdfe7dc352907fc980b868725387e9808188f8afd5c22436d8f270ee53ec170"}], "guid": "bfdfe7dc352907fc980b868725387e98f59cae5e753b0a31876694ce85c47944", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98ec476f90f1f6e616e836f5cbf5a8826f", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e982742fb9f3ddc293e43c65bab714408c6", "name": "Mantle"}, {"guid": "bfdfe7dc352907fc980b868725387e98c46180aea4e87057640961e6db37df0d", "name": "SDWebImage"}, {"guid": "bfdfe7dc352907fc980b868725387e9837027570fe3a09bb9c7d0d2620332306", "name": "SDWebImageWebPCoder"}], "guid": "bfdfe7dc352907fc980b868725387e982ec175b6b4d6149d1cce89f5f0b3694a", "name": "flutter_image_compress_common", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98b928db338a2b0bf59a36f34e391aa676", "name": "flutter_image_compress_common.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}