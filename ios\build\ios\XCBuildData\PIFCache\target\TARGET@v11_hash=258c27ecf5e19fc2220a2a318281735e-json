{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98eff4039e7b4398ca26b27f2b7779a7b9", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Daleel/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Daleel/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/geolocator_apple/geolocator_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/geolocator_apple/geolocator_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/geolocator_apple/geolocator_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "geolocator_apple", "PRODUCT_NAME": "geolocator_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984936d7cb8feef48513f6131e211caece", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9868ed291d38d8b3d010dbdde4de05448e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Daleel/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Daleel/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/geolocator_apple/geolocator_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/geolocator_apple/geolocator_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/geolocator_apple/geolocator_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "geolocator_apple", "PRODUCT_NAME": "geolocator_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98beaaf634ac9c45a28ea8b53ae8cea921", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9868ed291d38d8b3d010dbdde4de05448e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Daleel/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Daleel/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/geolocator_apple/geolocator_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/geolocator_apple/geolocator_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/geolocator_apple/geolocator_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "geolocator_apple", "PRODUCT_NAME": "geolocator_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9879f34f1e012c285b844492b857411475", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e984c9218719ac8a9849d610d901c40f255", "guid": "bfdfe7dc352907fc980b868725387e981edfd38698068c109e5f814f5e4a6055", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98301409dabeb4ace44680ef698dd8de06", "guid": "bfdfe7dc352907fc980b868725387e98e44ea0d59edc9cf326f461c4edc8b6b8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988d5a2f5a8c73771dc205a34da858314c", "guid": "bfdfe7dc352907fc980b868725387e9808054ff88657f5f4a9121eb08482dd42", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f90f78a5da4576b60a6431f20438e909", "guid": "bfdfe7dc352907fc980b868725387e98c63dcbb1d49e192d4215b43dfcc573eb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a0b68a22291145ce485c05a4ca808f91", "guid": "bfdfe7dc352907fc980b868725387e98b5cd957363787598d6f7aea4947494bb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984f3fc2bec856ce24d70f1ab0eab7f219", "guid": "bfdfe7dc352907fc980b868725387e98d9ff6e51462c6aa74f3c8bb07b3a17a4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9845b52a24aff9a725a6852990b5550f1b", "guid": "bfdfe7dc352907fc980b868725387e98f0b81eccdf6f9f4354e1b5fc3b7069e4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c993e03f4715852e417bd9d81c0435c2", "guid": "bfdfe7dc352907fc980b868725387e982f69314735d4a464362113b79c4a5d8d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98336f192cd5118001f6967d6b6375d6d7", "guid": "bfdfe7dc352907fc980b868725387e984363a2ba622225fbe85f9b02e4113775", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986851ac0dfdfc3acfb3a2b5590fced962", "guid": "bfdfe7dc352907fc980b868725387e98a992b9e90700201958c967c403aabb39", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ba148785acd291e6ea02f15f54ccfce", "guid": "bfdfe7dc352907fc980b868725387e98359d111b1a1af5f00f64f4399e6c18a2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9818a1db980ba52f26513989b9257a0927", "guid": "bfdfe7dc352907fc980b868725387e98e9ef437827e6d243b4695d7ea82ea05f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a6b6300b3f9f584e9759e4373066908e", "guid": "bfdfe7dc352907fc980b868725387e98fbd31237d7e080da5ab0719e8b1740d4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9814194480471fbc4b3253dfa8fe769031", "guid": "bfdfe7dc352907fc980b868725387e9884c53c68a4b506556a1947ff82c1daf4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b1fd4652d49d060f92338cb5efd7e23", "guid": "bfdfe7dc352907fc980b868725387e984587bd174efca234b3aec913ed68ceb9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fda93d947e127f1b00a67e5bb15c0a98", "guid": "bfdfe7dc352907fc980b868725387e9825c32461fbb2aaa4e12b9443a7425752", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fbee07b0f72803696f52d1bb49bca5cf", "guid": "bfdfe7dc352907fc980b868725387e98f66a7ba79f63abbb1f9be5aa6632f488", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e986576ae2c21c60200c6d4fa060d500570", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9882c45037e86a765081555f94efbc92c9", "guid": "bfdfe7dc352907fc980b868725387e9812a9d052cc584b524cfccd7c10554f2b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981b3dd03f2d41d6764f1156d2bbf63fa2", "guid": "bfdfe7dc352907fc980b868725387e985934ed61617fcd2150cc180e1b99986c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e8b560ce92f06d2b567cc087a92e8689", "guid": "bfdfe7dc352907fc980b868725387e9825f99b1272f44ae4dfdea94ee4f74373"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c16b9ee1dd44f45a1a39668148d83ebf", "guid": "bfdfe7dc352907fc980b868725387e9877701f66c8ff3229ca49f963f1b5ba3f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985eccfb16e9e9c0444e27b8b0542ddd1c", "guid": "bfdfe7dc352907fc980b868725387e98d9f834483786fc0c92f7593bcd6dd83c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c8a7ab2edefd2e937002bdb51e629d9e", "guid": "bfdfe7dc352907fc980b868725387e986f21906108394e559b5f20f7b1b2b7c5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9833a915f542a473eb8ed07519afdd11d8", "guid": "bfdfe7dc352907fc980b868725387e983577603b1f8bf82fa87b1f0d8f22ba8c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98091df136e97e968d81aa9578e0b9447d", "guid": "bfdfe7dc352907fc980b868725387e98dee846baf0ac46e7ac436eec838e500e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9817d47b9d5153e46d81657c9742e61077", "guid": "bfdfe7dc352907fc980b868725387e987c7b231934ce09ccf3bb4d6f09388bc6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98033786503733d72765333a1fcac5d966", "guid": "bfdfe7dc352907fc980b868725387e98629984a243136509c4f8eb078ee5ae35"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985b242782ab5ea01da792fc98cb50bfa2", "guid": "bfdfe7dc352907fc980b868725387e9824c51163076c8c96c78611917cd1dc98"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e5271ea3882664e679568e150e4202be", "guid": "bfdfe7dc352907fc980b868725387e9801b181447883d020411d92431c305571"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d771199373cca26d5f42085c45626e3", "guid": "bfdfe7dc352907fc980b868725387e98ccc265a5f52077e3b1c5c7ba958a6922"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980c9ac4d0d15cd53c7dbffff28b3dd220", "guid": "bfdfe7dc352907fc980b868725387e9860185781668f0073f944bada52aff903"}], "guid": "bfdfe7dc352907fc980b868725387e989d91e5abbad637a65716b7fef9466ccd", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9824f5d6ec5088eecbb83955c855580732", "guid": "bfdfe7dc352907fc980b868725387e9847d3b3fdbf8c6c03a84819a79f7ae5aa"}], "guid": "bfdfe7dc352907fc980b868725387e988ec00323e57392a3c283b014e048e33b", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98c4b351c20ac090098b0dcaf5fd3c18e2", "targetReference": "bfdfe7dc352907fc980b868725387e98e1aba8ff8dc833f2269ce0a7182533b3"}], "guid": "bfdfe7dc352907fc980b868725387e98053f546dc39b1661db608be4eb007f0a", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98e1aba8ff8dc833f2269ce0a7182533b3", "name": "geolocator_apple-geolocator_apple_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e9821d372cc1e7c7587a12aeda843619e39", "name": "geolocator_apple", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e986ff8f87e011522b1b6328c84d9533927", "name": "geolocator_apple.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}