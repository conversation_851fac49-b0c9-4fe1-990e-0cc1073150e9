{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ce382db62f00a4970eeaf421b1b3ff17", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98546bbba2f36db4b58da2ae1550d0ef53", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985ddac7597ba44ec9bdf3d220d232603c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9826c9e540ae8570620df74278795987b6", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985ddac7597ba44ec9bdf3d220d232603c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e981f37d7e12b20bddf5cee03724de74c1c", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98dd7ea534aacf0a6ab1bd0effc0c630d9", "guid": "bfdfe7dc352907fc980b868725387e98e43653943b8b90aa76bffdd58e214e48"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9819e6c676a4d5b3718407eb8c62d6549a", "guid": "bfdfe7dc352907fc980b868725387e98f4e08e674bc1e068f67e2fca7271f2a4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985be4f0340bc5cc133a7c44dc914a8ea7", "guid": "bfdfe7dc352907fc980b868725387e98bfa8c4d567e340e81eb942361c7689c1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba276c1aa4a8d0d22edf99fde2ec0905", "guid": "bfdfe7dc352907fc980b868725387e98e961ba30dafef335c872af62e85310b8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da7dd30bd945d13cfad130b0ed086279", "guid": "bfdfe7dc352907fc980b868725387e9817c98ae4370f62c26352374a402c27f2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98676f20cb6b8ea94aa66e4ba7ea6be522", "guid": "bfdfe7dc352907fc980b868725387e98252ed2608758a8161f87322751290bc9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98818b7a54e4ac026937da9f8aefeda4e3", "guid": "bfdfe7dc352907fc980b868725387e98b9524560282825bbf6f6d6c28b27d827"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec388c34c0475cd41280eba352e1a342", "guid": "bfdfe7dc352907fc980b868725387e98e9a75bb925d388116ffe51468e5edc1a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9869bedafe60981cc5ad66f2337d82de9d", "guid": "bfdfe7dc352907fc980b868725387e98045d27c85443a40f49e13ee773840b30", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982d8af9d94cc7c7be3cdb895838f841a0", "guid": "bfdfe7dc352907fc980b868725387e98a0b6a3ba2fe399bc87bf92490cfdd545"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981711f259d203b0ca592f0b7e4a59c636", "guid": "bfdfe7dc352907fc980b868725387e9833110019562d9284cc98d37c1cb3e68f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98218b1645af01bcdf39f501f61e287933", "guid": "bfdfe7dc352907fc980b868725387e982ed93578111cb37738d215c47cf7b5ca", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e6e4bdd01c5506de98b1a6f67e083da8", "guid": "bfdfe7dc352907fc980b868725387e9885a2b233f342b38daa7b3a9576096bf9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9899ab90cbe579dd6a3930aafab0db8546", "guid": "bfdfe7dc352907fc980b868725387e98f7413d41c94d70d7281ddea4d7b7c0b5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d19c48bd6c537353dfb7e587e567ab11", "guid": "bfdfe7dc352907fc980b868725387e98c2b65420fe6ed2633ec832294c62586c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98460b7c9266542bcf28e94bb1d8abecaf", "guid": "bfdfe7dc352907fc980b868725387e980792734ff61fc52778022d01fb0e72f9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981424831a2b4dfe8eac4d73daa32e4e1e", "guid": "bfdfe7dc352907fc980b868725387e9888098180ada49fd722acecd2e2bb9ca1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982038a7aad8734bbedf05831ddbc80bfd", "guid": "bfdfe7dc352907fc980b868725387e980b68dd3a61aafc35422a130ba847b741", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98280aea887e9396bbc188d0799f11a6bc", "guid": "bfdfe7dc352907fc980b868725387e985bf17e9fa5b7280233ddd370291c0731", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9864a5e86e29d8893670375cc756db9701", "guid": "bfdfe7dc352907fc980b868725387e988e8aaa225932e9a335174d295b660985"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cdd2f5e6a184908356095cb2c628759d", "guid": "bfdfe7dc352907fc980b868725387e98852ecd301d71317cad23bf580444620e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821f961d9840e05b316deca8c0e051484", "guid": "bfdfe7dc352907fc980b868725387e98abb42e38a436c4bf861499c3c0efb37a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d1b98ca13b1aaf345bef7ad351535a2b", "guid": "bfdfe7dc352907fc980b868725387e9847c96fb64658f75b4d09b48747c6bafb", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e981681c8db89d2ec5aea81ca873ea7c7ac", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9850d91b2490d08ef94e82e8dd4fd83b94", "guid": "bfdfe7dc352907fc980b868725387e9824ffc078fce23fd35a0006d8f906127f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984abc3991aa22633cb71a3f69a3564f8c", "guid": "bfdfe7dc352907fc980b868725387e984850f2171321b4ecd5f9d113d99478ee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983669973cbe49dd9cb4087e3b2edea02a", "guid": "bfdfe7dc352907fc980b868725387e98ca23608313aa97ddd96c0a697090ade3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986ccf426f8992ea09298a82a621896496", "guid": "bfdfe7dc352907fc980b868725387e988bb544c1805dd01c2086061432b901bd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98610d57e0b9254baeee3c6d204c08b71f", "guid": "bfdfe7dc352907fc980b868725387e98bac8ff6041057f7e3b7fc0a60416e65e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985f9e299a8b957d916777c63bb26b4f8d", "guid": "bfdfe7dc352907fc980b868725387e98a2814cae3dcb469680338dcf0082221b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985a885ebd7438e9e7a60c71d5f1cdb04b", "guid": "bfdfe7dc352907fc980b868725387e9867d789aa455015b60edb71c47de5b4b1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9823712f026aa22269cafe5f492e680a1e", "guid": "bfdfe7dc352907fc980b868725387e986b6c94cd77981f6b1890a6b9c6453aa5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a61748392f0ba13051d0756af56b312d", "guid": "bfdfe7dc352907fc980b868725387e98f4e375202f431c835452c71c83007ac9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d700cd86ad71a639748963aa229df9a0", "guid": "bfdfe7dc352907fc980b868725387e988f66876b251d253a62889f3bfdc92bbb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9808c7e38e3e47800b545285f56387c5d1", "guid": "bfdfe7dc352907fc980b868725387e98f1dc2056fb9a2c7475e20c704c1069e4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98567383250999e26eae2744560e4a45d3", "guid": "bfdfe7dc352907fc980b868725387e986c59f05dcc40812dc61e1efb8d1330a7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e2ea8c12b56ce793dca37ae25123450c", "guid": "bfdfe7dc352907fc980b868725387e98cca57070c92c3b64332e66029cac3f13"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9845c426e33e84494de5102d2b55d00495", "guid": "bfdfe7dc352907fc980b868725387e988201f30682441329547acef75a6c7f4a"}], "guid": "bfdfe7dc352907fc980b868725387e981a30d3cd8582bc935920153b336cc130", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9824f5d6ec5088eecbb83955c855580732", "guid": "bfdfe7dc352907fc980b868725387e984c7c3f9f7a81d74bb4ffad105f976521"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9848a7bf5f5ee928f3be52fdff6a2dc80a", "guid": "bfdfe7dc352907fc980b868725387e983694bafec4936e3e409085015bd380a2"}], "guid": "bfdfe7dc352907fc980b868725387e98f8a2e6c109e260a4aaaf569236cbac3b", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98f6d2000851f4fe561de97326b84f616d", "targetReference": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14"}], "guid": "bfdfe7dc352907fc980b868725387e981c8357a049b945ea95ca07f0e5513a15", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14", "name": "FirebaseCore-FirebaseCore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e988ae261e418baab0fdd0a48d117fe7fa2", "name": "FirebaseCore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}