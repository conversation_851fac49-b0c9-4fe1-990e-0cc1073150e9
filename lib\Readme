🔄 Enable Background Token Refresh
To enable periodic background token refreshing, make sure to uncomment the following lines in the respective files:

📍 File: lib/core/service/refresh_token_manger/token_manager.dart
Uncomment the Workmanager().executeTask block:

Workmanager().executeTask((task, inputData) async {
  print('Executing refresh token task');
  TokenManager tokenManager = TokenManager();
  await CacheHelper.init();
  await tokenManager.refreshToken();
  return Future.value(true);
});
📍 File: lib/core/service/main_service/presentation/widgets/init_functions.dart
Uncomment the initialization and task registration:

Workmanager().initialize(ReminderManager.callbackDispatcher, isInDebugMode: kDebugMode);

Workmanager().registerPeriodicTask(
  '1',
  'refreshTokenTask',
  frequency: Duration(hours: 4),
  initialDelay: Duration(seconds: 10),
  constraints: Constraints(networkType: NetworkType.connected),
);


📍 File: pubspec.yaml
Uncomment the following lines in the pubspec.yaml file:

dependencies:
  workmanager: ^0.5.0


on  Booking Screen we need to solce this code in this path lib/features/appointments/view/appointments/booking_screen.dart same prlem lib/features/appointments/view/appointments/book_again_screen.dart

          if (state is CreateAppointmentsSuccess) {
            LayoutCubit.get(context).changeBottomNav(2);
            LayoutCubit.get(context).pets.forEach((element) {
              element.isSelected = false;
            });
            navigateAndFinish(context, LayoutScreen());
          }


auth cubit