// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a ar locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'ar';

  static String m0(count) => "عرض ${count} ردود";

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
    "AddServiceSupplier": MessageLookupByLibrary.simpleMessage(
      "إضافة مزود خدمة",
    ),
    "CleanlinessOfClinic": MessageLookupByLibrary.simpleMessage(
      "نظافة العيادة",
    ),
    "DateOfRecord": MessageLookupByLibrary.simpleMessage("التاريخ"),
    "DoctorService": MessageLookupByLibrary.simpleMessage("خدمة الدكتور"),
    "HomePageSearchText": MessageLookupByLibrary.simpleMessage("بحث"),
    "HomePageSearchText2": MessageLookupByLibrary.simpleMessage(
      "ابحث عن طبيبك",
    ),
    "NameOfRecord": MessageLookupByLibrary.simpleMessage("اسم السجل"),
    "Orloginasadoctor": MessageLookupByLibrary.simpleMessage(
      "أو قم بتسجيل الدخول كطبيب",
    ),
    "PASSWORD_MIN_LENGTH": MessageLookupByLibrary.simpleMessage(
      "يجب أن تتكون كلمة المرور من 6 أحرف على الأقل",
    ),
    "ServiceImage": MessageLookupByLibrary.simpleMessage("إضافة صورة الخدمة"),
    "ServiceLocation": MessageLookupByLibrary.simpleMessage("موقع الخدمة"),
    "ServiceName": MessageLookupByLibrary.simpleMessage("اسم الخدمة"),
    "ServicePhone": MessageLookupByLibrary.simpleMessage("هاتف الخدمة"),
    "ShareImagesPet": MessageLookupByLibrary.simpleMessage("شارك صور الالأليف"),
    "TimeOfRecord": MessageLookupByLibrary.simpleMessage("الوقت"),
    "about": MessageLookupByLibrary.simpleMessage("عن التطبيق"),
    "accept": MessageLookupByLibrary.simpleMessage("قبول"),
    "addAppointment": MessageLookupByLibrary.simpleMessage("إضافة موعد"),
    "addAvailabilities": MessageLookupByLibrary.simpleMessage(
      "إضافة الأوقات المتاحة",
    ),
    "addComment": MessageLookupByLibrary.simpleMessage("أضف تعليقك ..."),
    "addPet": MessageLookupByLibrary.simpleMessage("إضافة أليف"),
    "addPetChoose": MessageLookupByLibrary.simpleMessage("اختر..."),
    "addPetService": MessageLookupByLibrary.simpleMessage("خدمات"),
    "addRecord": MessageLookupByLibrary.simpleMessage("إضافة تذكير"),
    "addReplayComment": MessageLookupByLibrary.simpleMessage("أضف رد ..."),
    "addToSqueak": MessageLookupByLibrary.simpleMessage("اضافة الى Squeak"),
    "addYourFirstPet": MessageLookupByLibrary.simpleMessage(
      "اضف اول صديق اليف لك",
    ),
    "address": MessageLookupByLibrary.simpleMessage("عنوان"),
    "addressCity": MessageLookupByLibrary.simpleMessage("الموقع"),
    "address_hint": MessageLookupByLibrary.simpleMessage("أدخل عنوانك"),
    "address_validation": MessageLookupByLibrary.simpleMessage(
      "يرجى إدخال عنوانك",
    ),
    "alreadyHaveAccount": MessageLookupByLibrary.simpleMessage(
      "هل لديك حساب بالفعل؟",
    ),
    "appointmentButtonBooking": MessageLookupByLibrary.simpleMessage(
      "احجز مرة أخرى",
    ),
    "appointmentButtonCall": MessageLookupByLibrary.simpleMessage("اتصال"),
    "appointmentButtonCancel": MessageLookupByLibrary.simpleMessage("إلغاء"),
    "appointmentButtonEdit": MessageLookupByLibrary.simpleMessage("تعديل"),
    "appointmentCanceled": MessageLookupByLibrary.simpleMessage("ملغى"),
    "appointmentDone": MessageLookupByLibrary.simpleMessage("تم"),
    "appointmentExamination": MessageLookupByLibrary.simpleMessage(
      "فحص العيادة",
    ),
    "appointmentModalButtonNo": MessageLookupByLibrary.simpleMessage("لا"),
    "appointmentModalButtonYes": MessageLookupByLibrary.simpleMessage("نعم"),
    "appointmentModalDescription": MessageLookupByLibrary.simpleMessage(
      "هل أنت متأكد أنك تريد إلغاء الموعد إلى",
    ),
    "appointmentModalTitle": MessageLookupByLibrary.simpleMessage(
      "تأكيد الإلغاء",
    ),
    "appointmentReserved": MessageLookupByLibrary.simpleMessage("محجوز"),
    "appointmentStatus": MessageLookupByLibrary.simpleMessage("تم"),
    "appointmentsAll": MessageLookupByLibrary.simpleMessage("جميع"),
    "appointmentsAttended": MessageLookupByLibrary.simpleMessage("حضر"),
    "appointmentsCanceled": MessageLookupByLibrary.simpleMessage("ملغى"),
    "appointmentsEndExamination": MessageLookupByLibrary.simpleMessage(
      "إنهاء الفحص",
    ),
    "appointmentsFinished": MessageLookupByLibrary.simpleMessage("منتهي"),
    "appointmentsReserved": MessageLookupByLibrary.simpleMessage("محجوز"),
    "appointmentsStartExamination": MessageLookupByLibrary.simpleMessage(
      "بدء الفحص",
    ),
    "arabic": MessageLookupByLibrary.simpleMessage("اللغة العربية"),
    "bill": MessageLookupByLibrary.simpleMessage("الفاتورة"),
    "birthdate": MessageLookupByLibrary.simpleMessage("تاريخ الميلاد"),
    "birthdateValidation": MessageLookupByLibrary.simpleMessage(
      "من فضلك ادخل تاريخ الميلاد",
    ),
    "birthdate_hint": MessageLookupByLibrary.simpleMessage("أدخل تاريخ ميلادك"),
    "birthdate_validation": MessageLookupByLibrary.simpleMessage(
      "يرجى إدخال تاريخ ميلادك",
    ),
    "boardingBy": MessageLookupByLibrary.simpleMessage("الإقامة بواسطة"),
    "boardingCage": MessageLookupByLibrary.simpleMessage("القفص"),
    "boardingPrice": MessageLookupByLibrary.simpleMessage("التكلفة الأولية"),
    "boardingType": MessageLookupByLibrary.simpleMessage("نوع الإقامة"),
    "boardingTypeNoteDay": MessageLookupByLibrary.simpleMessage(
      "يرجى ملاحظة أنك اخترت تكلفة الإقامة بالأيام",
    ),
    "boardingTypeNoteHour": MessageLookupByLibrary.simpleMessage(
      "يرجى ملاحظة أنك اخترت تكلفة الإقامة بالساعات",
    ),
    "booking": MessageLookupByLibrary.simpleMessage("الحجز"),
    "breed": MessageLookupByLibrary.simpleMessage("السلالة"),
    "cageSelect": MessageLookupByLibrary.simpleMessage("اختر قفصًا"),
    "cancel": MessageLookupByLibrary.simpleMessage("الغاء"),
    "cancelFollow": MessageLookupByLibrary.simpleMessage("إلغاء المتابعة"),
    "changePassword": MessageLookupByLibrary.simpleMessage("تغيير كلمة المرور"),
    "changePhoto": MessageLookupByLibrary.simpleMessage("تغيير الصورة"),
    "checkYourEmail": MessageLookupByLibrary.simpleMessage(
      "تحقق من بريدك الإلكتروني",
    ),
    "chooseViewMode": MessageLookupByLibrary.simpleMessage("اختر وضع العرض"),
    "chosePet": MessageLookupByLibrary.simpleMessage("اختر أليف"),
    "city": MessageLookupByLibrary.simpleMessage("المدينة"),
    "clinic": MessageLookupByLibrary.simpleMessage("العيادة"),
    "clinicDoctor": MessageLookupByLibrary.simpleMessage("أطباء العيادة"),
    "clinicFollowedBefore": MessageLookupByLibrary.simpleMessage(
      "لقد تابعت هذه العيادة من قبل",
    ),
    "closeIn": MessageLookupByLibrary.simpleMessage("يغلق في"),
    "commentReply": MessageLookupByLibrary.simpleMessage("رد التعليق"),
    "comments": MessageLookupByLibrary.simpleMessage("التعليقات"),
    "comparePassword": MessageLookupByLibrary.simpleMessage(
      "أدخل تأكيد كلمة المرور الخاصة بك",
    ),
    "compeleteSqueakRegister": MessageLookupByLibrary.simpleMessage(
      "أكمل نموذج التسجيل دعوة vetIcare",
    ),
    "compliance": MessageLookupByLibrary.simpleMessage(
      "الامتثال للوائح حماية البيانات",
    ),
    "complianceDesc": MessageLookupByLibrary.simpleMessage(
      "نحن نلتزم بالقوانين واللوائح ذات الصلة بحماية البيانات لضمان التعامل مع بياناتك بأقصى قدر من العناية ووفقًا للمتطلبات القانونية.",
    ),
    "confirmNewPassword": MessageLookupByLibrary.simpleMessage(
      "تأكيد كلمة المرور الجديدة",
    ),
    "confirmPassword": MessageLookupByLibrary.simpleMessage(
      "تأكيد كلمة المرور",
    ),
    "contactPrivacy": MessageLookupByLibrary.simpleMessage(
      "اتصل بنا بخصوص الخصوصية",
    ),
    "contactPrivacyDesc": MessageLookupByLibrary.simpleMessage(
      "إذا كانت لديك أي مخاوف أو استفسارات حول كيفية تعاملنا مع بياناتك أو إذا كنت ترغب في ممارسة حقوقك المتعلقة بالبيانات، يرجى الاتصال بفريق الخصوصية الخاص بنا على <EMAIL>. نحن هنا لضمان شعورك بالثقة والأمان عند استخدام خدماتنا.",
    ),
    "contactProblem": MessageLookupByLibrary.simpleMessage(
      "تواصل معانا في حالة حدوث اي مشكلة",
    ),
    "contactUs": MessageLookupByLibrary.simpleMessage("اتصل بنا"),
    "contactUsDesc": MessageLookupByLibrary.simpleMessage(
      "إذا كان لديك أي أسئلة حول سياسة الخصوصية هذه، يرجى الاتصال بنا على <EMAIL>.",
    ),
    "copyright": MessageLookupByLibrary.simpleMessage(
      "© 2023 Quad Insight. جميع الحقوق محفوظة",
    ),
    "copyright2": MessageLookupByLibrary.simpleMessage(
      "© 2024 فيتيكير. جميع الحقوق محفوظة.",
    ),
    "couldNotOpenLink": MessageLookupByLibrary.simpleMessage("تعذر فتح الرابط"),
    "crNumber": MessageLookupByLibrary.simpleMessage("بيانات الأليف"),
    "createPost": MessageLookupByLibrary.simpleMessage("إنشاء منشور"),
    "darkMode": MessageLookupByLibrary.simpleMessage("الوضع الداكن"),
    "dataEncryption": MessageLookupByLibrary.simpleMessage("تشفير البيانات"),
    "dataEncryptionDesc": MessageLookupByLibrary.simpleMessage(
      "يتم تشفير جميع البيانات المرسلة بين تطبيقنا وخوادمنا باستخدام بروتوكولات آمنة (مثل HTTPS). بالإضافة إلى ذلك، يتم تشفير المعلومات الحساسة، بما في ذلك بيانات عيادتك ومعلومات الدفع، وتخزينها بأمان لمنع الوصول غير المصرح به.",
    ),
    "dataRetention": MessageLookupByLibrary.simpleMessage(
      "سياسة الاحتفاظ بالبيانات",
    ),
    "dataRetentionDesc": MessageLookupByLibrary.simpleMessage(
      "نحتفظ ببياناتك فقط طالما كانت ضرورية لتقديم خدماتنا ولأغراض أعمال مشروعة، مثل الاحتفاظ بالسجلات لأغراض مالية أو قانونية أو امتثال. بمجرد أن تصبح البيانات غير ضرورية، نتخذ خطوات لحذفها بأمان أو إخفاء هويتها.",
    ),
    "dataSecurity": MessageLookupByLibrary.simpleMessage(
      "أمان البيانات والخصوصية",
    ),
    "dataSecurityDesc": MessageLookupByLibrary.simpleMessage(
      "خصوصيتك وأمن بيانات عيادتك هما من أولوياتنا. نحن نطبق تدابير أمان وفقًا للمعايير الصناعية لحماية بياناتك من الوصول غير المصرح به أو التعديل أو الإفشاء. يتم تحديث أنظمتنا بانتظام لمعالجة الثغرات المحتملة وضمان أعلى مستوى من الحماية.",
    ),
    "date": MessageLookupByLibrary.simpleMessage("التاريخ"),
    "delete": MessageLookupByLibrary.simpleMessage("حذف"),
    "deleteComment": MessageLookupByLibrary.simpleMessage("حذف التعليق"),
    "deleteConfirmation": MessageLookupByLibrary.simpleMessage("تاكيد الحذف"),
    "deletePhoto": MessageLookupByLibrary.simpleMessage("حذف الصورة"),
    "doctorName": MessageLookupByLibrary.simpleMessage("اسم الطبيب"),
    "edit": MessageLookupByLibrary.simpleMessage("تحرير"),
    "editComment": MessageLookupByLibrary.simpleMessage("تعديل التعليق ..."),
    "editCommentPost": MessageLookupByLibrary.simpleMessage("تعديل تعليق"),
    "editPet": MessageLookupByLibrary.simpleMessage("تعديل الاليف"),
    "editReplyCommentPost": MessageLookupByLibrary.simpleMessage(
      "تعديل رد على تعليق",
    ),
    "email": MessageLookupByLibrary.simpleMessage("عنوان البريد الإلكتروني"),
    "emailCopied": MessageLookupByLibrary.simpleMessage(
      "تم نسخ البريد الإلكتروني إلى الحافظة",
    ),
    "email_hint": MessageLookupByLibrary.simpleMessage("أدخل بريدك الإلكتروني"),
    "email_valid": MessageLookupByLibrary.simpleMessage(
      "يرجى إدخال بريد إلكتروني",
    ),
    "email_validation": MessageLookupByLibrary.simpleMessage(
      "يرجى إدخال بريد إلكتروني صالح",
    ),
    "english": MessageLookupByLibrary.simpleMessage("الإنجليزية"),
    "enterAValidEm": MessageLookupByLibrary.simpleMessage(
      "أدخل بريدًا إلكترونيًا صالحًا",
    ),
    "enterCharNamePls": MessageLookupByLibrary.simpleMessage(
      "الاسم أكثر من 6 أحرف",
    ),
    "enterCharPassword": MessageLookupByLibrary.simpleMessage(
      "كلمة المرور أكثر من 6 أحرف",
    ),
    "enterCode": MessageLookupByLibrary.simpleMessage("أدخل الرمز المرسل إليك"),
    "enterConfirmNewPass": MessageLookupByLibrary.simpleMessage(
      "أدخل تأكيد كلمة المرور الجديدة",
    ),
    "enterName": MessageLookupByLibrary.simpleMessage(
      "أدخل الاسم الكامل الخاص بك",
    ),
    "enterNewPassword": MessageLookupByLibrary.simpleMessage(
      "أدخل كلمة المرور الجديدة",
    ),
    "enterPetName": MessageLookupByLibrary.simpleMessage("ادخل اسم الاليف"),
    "enterPetNameValidation": MessageLookupByLibrary.simpleMessage(
      "من فضلك ادخل الاسم الأليف",
    ),
    "enterPhone": MessageLookupByLibrary.simpleMessage("أدخل رقم هاتفك"),
    "enterUrAddress": MessageLookupByLibrary.simpleMessage("أدخل عنوانك"),
    "enterUrEmail": MessageLookupByLibrary.simpleMessage(
      "أدخل عنوان بريدك الإلكتروني",
    ),
    "enterUrEmailOPhone": MessageLookupByLibrary.simpleMessage(
      "الرجاء أدخل بريدك الإلكتروني أو رقم هاتفك",
    ),
    "enterUrEmailOPhoneValid": MessageLookupByLibrary.simpleMessage(
      " الرجاء أدخل بريد إلكتروني أو رقم هاتف صالح",
    ),
    "enterUrEmailORPhone": MessageLookupByLibrary.simpleMessage(
      "أدخل بريدك الإلكتروني أو رقم هاتفك",
    ),
    "enterUrPassword": MessageLookupByLibrary.simpleMessage(
      "أدخل كلمة المرور الخاصة بك",
    ),
    "enterYourText": MessageLookupByLibrary.simpleMessage("بماذا تفكر ....؟"),
    "entryDate": MessageLookupByLibrary.simpleMessage("تاريخ الدخول"),
    "exitDate": MessageLookupByLibrary.simpleMessage("تاريخ الخروج"),
    "expiredRequest": MessageLookupByLibrary.simpleMessage(
      "طلب المتابعة الذي تبحث عنه لم يعد متوفرًا. قد يكون قد تم إلغاؤه، أو انتهت صلاحيته، أو تم قبوله بالفعل.",
    ),
    "failedToLoadVersion": MessageLookupByLibrary.simpleMessage(
      "فشل تحميل الإصدار",
    ),
    "female": MessageLookupByLibrary.simpleMessage("أنثى"),
    "files": MessageLookupByLibrary.simpleMessage("الملفات"),
    "filesAndPrescription": MessageLookupByLibrary.simpleMessage(
      "Files And Prescription",
    ),
    "filter_hint_State": MessageLookupByLibrary.simpleMessage("فرز حسب الحالة"),
    "filter_hint_pets": MessageLookupByLibrary.simpleMessage(
      "فرز حسب اسم الأليف",
    ),
    "findPotentialMates": MessageLookupByLibrary.simpleMessage(
      "ابحث عن صديقك الاليف...",
    ),
    "followCode": MessageLookupByLibrary.simpleMessage("كود العياده"),
    "followConfirmation": MessageLookupByLibrary.simpleMessage(
      "تأكيد المتابعة",
    ),
    "followRequest": MessageLookupByLibrary.simpleMessage("طلبات المتابعة"),
    "followRequestUnAvailable": MessageLookupByLibrary.simpleMessage(
      "طلب المتابعة غير متوفر",
    ),
    "forgotPass": MessageLookupByLibrary.simpleMessage("هل نسيت كلمة المرور؟"),
    "fullName": MessageLookupByLibrary.simpleMessage("الاسم الكامل"),
    "gaber": MessageLookupByLibrary.simpleMessage("اختبارEN"),
    "gender": MessageLookupByLibrary.simpleMessage("الجنس"),
    "generalInformation": MessageLookupByLibrary.simpleMessage("معلومات عامة"),
    "goToClinicsFromPetsScreen": MessageLookupByLibrary.simpleMessage(
      "اذهب الى العيادات",
    ),
    "haveNotAccount": MessageLookupByLibrary.simpleMessage("ليس لديك حساب؟"),
    "help": MessageLookupByLibrary.simpleMessage("الدعم"),
    "helpShare": MessageLookupByLibrary.simpleMessage(
      "ساعدنا في مشاركة التطبيق",
    ),
    "howWeUse": MessageLookupByLibrary.simpleMessage("كيف نستخدم معلوماتك"),
    "howWeUseDesc": MessageLookupByLibrary.simpleMessage(
      "تُستخدم معلوماتك لتقديم وتحسين خدماتنا.",
    ),
    "ignore": MessageLookupByLibrary.simpleMessage("رفض"),
    "infoProtection": MessageLookupByLibrary.simpleMessage("حماية المعلومات"),
    "infoProtectionDesc": MessageLookupByLibrary.simpleMessage(
      "نحن نأخذ أمن البيانات على محمل الجد ونطبق تدابير مناسبة لحماية معلوماتك الشخصية من الوصول غير المصرح به أو التعديل أو الإفشاء.",
    ),
    "infoWeCollect": MessageLookupByLibrary.simpleMessage(
      "المعلومات التي نجمعها",
    ),
    "infoWeCollectDesc": MessageLookupByLibrary.simpleMessage(
      "قد نجمع الأنواع التالية من المعلومات:",
    ),
    "inviteFriends": MessageLookupByLibrary.simpleMessage("دعوة الأصدقاء"),
    "invoiceNo": MessageLookupByLibrary.simpleMessage("رمز الفاتورة"),
    "itemName": MessageLookupByLibrary.simpleMessage("اسم الصنف"),
    "labelPost": MessageLookupByLibrary.simpleMessage("بماذا تفكر؟"),
    "langMode": MessageLookupByLibrary.simpleMessage("وضع اللغة"),
    "language": MessageLookupByLibrary.simpleMessage("اللغه"),
    "lastUpdated": MessageLookupByLibrary.simpleMessage("آخر تحديث"),
    "linkAnotherPet": MessageLookupByLibrary.simpleMessage("الارتباط أليف آخر"),
    "location": MessageLookupByLibrary.simpleMessage("الموقع"),
    "login": MessageLookupByLibrary.simpleMessage("تسجيل الدخول"),
    "logout": MessageLookupByLibrary.simpleMessage("تسجيل الخروج"),
    "logoutConfirm": MessageLookupByLibrary.simpleMessage(
      "هل تريد تسجيل الخروج؟",
    ),
    "male": MessageLookupByLibrary.simpleMessage("ذكر"),
    "message_hint": MessageLookupByLibrary.simpleMessage("أدخل رسالتك"),
    "message_validation": MessageLookupByLibrary.simpleMessage(
      "يرجى إدخال رسالة",
    ),
    "myFavorites": MessageLookupByLibrary.simpleMessage("مفضلتي"),
    "myPets": MessageLookupByLibrary.simpleMessage("اصدقائي الصغار"),
    "name": MessageLookupByLibrary.simpleMessage("الاسم"),
    "name_hint": MessageLookupByLibrary.simpleMessage("أدخل اسمك الكامل"),
    "name_validation": MessageLookupByLibrary.simpleMessage("يرجى إدخال اسمك"),
    "newPassword": MessageLookupByLibrary.simpleMessage("كلمة المرور الجديدة"),
    "no": MessageLookupByLibrary.simpleMessage("لا"),
    "noAppointment": MessageLookupByLibrary.simpleMessage("لا توجد مواعيد"),
    "noPetsFound": MessageLookupByLibrary.simpleMessage(
      "لا توجد اصدقاء اليفه الان",
    ),
    "noPetsInVetICare": MessageLookupByLibrary.simpleMessage(
      "لا يوجد اصدقاء أليفة في هذه العيادة، أو قد تم إضافتها بالفعل على SQueak",
    ),
    "notEqualPassword": MessageLookupByLibrary.simpleMessage(
      "تأكيد كلمة المرور غير مطابق لكلمة المرور",
    ),
    "notSpayed": MessageLookupByLibrary.simpleMessage("غير معقم"),
    "notifications": MessageLookupByLibrary.simpleMessage("الإشعارات"),
    "offlineMessagesContent": MessageLookupByLibrary.simpleMessage(
      "أنت حاليا غير متصل بالإنترنت.  التحقق من اتصالك بالإنترنت.",
    ),
    "offlineMessagesTitle": MessageLookupByLibrary.simpleMessage(
      "لا يوجد اتصال بالانترنت",
    ),
    "openAt": MessageLookupByLibrary.simpleMessage("يفتح في"),
    "other": MessageLookupByLibrary.simpleMessage("اخري"),
    "other_valid": MessageLookupByLibrary.simpleMessage(
      "برجاء ادخال عنوان واضح",
    ),
    "other_valid_more_than_30_char": MessageLookupByLibrary.simpleMessage(
      "يجب الا يقل عن حرف او يزيد عن ٣٠ حرف",
    ),
    "ownerDetails": MessageLookupByLibrary.simpleMessage("بيانات المالك"),
    "paid": MessageLookupByLibrary.simpleMessage("المدفوع"),
    "password": MessageLookupByLibrary.simpleMessage("كلمة المرور"),
    "password_hint": MessageLookupByLibrary.simpleMessage("أدخل كلمة المرور"),
    "password_validation": MessageLookupByLibrary.simpleMessage(
      "يرجى إدخال كلمة مرور",
    ),
    "payment": MessageLookupByLibrary.simpleMessage("الدفع"),
    "paymentType": MessageLookupByLibrary.simpleMessage("النوع"),
    "personalInfo": MessageLookupByLibrary.simpleMessage(
      "المعلومات الشخصية (الاسم، عنوان البريد الإلكتروني، رقم الهاتف، إلخ)",
    ),
    "personalization": MessageLookupByLibrary.simpleMessage("اعداد شخصية"),
    "pet": MessageLookupByLibrary.simpleMessage("الأليف"),
    "petDeletedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "تم حذف الاليف بنجاح",
    ),
    "petDetails": MessageLookupByLibrary.simpleMessage("بيانات  الأليف"),
    "petName": MessageLookupByLibrary.simpleMessage("اسم الأليف"),
    "pet_birthdate_hint": MessageLookupByLibrary.simpleMessage(
      "أدخل تاريخ ميلاد لصديقك الأليف",
    ),
    "pet_birthdate_validation": MessageLookupByLibrary.simpleMessage(
      "يرجى إدخال تاريخ ميلاد لصديقك الأليف",
    ),
    "petsVetICare": MessageLookupByLibrary.simpleMessage(
      " أصدقائك الصغار في vetIcare",
    ),
    "phone": MessageLookupByLibrary.simpleMessage("الهاتف"),
    "phone_hint": MessageLookupByLibrary.simpleMessage("أدخل رقم هاتفك"),
    "phone_or_password_hint": MessageLookupByLibrary.simpleMessage(
      "أدخل رقم هاتفك أو كلمة المرور",
    ),
    "phone_or_password_validation": MessageLookupByLibrary.simpleMessage(
      "يرجى إدخال رقم هاتفك أو كلمة المرور",
    ),
    "phone_validation": MessageLookupByLibrary.simpleMessage(
      "يرجى إدخال رقم هاتفك",
    ),
    "pleaseSelectBoarding": MessageLookupByLibrary.simpleMessage(
      "يرجى اختيار نوع الإقامة",
    ),
    "pleaseSelectCage": MessageLookupByLibrary.simpleMessage(
      "يرجى اختيار القفص",
    ),
    "prescription": MessageLookupByLibrary.simpleMessage("الروشة"),
    "price": MessageLookupByLibrary.simpleMessage("السعر"),
    "privacyPolicy": MessageLookupByLibrary.simpleMessage("سياسة الخصوصية"),
    "profile": MessageLookupByLibrary.simpleMessage("ملفك الشخصي"),
    "publish": MessageLookupByLibrary.simpleMessage("نشر"),
    "qty": MessageLookupByLibrary.simpleMessage("الكمية"),
    "rate": MessageLookupByLibrary.simpleMessage("التقييم"),
    "receiveCode": MessageLookupByLibrary.simpleMessage("ادخال الرمز المتلقي "),
    "receiveEmail": MessageLookupByLibrary.simpleMessage(
      "أدخل بريدك الإلكتروني\nلتلقي التعليمات لإعادة تعيين كلمة المرور الخاصة بك",
    ),
    "register": MessageLookupByLibrary.simpleMessage("تسجيل"),
    "reminderOtherHintText": MessageLookupByLibrary.simpleMessage(
      "الرجاء ادخال عنوان الخدمه",
    ),
    "removeClinic": MessageLookupByLibrary.simpleMessage("إزالة العيادة"),
    "removePost": MessageLookupByLibrary.simpleMessage("إزالة المنشور"),
    "replies": MessageLookupByLibrary.simpleMessage("الردود"),
    "reply": MessageLookupByLibrary.simpleMessage("رد"),
    "returnPolicy": MessageLookupByLibrary.simpleMessage(
      "سياسة الإرجاع والاستبدال: يمكن إرجاع العناصر خلال 3 أيام من الشراء مع الإيصال. المنتجات الطبية غير قابلة للاسترداد.",
    ),
    "reviewDescription": MessageLookupByLibrary.simpleMessage(
      "ساعدنا في توفير أفضل رعاية لأصدقائك الصغار .",
    ),
    "reviewMessage": MessageLookupByLibrary.simpleMessage(
      "تم إرسال مراجعتك بنجاح.",
    ),
    "reviewMessage2": MessageLookupByLibrary.simpleMessage(
      "يمكنكم مشاهدة مراجعتكم عن طريق شاشة المواعيد.",
    ),
    "reviewTitle": MessageLookupByLibrary.simpleMessage(
      "مراجعه للعيادة و الدكتور",
    ),
    "save": MessageLookupByLibrary.simpleMessage("حفظ"),
    "search": MessageLookupByLibrary.simpleMessage("بحث"),
    "searchFor": MessageLookupByLibrary.simpleMessage("البحث عن خدمة"),
    "searchValid": MessageLookupByLibrary.simpleMessage("إضافة خدمة"),
    "securityAudits": MessageLookupByLibrary.simpleMessage(
      "تدقيقات الأمان المنتظمة",
    ),
    "securityAuditsDesc": MessageLookupByLibrary.simpleMessage(
      "نقوم بإجراء تدقيقات أمنية منتظمة لتحديد ومعالجة المخاطر الأمنية المحتملة. فريقنا مكرس لمراقبة وتعزيز تدابير الأمان باستمرار لضمان سلامة وخصوصية بيانات عيادتك.",
    ),
    "selectBoarding": MessageLookupByLibrary.simpleMessage("اختر نوع الإقامة"),
    "selectBoardingBy": MessageLookupByLibrary.simpleMessage("اختر طبيبًا"),
    "selectPet": MessageLookupByLibrary.simpleMessage(
      "يرجى اختيار أليف لبدء الخدمة",
    ),
    "selectPetType": MessageLookupByLibrary.simpleMessage("اختر نوع الاليف"),
    "send": MessageLookupByLibrary.simpleMessage("إرسال"),
    "sendMeNow": MessageLookupByLibrary.simpleMessage("أرسل لي الآن"),
    "sentVerification": MessageLookupByLibrary.simpleMessage(
      "لقد أرسلنا رمز التحقق إلى",
    ),
    "service_hint": MessageLookupByLibrary.simpleMessage("اختر خدمة"),
    "service_validation": MessageLookupByLibrary.simpleMessage(
      "يرجى اختيار خدمة",
    ),
    "setLanguage": MessageLookupByLibrary.simpleMessage("ضبط لغة التطبيق"),
    "settings": MessageLookupByLibrary.simpleMessage("الإعدادات"),
    "sex": MessageLookupByLibrary.simpleMessage("الجنس"),
    "shareApp": MessageLookupByLibrary.simpleMessage("مشاركة التطبيق"),
    "sharingInfo": MessageLookupByLibrary.simpleMessage("مشاركة معلوماتك"),
    "sharingInfoDesc": MessageLookupByLibrary.simpleMessage(
      "لا نقوم ببيع أو تداول معلوماتك الشخصية. ومع ذلك، قد نشارك بعض المعلومات مع مزودي الخدمات الموثوق بهم لدعم وتحسين خدماتنا. يُطلب من هؤلاء المزودين الالتزام بمعايير حماية البيانات الصارمة ويُسمح لهم فقط باستخدام المعلومات للأغراض الموضحة في سياسة الخصوصية الخاصة بنا.",
    ),
    "signOut": MessageLookupByLibrary.simpleMessage("تسجيل الخروج"),
    "signUp": MessageLookupByLibrary.simpleMessage("التسجيل"),
    "signUpAsADoctor": MessageLookupByLibrary.simpleMessage("التسجيل كطبيب"),
    "skip": MessageLookupByLibrary.simpleMessage("تخطي"),
    "spayed": MessageLookupByLibrary.simpleMessage("معقم"),
    "speciality": MessageLookupByLibrary.simpleMessage("التخصص"),
    "species": MessageLookupByLibrary.simpleMessage("الأنواع"),
    "speciesOne": MessageLookupByLibrary.simpleMessage("النوع"),
    "startAppointment": MessageLookupByLibrary.simpleMessage("ابدء الموعد"),
    "submit": MessageLookupByLibrary.simpleMessage("إرسال"),
    "swapPet": MessageLookupByLibrary.simpleMessage(
      "مرر لليسار أو اليمين لاختيار أليف",
    ),
    "taxId": MessageLookupByLibrary.simpleMessage("الضريبة"),
    "terms": MessageLookupByLibrary.simpleMessage(
      "بالتسجيل أنت توافق على شروط الاستخدام وسياسة الخصوصية",
    ),
    "time": MessageLookupByLibrary.simpleMessage("الوقت"),
    "title_hint": MessageLookupByLibrary.simpleMessage("أدخل العنوان"),
    "title_validation": MessageLookupByLibrary.simpleMessage(
      "يرجى إدخال عنوان",
    ),
    "total": MessageLookupByLibrary.simpleMessage("المجموع"),
    "totalAmount": MessageLookupByLibrary.simpleMessage("المجموع"),
    "trademarkInfo": MessageLookupByLibrary.simpleMessage(
      "معلومات العلامة التجارية",
    ),
    "unfollow": MessageLookupByLibrary.simpleMessage("الغاء المتابعة"),
    "unfollowConfirmation": MessageLookupByLibrary.simpleMessage(
      "تأكيد الغاء  المتابعة",
    ),
    "uniqueCode": MessageLookupByLibrary.simpleMessage("الرمز الفريد"),
    "updateAppointment": MessageLookupByLibrary.simpleMessage("تحديث الموعد"),
    "updateNotification": MessageLookupByLibrary.simpleMessage("إشعار التحديث"),
    "updateNotificationDesc": MessageLookupByLibrary.simpleMessage(
      "قد نقوم بتحديث سياسة الخصوصية الخاصة بنا من وقت لآخر. عندما نقوم بذلك، سنقوم بإبلاغك عن طريق نشر السياسة الجديدة على هذه الصفحة. تقع على عاتقك مسؤولية مراجعة هذه السياسة بشكل دوري للاطلاع على أي تغييرات.",
    ),
    "updateProfile": MessageLookupByLibrary.simpleMessage("تحديث"),
    "updateSuccess": MessageLookupByLibrary.simpleMessage("تم التعديل بنجاح"),
    "updateVersionModuleButtonIgnore": MessageLookupByLibrary.simpleMessage(
      "تجاهل",
    ),
    "updateVersionModuleButtonLater": MessageLookupByLibrary.simpleMessage(
      "لاحقا",
    ),
    "updateVersionModuleButtonUpdateNow": MessageLookupByLibrary.simpleMessage(
      "تحديث الآن",
    ),
    "updateVersionModuleContent": MessageLookupByLibrary.simpleMessage(
      "يحتاج تطبيقك إلى التحديث\n",
    ),
    "updateVersionModuleContent2": MessageLookupByLibrary.simpleMessage(
      "هذا الإصدار من التطبيق قديم.\nيرجى الانتقال إلى المتجر للتحديث",
    ),
    "updateVersionModuleTitle": MessageLookupByLibrary.simpleMessage(
      "تحديث التطبيق",
    ),
    "userControl": MessageLookupByLibrary.simpleMessage(
      "تحكم المستخدم في البيانات",
    ),
    "userControlDesc": MessageLookupByLibrary.simpleMessage(
      "لديك السيطرة الكاملة على البيانات التي تشاركها معنا. يمكنك تحديث أو تعديل أو حذف معلوماتك الشخصية ومعلومات العيادة في أي وقت من خلال منصتنا. نوفر لك إعدادات واضحة وخيارات لإدارة بياناتك لضمان أنك دائمًا تتحكم في المعلومات المخزنة والمشتركة.",
    ),
    "value": MessageLookupByLibrary.simpleMessage("القيمة"),
    "verification": MessageLookupByLibrary.simpleMessage("التحقق"),
    "version": MessageLookupByLibrary.simpleMessage("الإصدار"),
    "versionNumber": MessageLookupByLibrary.simpleMessage("الإصدار"),
    "view1Reply": MessageLookupByLibrary.simpleMessage("عرض رد واحد"),
    "viewReplies": m0,
    "yes": MessageLookupByLibrary.simpleMessage("نعم"),
    "yourAppointments": MessageLookupByLibrary.simpleMessage("مواعيدك"),
    "yourClinic": MessageLookupByLibrary.simpleMessage("عيادات"),
    "yourConsent": MessageLookupByLibrary.simpleMessage("موافقتك"),
    "yourConsentDesc": MessageLookupByLibrary.simpleMessage(
      "باستخدام تطبيقنا، فإنك توافق على سياسة الخصوصية الخاصة بنا.",
    ),
    "yourPets": MessageLookupByLibrary.simpleMessage("اصدقاءك الصغار"),
    "yourProfile": MessageLookupByLibrary.simpleMessage("حسابك الشخصي"),
    "yourUpcomingAppointments": MessageLookupByLibrary.simpleMessage(
      "مواعيدك القادمة",
    ),
  };
}
