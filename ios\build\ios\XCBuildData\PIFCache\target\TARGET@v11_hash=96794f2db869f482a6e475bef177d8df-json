{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981c77546c772c8fa92811404e63753717", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e981f41e620de1cd6d50b2749efcf04fb33", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988ee81381a38e64d31876d21051a240c2", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9848e152e4d96a1a626b352267b0077dab", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988ee81381a38e64d31876d21051a240c2", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e38ea13e296830d1409a7c460a72995b", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98675f3f4546a14295f7608bd6c461c5a1", "guid": "bfdfe7dc352907fc980b868725387e98bde187d966e2cac997df0189b015c39c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9850598903e9fa53a393bd06cf4ca5b671", "guid": "bfdfe7dc352907fc980b868725387e98f55f04b61f2f457399dab2b2afd43610"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98197d229f919a436fe30c98f6e044187b", "guid": "bfdfe7dc352907fc980b868725387e98483d601a997f6d6b719d80e24407a25c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b09cf3ab095eb18bcadb692a9940a613", "guid": "bfdfe7dc352907fc980b868725387e98ade6f04fe6e55ee75bcb9d3ea6215c97"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e21230b4258e7ce01ef8d0610c215e3", "guid": "bfdfe7dc352907fc980b868725387e988069855d158074e45b1e721da51df83d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9872232872398fad83ba7a2ba08547a061", "guid": "bfdfe7dc352907fc980b868725387e985acc168c44292826f18b8e371bc2f691"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d49189754f11017f96e413a52bb23e44", "guid": "bfdfe7dc352907fc980b868725387e982c0c313dff7bff7196aa84d477d32088"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989f134e0532f88ab333471cc7d05a03fa", "guid": "bfdfe7dc352907fc980b868725387e9884b412b7d43e2e413adf1def51787f1a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cf6f48b68e8c1c7a9d207d55293a7cb8", "guid": "bfdfe7dc352907fc980b868725387e987a87215f13977e0c357fffbad74a1409"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989e2e5e5fc7b5ef156fc4fbadf9523cf8", "guid": "bfdfe7dc352907fc980b868725387e98b1d2784d2b357c5553b4142eb89bcb55", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985732bd0d656ea32db54397503ecff217", "guid": "bfdfe7dc352907fc980b868725387e98d32220ab2056fa5563674c54bab605c7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982287a0ec14322be3693c58140162d0b3", "guid": "bfdfe7dc352907fc980b868725387e98d262d7c2bc537424887fb47aa058b241"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f5fcaa7c206a806938ceff6af592cd2e", "guid": "bfdfe7dc352907fc980b868725387e9845a119b9fb42b438c4753e94ab7c5076"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d44c2c81823e19eea02b6621fe9be680", "guid": "bfdfe7dc352907fc980b868725387e9883c3a8847853950ddad7fddc10744c59"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986161582bf3cfa287d7f3802026f69ac5", "guid": "bfdfe7dc352907fc980b868725387e98acb745ab68d94b7b5ec6fb55a01e9f55"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b4441e8e3de41d7a5f93e29cbf5cef4a", "guid": "bfdfe7dc352907fc980b868725387e9847092f35543175bd5a45588b7734fe66"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98471e75070ef0868a804b46dddc65c6fa", "guid": "bfdfe7dc352907fc980b868725387e98f83755e39dd3e1e19d98035ac41a7496", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981682e69337fe9876c69e05a406a7fe0c", "guid": "bfdfe7dc352907fc980b868725387e98ac508a4ac36077cff5470dd75db1bddf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b0138a9b18c714ce5f990b11494ccd46", "guid": "bfdfe7dc352907fc980b868725387e987c232cd7cedce44fef5cf18adb941c27"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9896fd1540586d924ae206601022deb794", "guid": "bfdfe7dc352907fc980b868725387e986f0e6f124402b7bc2ed23a0d8d6f4a91"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98804719bc0b8d8a0183565fd813e4d1fc", "guid": "bfdfe7dc352907fc980b868725387e9849a84f15d42248d25dee5a4e85756745"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982a1dba9fbab1eb807dadb39b287e1b48", "guid": "bfdfe7dc352907fc980b868725387e98748396ef39dca5021d4afc5fdb777f66"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9861854bc7e6641e8067e8f9aa47f51ff5", "guid": "bfdfe7dc352907fc980b868725387e98a38b44bea08b31d1017e1bcf66751a58"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985068a99f89c6a3996c2ce2391f3ee4f9", "guid": "bfdfe7dc352907fc980b868725387e98ee3557a8e2bc26e48393f110ed1a516f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98104ae55997192ee793c0e74a70c851ad", "guid": "bfdfe7dc352907fc980b868725387e98211d2aee6d76c3a6dac1f1da226222bc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98162330de0d056cf85e814dbf24e9820b", "guid": "bfdfe7dc352907fc980b868725387e984638789c0032494c34e7523d4a3d76e7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986f3772c8a4d0434b7543eff2b71161ef", "guid": "bfdfe7dc352907fc980b868725387e989ed792d1eb3bc90aa0e8f6e680c48ab3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98145b72d6a2ab6a95f2e830a92dc839aa", "guid": "bfdfe7dc352907fc980b868725387e98f5a87253630d413c20c8bb2439b30cab"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b16e2835a6f9f0d059e39ada56024eac", "guid": "bfdfe7dc352907fc980b868725387e9866ef3b1461fb19f4b3e9d491e6705129"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986666036355e383da11b6d4feab5f6ca6", "guid": "bfdfe7dc352907fc980b868725387e98da72f2dfbd6b4a49f87b23dbe498a382"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f672229e2f6e87097996a85a24eda1d9", "guid": "bfdfe7dc352907fc980b868725387e984a7207143b1cfdd539174c3405695b5c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9856ea31f4427c8ca13ab631f01463cdd3", "guid": "bfdfe7dc352907fc980b868725387e98c451d234d82d68b8d1d9382f0115ea13", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a8e87840361b4c41e4ca6643649eec56", "guid": "bfdfe7dc352907fc980b868725387e989def348c123cd1f10785f1d95cf8594b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a30baaebb8c6c469450facb0edd72648", "guid": "bfdfe7dc352907fc980b868725387e987d253c11a75edbee6a4b3b3dd15a5c47"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98345a6d3584e4643eeedf38ae6ca3b3f7", "guid": "bfdfe7dc352907fc980b868725387e9896a1e88c27f1737700c6f1891a56cf37"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985bed27487aadc8a8b74b762a6b1e2908", "guid": "bfdfe7dc352907fc980b868725387e98ff47f989b716a24284a2142d06ce1a3e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fd5b66ed9de92c08fa3daf3013af635b", "guid": "bfdfe7dc352907fc980b868725387e988c4ab1fde3b77032f38f67309e470ad2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982da5f279ce4b6ae42d12fa4f492ea64e", "guid": "bfdfe7dc352907fc980b868725387e980dff9335f6da53310264c886f6166f0a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981755d877f7d96e71f3ac8844346b9983", "guid": "bfdfe7dc352907fc980b868725387e9802a467e0e841ec2c2f40d4f640d4d310"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d509a2d655db94d663d1764a7ca8867", "guid": "bfdfe7dc352907fc980b868725387e98237583a9002432342083caef17411d4a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9823d86af3772e75ba03bcb68d4fef6678", "guid": "bfdfe7dc352907fc980b868725387e98b3ed2df327e27a54cefc5da4e3626bdd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988de93a25e0e823f05e5176fea5154631", "guid": "bfdfe7dc352907fc980b868725387e98a6c034c9161d6511880f61cec5115130"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9832580fc8d0677979dbae566d04a53d95", "guid": "bfdfe7dc352907fc980b868725387e9863b54abfc8b8622779acb37fb88ec751"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987c99da7b949e14f634b2580a4e3473ce", "guid": "bfdfe7dc352907fc980b868725387e9832099bad105880a5b25c44bc1446da98"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9874d50e79fd6bb4bb05c7161c0884c180", "guid": "bfdfe7dc352907fc980b868725387e98914ff5e04c15490555e4b98494750999"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec96fd11c8fe46dea417a81930e3b1f4", "guid": "bfdfe7dc352907fc980b868725387e98e045aa666b2d386d503e660f839650f5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e3261f713e45bd43ab4e80a0fb0296ff", "guid": "bfdfe7dc352907fc980b868725387e982518e0d0fd880a0d3ca23966fcda05a1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9803cfdec9b461754e1eae12821305ffb7", "guid": "bfdfe7dc352907fc980b868725387e98c27425d6c5e9c4d29e0084b239ef2e3f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f66b407268c561479cbd66bcea319ac8", "guid": "bfdfe7dc352907fc980b868725387e98ad81b36a7b9d1b9e86841146ba4b98ac"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9856ea6c310610e7b25ce776450604624e", "guid": "bfdfe7dc352907fc980b868725387e98e771930a2f0a4e929421a62c2e323a45"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aca8609bc08e5dfcbe90b3fe214310ad", "guid": "bfdfe7dc352907fc980b868725387e98bd218d5cc01fcb192cc7fadfe7f9095d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98be338f63428f035b5689a84d06da5bdf", "guid": "bfdfe7dc352907fc980b868725387e9801921d69af540d0fa6463183f4ce58c3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9888343bedf3e9f9cce301f15fd10d2c4f", "guid": "bfdfe7dc352907fc980b868725387e9848aee7802503c56cb0808a0ff41856c3"}], "guid": "bfdfe7dc352907fc980b868725387e98640a726372b94d2ee1d72ba657403a85", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e3a573bc4d933721328085eb94a7508b", "guid": "bfdfe7dc352907fc980b868725387e9828d03014665c0dc97115bd092bc5a885"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980222c2eaa5308646c8c27ec3258d347c", "guid": "bfdfe7dc352907fc980b868725387e98b317b658c4cf1d2ed6db428dc9739048"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e5efed8b94e8ac7bf666a1d3ca136439", "guid": "bfdfe7dc352907fc980b868725387e98ab5afc37daad7c675629784ce9747218"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9817473cadf5b12c96094f62eb307a7b1a", "guid": "bfdfe7dc352907fc980b868725387e987dc7568fc486719642e450edbf315694"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981b43b4192aba54ab9ff4d52a78a6cb87", "guid": "bfdfe7dc352907fc980b868725387e9851855111885c01a4f0a8d0f34a166143"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ed45eeb3c4a113bcbb042d5f660d746", "guid": "bfdfe7dc352907fc980b868725387e988041a69786b7e806f6e8f01e40aeff58"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987535fe5652f08604d5befe7963b45770", "guid": "bfdfe7dc352907fc980b868725387e988d5a2f1f25042ffeb49dd393c13ed56c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98266deeb4b22e8fba3e22036c3cb96389", "guid": "bfdfe7dc352907fc980b868725387e9889996867975655f416ba83113727ca99"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985a8e910042582bc529752c02d4e8ff3f", "guid": "bfdfe7dc352907fc980b868725387e9878ff48fd4047f7ffc45e6c3a9adea610"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9882bb01f4208f36ce0c242c43a67cd113", "guid": "bfdfe7dc352907fc980b868725387e988cd25291212ff3c8e992b98b3e087f87"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b2a419e9e147ba925124c73b30a09661", "guid": "bfdfe7dc352907fc980b868725387e98ce4fec845999ebe890b75fb59864d7e7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98008f9c5ad54a9b70d88809b66e91070a", "guid": "bfdfe7dc352907fc980b868725387e982df5c6b19aac6a073c6fcc96fa09a9a1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aeff5cef1a98d405cb93cece8cd7a57d", "guid": "bfdfe7dc352907fc980b868725387e98aaa001fc52b8fcdbb7cda5e385f4d1c5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985d0bcac1530350ca7726ea1acc6502a3", "guid": "bfdfe7dc352907fc980b868725387e98cebf48ebd030220fc99776f04980238d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986ce403a1696672bcac883d72528025dd", "guid": "bfdfe7dc352907fc980b868725387e98cfafcf54bfb13daa0f37b059e4aa28a2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989436a76fb84bbaf581cc919c5a0df95a", "guid": "bfdfe7dc352907fc980b868725387e98a7776f247de3c6ee182b412c8067fa46"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e507a4d23b8ebeda4789a17dea5fd1be", "guid": "bfdfe7dc352907fc980b868725387e98dd736876191db638b5fb63d4858229e4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d4162dcf363dd6c552259569f13fc82", "guid": "bfdfe7dc352907fc980b868725387e987cc0bb884e089e6f434938a14a353727"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bcf54fe71174773df771eecd18a513a2", "guid": "bfdfe7dc352907fc980b868725387e98af19f172996fe880c357a9d3610ac08e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98731291e821874007e9665f0a21366124", "guid": "bfdfe7dc352907fc980b868725387e98e3fc0d2e8d604a7288ac4eb8a124c901"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e14a22c7134f10445e5d115d8dd6d419", "guid": "bfdfe7dc352907fc980b868725387e98674c08de876cf5be235177f7c8a534a9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9893440093d1f0ea833ff2ab39e71424ac", "guid": "bfdfe7dc352907fc980b868725387e98658e53dbcf0178fcef8acadc8fe7cb60"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988facf143b80f11598a5fc834dc8438f5", "guid": "bfdfe7dc352907fc980b868725387e98c62ac003765ad640d35191505cae29b3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98616e7cc27e393ac5f80dd41f38813f7f", "guid": "bfdfe7dc352907fc980b868725387e98c2236fcc96d5792e41d5c2c1f4951f05"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982fa9de61c7aafbff6c70780c4f72a088", "guid": "bfdfe7dc352907fc980b868725387e983520ccc1a472e21f33b599b5221e61e4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f5da0e2f28ce11b862a291f04b96d9cb", "guid": "bfdfe7dc352907fc980b868725387e98ecdf4a66c93e2adaee5b0efde0f39f2a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988cfb3b81bdb9eadcbd108d8647140597", "guid": "bfdfe7dc352907fc980b868725387e9851371d6d835c11c44ebf70e5ea19cdd4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98717cbb5964a63fd053ca9c8397475089", "guid": "bfdfe7dc352907fc980b868725387e98fd284dc255151b44e17578ed919bc814"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987cd428bf29069f3607316c2e506c398f", "guid": "bfdfe7dc352907fc980b868725387e98512ebed519bddbc0bbfc61371cd830b7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f21a85982b0cfb14c2ff17aca98bdc0f", "guid": "bfdfe7dc352907fc980b868725387e98ee55be810aa2b5973098ac304fa5bdef"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d49326f102b56cc2129f32b0c788f462", "guid": "bfdfe7dc352907fc980b868725387e981cc5430ca9396f7ea34a41c54fe93aa5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d58a81dd46fb729ca9e298a4af26841f", "guid": "bfdfe7dc352907fc980b868725387e98b04ab23692e3c81a7376b4bd2c33a9de"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98938f1e063ee3d0d2e845842d691913d5", "guid": "bfdfe7dc352907fc980b868725387e985fdcf45cbc0e0c72f5f85b8c441a5309"}], "guid": "bfdfe7dc352907fc980b868725387e9899b03acfe589c10b5013a644798bb990", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9824f5d6ec5088eecbb83955c855580732", "guid": "bfdfe7dc352907fc980b868725387e98c33f800a62dfa226d8d6c21df0515f25"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987cbcfbbfed448f33604b899a4f76dac9", "guid": "bfdfe7dc352907fc980b868725387e986e07d302b579a6ba328e2b90bb09a7a9"}], "guid": "bfdfe7dc352907fc980b868725387e98d051f6951acb234c9948fb0dad9eecdb", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98063ce6b819923ccc7707c54152509518", "targetReference": "bfdfe7dc352907fc980b868725387e98974c3b2447afb83a5c25c38d101a48ab"}], "guid": "bfdfe7dc352907fc980b868725387e98fe98b27e44564f28dca5ff05451e0da9", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations"}, {"guid": "bfdfe7dc352907fc980b868725387e98974c3b2447afb83a5c25c38d101a48ab", "name": "FirebaseMessaging-FirebaseMessaging_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98d3c8dfff2c580c352f83d3850ad17775", "name": "GoogleDataTransport"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e980062393f91a1d2d94e3e5ed3a5aa5da9", "name": "nanopb"}], "guid": "bfdfe7dc352907fc980b868725387e983da17a3564c774dfaa331fa07754d2bc", "name": "FirebaseMessaging", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98b3b0fadaedeb0138a07668440d83e3b3", "name": "FirebaseMessaging.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}