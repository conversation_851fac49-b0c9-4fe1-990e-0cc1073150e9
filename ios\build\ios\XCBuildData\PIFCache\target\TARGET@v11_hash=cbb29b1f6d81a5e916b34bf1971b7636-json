{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9864007a1349b3e0e4bd6376a49fdb2b70", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Daleel/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Daleel/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98519c0f1aabf34eacbf6f755b813f1496", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9884115255b415c98e498d842711c3efa0", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Daleel/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Daleel/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ec62137b0ee28ca4265944856877be27", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9884115255b415c98e498d842711c3efa0", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Daleel/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Daleel/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9832ebf16d88dad2729e444c32094aa46a", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ee14129618481c31f04f3a05af97c55c", "guid": "bfdfe7dc352907fc980b868725387e980eb9d23368b95b4f0923cd46a43136d3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986c45be175d76afc36afe8665ed268f61", "guid": "bfdfe7dc352907fc980b868725387e98f457cef344e960dcd5e86a14876a724f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d3361b6567d97ada4e71647846d73d8e", "guid": "bfdfe7dc352907fc980b868725387e98448d0ae29de8f3c224c2dcfdec1456de", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b371858b88c29817e45532cabf29e6d8", "guid": "bfdfe7dc352907fc980b868725387e98813b7079aa7de27e6db954b27ad09619", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aebe8f6e54c73cba858f6b5494abf677", "guid": "bfdfe7dc352907fc980b868725387e981ea8f363f0722babcaed14c60409bf02", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a00b43506cb979f30510b46904b15a13", "guid": "bfdfe7dc352907fc980b868725387e98e7ac745c3ef27b02fac012aa73444821", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e5ea7e5718b82a82da44fec6a39e09ee", "guid": "bfdfe7dc352907fc980b868725387e985678ecb94e589405999c12157a408775", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987bd454000f98f49a8704d0667701a3f0", "guid": "bfdfe7dc352907fc980b868725387e98cd87b467ebfeb07dfeb1385499e6cd77", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a1a4db3066d530ed6c558f53ad1292f7", "guid": "bfdfe7dc352907fc980b868725387e9871c163114fc9cc28ecff5b1b2d0a4de2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98141797ce7daa261686f314f77a28393c", "guid": "bfdfe7dc352907fc980b868725387e981978a187afff967da6262086dc0e6fc3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984d3fb3cd5780f464d8d0de66a6364f57", "guid": "bfdfe7dc352907fc980b868725387e9826c907de3610a0743fa9d834fe3a26ab", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983692b0ce07dd1f6f6863f12fdb2bd4fb", "guid": "bfdfe7dc352907fc980b868725387e980aab54a36f4355218c04e0f9e0c22709", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ca7c908f671917f20f20ac24d4f6b524", "guid": "bfdfe7dc352907fc980b868725387e98dc02dbc796bf1a1c79ffc8c7bf3cea72", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b151923c172ac5276d965d8f539360ea", "guid": "bfdfe7dc352907fc980b868725387e98d24acab5f9f5227c374c09fc08c9e695", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aaa3b4f7c89e2b0d51ee926e458dc992", "guid": "bfdfe7dc352907fc980b868725387e98eed3f758e63ab4fdfc010074b9de804b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b9a5ab55b4e2b10de672db3299d8a28", "guid": "bfdfe7dc352907fc980b868725387e981f08693bd23a6d005b3df15e8e699fde", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fa99c794a5164a2d47e0d95a925bd71d", "guid": "bfdfe7dc352907fc980b868725387e98bf1362e319e49dc96debe2ff1acb9025", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988714b0b7768d075b66f5722499bcd809", "guid": "bfdfe7dc352907fc980b868725387e985b2090fddc5dd999ec75f69196ebf8ad", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982cf99ce1eabded2100e3db7a7fdc347e", "guid": "bfdfe7dc352907fc980b868725387e98232fcc544ef53e9da9ad40243b28e5a2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98966515679a793fa419619776f5ebaeb9", "guid": "bfdfe7dc352907fc980b868725387e98a63597065bd83c36166c1f6aeac7172a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ee66b85b0f4be2b0bec096f84b3e43de", "guid": "bfdfe7dc352907fc980b868725387e9829db91ca625511289316787d6d10ea44", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986f9a221a23af83e56898f4918ec675dc", "guid": "bfdfe7dc352907fc980b868725387e98c0b06c2b33df0dfa02bfa09f9932011c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982cbfdf16ddd08219a52bdbcc8fbf4216", "guid": "bfdfe7dc352907fc980b868725387e9851ba8fa6ddb817e605d2665626d9d641", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e986a03fa67e33d48dce94be2b8eb2259ec", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98bcaaee60cf5aeee8b70debaacdbe0a28", "guid": "bfdfe7dc352907fc980b868725387e98277468ca209872376e5f206f84332670"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c3b4371fc9bad831121a77699d3ac722", "guid": "bfdfe7dc352907fc980b868725387e98e9a6f3c2565cf9a05dd3d353eaa7a4c8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b2380f17dd16c3968ae38cade6bbf069", "guid": "bfdfe7dc352907fc980b868725387e986111772b572857d7961a008265fc355c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b631879b362efb6a43e313b419f531d1", "guid": "bfdfe7dc352907fc980b868725387e983996f9c15e1fbf682fcc40770fd256d7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c1b15cd73fd95b124cef8c33829adb4a", "guid": "bfdfe7dc352907fc980b868725387e9858a8497e775e00416ce705748e767ece"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f72cdf5bf8c2ed096f0ae3d5dcce8701", "guid": "bfdfe7dc352907fc980b868725387e98c6bee8141090191321d7771c3a1f978b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98116ab3f875f1a1ef69491bb2428efb57", "guid": "bfdfe7dc352907fc980b868725387e981cebb2289f1bb2bf7dee62eea371ce0c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983fb42c3ee0bfe17f38c34bd35c6799b6", "guid": "bfdfe7dc352907fc980b868725387e98f1f6bd36d03eee3c0bdc3bc98e83ea19"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985411028b44e97c7633f80dc0c17e461f", "guid": "bfdfe7dc352907fc980b868725387e98a32f894597a07a2a5504a8b9bcfdabdc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98477b442719034f0361a9833a31c4c78f", "guid": "bfdfe7dc352907fc980b868725387e9800ac44543ad413e32dda38d808c2afef"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98882518bdb4a785b6794d46d28b7fcbb0", "guid": "bfdfe7dc352907fc980b868725387e980a3b558904dbc56f2153ae21439d6437"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c86eb00d1e02de531dc3499f52a4b3f", "guid": "bfdfe7dc352907fc980b868725387e9803ab6e9df07f67a9975e5c61b861ce4a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9860338e4fe2d7c0daa36902fc4a4129bd", "guid": "bfdfe7dc352907fc980b868725387e981850b344567e5543096f0a83d9eac0b2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cbb04e973cd7a593897c766fac811bea", "guid": "bfdfe7dc352907fc980b868725387e984a502a85d82ab7ae770f404ac3bb3b9c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f0f78d400a41acd0807d9554f61221ab", "guid": "bfdfe7dc352907fc980b868725387e984c79dfb7fcb3ff07f9037801d36c7134"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b5051fe4561aa63d7413aa5013c2f92e", "guid": "bfdfe7dc352907fc980b868725387e98832efc148e497d7aa81cf3800da27235"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98defbc5908ee71b360ad4510cf3a9a1de", "guid": "bfdfe7dc352907fc980b868725387e988b056d871bf720cc83d9b2c7291eec07"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e795cd6424c85db84588e74ebf7e5ed3", "guid": "bfdfe7dc352907fc980b868725387e98afb0626ba205a690e15081763b4adef1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98abd87183247ec2910b44c1f226cf2deb", "guid": "bfdfe7dc352907fc980b868725387e98cf6232b3e1482ebfb128206cef66a17c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a2f5d1d06230e28ce822add7ef8c4dd", "guid": "bfdfe7dc352907fc980b868725387e98c76565777e1dccbddf6eca764d227a41"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985f3f2048a3e1421b61bad4927ce39a92", "guid": "bfdfe7dc352907fc980b868725387e98dba12151ab617cdd659014f8477b0bfb"}], "guid": "bfdfe7dc352907fc980b868725387e98be6229230a4715433df2f8e74fbafc5b", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9824f5d6ec5088eecbb83955c855580732", "guid": "bfdfe7dc352907fc980b868725387e980797c2152e50219ee4196549bb34f857"}], "guid": "bfdfe7dc352907fc980b868725387e984d290968aff9eafa4ed5b85c80a8c610", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98fa0d11ed0b4e1a85c13d68e37d1547e0", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e9802f35ab680609a626ebd2ddd692a3822", "name": "permission_handler_apple-permission_handler_apple_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98ef10255b706f98e1e88fae00855b0968", "name": "permission_handler_apple", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f8f53f8ba4165e76c7481b24262177ed", "name": "permission_handler_apple.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}