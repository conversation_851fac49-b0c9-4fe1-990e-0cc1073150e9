{"buildFiles": ["C:\\Users\\<USER>\\fvm\\versions\\3.29.1\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "E:\\flutter_projects\\squeak\\android\\app\\.cxx\\Debug\\5m1o4206\\x86_64", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "E:\\flutter_projects\\squeak\\android\\app\\.cxx\\Debug\\5m1o4206\\x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}