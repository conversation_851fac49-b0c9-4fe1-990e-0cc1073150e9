{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98bef24c887c5234514ce25948c8b60f57", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e05f71067c704e48bfba998a813ba3c1", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982daf3f28e8ee6d9599893da3c949c0ef", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98acc2885013abc00014647caffe3405f3", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982daf3f28e8ee6d9599893da3c949c0ef", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e989c439cbcbaf12f01a261c835728d5ff1", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e5025fce17506b5b05dc735d2e129b8d", "guid": "bfdfe7dc352907fc980b868725387e98b3277946cabe7aae9666c81743237110", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e987df50fd914e2ebc500882974e5da856c", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e989241ec21dce7536ad5cb8348307c13e3", "guid": "bfdfe7dc352907fc980b868725387e980ee1e5d233e69ef926a5f09c211a86e8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98845d927fb825f3d72b30bbab3636d639", "guid": "bfdfe7dc352907fc980b868725387e984f4e7faef11d2a0dfe48098a40d47d04"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989dc65a603782d8535e18bbb15e3e31cc", "guid": "bfdfe7dc352907fc980b868725387e98c94b3051e85e090f3aea308674d1e63f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989940bfec0f9a465c31dcf9df873a64f0", "guid": "bfdfe7dc352907fc980b868725387e9858d3164090591e116b76b5e70f401030"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a5189a7454699b6a9a765acf091f730f", "guid": "bfdfe7dc352907fc980b868725387e9834fb5f9ed730847f37203cc54e3df06e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cc7966e61dd0b97cc35bf0a54d16c8bf", "guid": "bfdfe7dc352907fc980b868725387e984af230f145172ebec58faed0bf4292da"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984daad2a11e4b6cc3d125c27984c7fd58", "guid": "bfdfe7dc352907fc980b868725387e98f3435da0de53896c040b36a030a54e7b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bd8ee62f2febbdc64fc4854391964db7", "guid": "bfdfe7dc352907fc980b868725387e98000fbfdd8eabf16eac20854b3ef08316"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fa1400c92c1a220299668f7009a57729", "guid": "bfdfe7dc352907fc980b868725387e98769312a4921234d32375e9962819d112"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b858c14fb29aad1d417e37252ce64284", "guid": "bfdfe7dc352907fc980b868725387e98442c34d94eea3817021f00fc7f8ac2d3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f9ce8d25316ec5c8fe140eefa9efc2a3", "guid": "bfdfe7dc352907fc980b868725387e9868fb78be29deac619402c70e56802fb3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b201af19b31d8b42850a3ee7cc4cc044", "guid": "bfdfe7dc352907fc980b868725387e982ea68d27d4257ac6bb03a1b37d9d6bfa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987fcaf66b5d66eaeb43bae669a3f02340", "guid": "bfdfe7dc352907fc980b868725387e98c3b78f6ca50a62e35e5b33345bdf9d22"}], "guid": "bfdfe7dc352907fc980b868725387e9892bb154ae89d5917d5c059b4fa9f71d7", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9824f5d6ec5088eecbb83955c855580732", "guid": "bfdfe7dc352907fc980b868725387e98496e65e02a342f20a928ab3c810b6045"}], "guid": "bfdfe7dc352907fc980b868725387e981e7ae030fcbd721e308fc8e9f0649b69", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e984d5c8a0660b011cc1089ce0e11b112e6", "targetReference": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f"}], "guid": "bfdfe7dc352907fc980b868725387e9879443e4a918bb2443bffa10ad7b95d41", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f", "name": "FirebaseCoreInternal-FirebaseCoreInternal_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e983d86e87924acfad2934921ce7ad9fbea", "name": "FirebaseCoreInternal.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}