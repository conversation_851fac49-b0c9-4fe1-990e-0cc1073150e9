{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987ea6d71b4fed56920588ca202b700f08", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98803d513f9f4ef29d8e959c6dc1417ffa", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9892fc5747a0e7166219edb5edabfab326", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e987496ae0b42aec4ae919b548c05ede613", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9892fc5747a0e7166219edb5edabfab326", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c6f43f62db0f4a85fdab17190cfca515", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e988b7c136b249402f1e8d0398941ce02de", "guid": "bfdfe7dc352907fc980b868725387e987e4395016bff527459a9e5d9f24d84c9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981f5086d8e60fb6eb84ffd230e4744f39", "guid": "bfdfe7dc352907fc980b868725387e98951e85736267a2731c1f8e338903a92c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98956a71a310de5c8679e0e4f75dea63e1", "guid": "bfdfe7dc352907fc980b868725387e98fbeb579020713fde39fd6b0770093afb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987a9b8625638f2b08fa3ddbe77cfed2c4", "guid": "bfdfe7dc352907fc980b868725387e988f132825c125eea5a7728780309460d6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98012a5c371f7e0de63249f8c6db45cd1a", "guid": "bfdfe7dc352907fc980b868725387e985fe351966cb652d689fe3c1256dde0d8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9823944b8d7e69e3a6f836cf3beb0c3ed4", "guid": "bfdfe7dc352907fc980b868725387e983b5407f7e17a386bb30de219c3138743", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988b2d8b3ea27b7394175becec30a6e7be", "guid": "bfdfe7dc352907fc980b868725387e9807301004233e69083b341447c002edb6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ff52b0864dcc53eca16b4da4744d03ac", "guid": "bfdfe7dc352907fc980b868725387e98f25e67e761811041e11c737ef8f5991f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989cbf3742313eb26a7992d4ef632379d0", "guid": "bfdfe7dc352907fc980b868725387e9816c0265f340f2e9d4dc3f1e0cc4a360d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985da6f4d30fffd2f8d1c3086a131d54ab", "guid": "bfdfe7dc352907fc980b868725387e9820b3fcb490891e8c606b333c4d4492ec", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b7fceba655485c4fb34b66d4176dc2ab", "guid": "bfdfe7dc352907fc980b868725387e98b3bf24bb918346e92bab51a3d4f7d17a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989f615fbd42ba2ec1fc9848fc1127dd34", "guid": "bfdfe7dc352907fc980b868725387e98da2cc1b302e3ea3d4b842cad098d08d5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988791f9c5c09c3c1809468be73fedc949", "guid": "bfdfe7dc352907fc980b868725387e98551b8ed4fd6ad3c12490c79c19d9c6c8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983c2ac6024993df43f983c875d59d5208", "guid": "bfdfe7dc352907fc980b868725387e98e7be16bfa6d356830a2800addab5ab45", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b0988e3c09659605b5fb285297b97ef", "guid": "bfdfe7dc352907fc980b868725387e98ad04ede7e4e22d1b497523283d862079"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98019863fada5d106ba10c018ac7a99f2f", "guid": "bfdfe7dc352907fc980b868725387e98593f85f9af66ad9f1c3b37c32ce55ab3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f5122446b2e86142d42e63d24e0757de", "guid": "bfdfe7dc352907fc980b868725387e98217fce318043b1d7fd2d003491f46de5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bd96f5f9717c2901c5e03f50c6dd6618", "guid": "bfdfe7dc352907fc980b868725387e98fe7631d49abd0c9f015b1a076c9fef5d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980c5c10d0cf9c7dff7edb0d80f285978b", "guid": "bfdfe7dc352907fc980b868725387e984870f632ac5785dcdb7d99f89752fe0b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98982d0835627385449147d85ab3c54f36", "guid": "bfdfe7dc352907fc980b868725387e9800ec22310d0d1f8ea9314ce1990dd51a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821f15c39e6dc182f32a938aa9af3fae3", "guid": "bfdfe7dc352907fc980b868725387e9808979a5a7c2626091900c3c75147abf8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9829d2255c5dc92c73a238e17804dbf623", "guid": "bfdfe7dc352907fc980b868725387e986a639385d99840a60d5597e4c67e4e4f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98937014aabfc488848aef931da2ae2a3d", "guid": "bfdfe7dc352907fc980b868725387e9821361998f3e18c393c2996c522712332", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98de18ec670de9f055069175171b1b8091", "guid": "bfdfe7dc352907fc980b868725387e98a82c00632d9b067ee7714c090196fc2e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98911cc19942ecb0d185a7484f5fb7681b", "guid": "bfdfe7dc352907fc980b868725387e98d3a1f25890678afa78ae84cc9da805f1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986fd34d4ae419af365711e996069383ec", "guid": "bfdfe7dc352907fc980b868725387e983c060016f6618ce2e4879a8a4de592ff"}], "guid": "bfdfe7dc352907fc980b868725387e9880d10b038f96d61edc158333b56b47e0", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f5ac87662a84ce4c3d970c3f57c9b327", "guid": "bfdfe7dc352907fc980b868725387e98356b32bd5e3bc6cf2fd638622c97aace"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec1971d19721dc3e02f5be00e2b6d09e", "guid": "bfdfe7dc352907fc980b868725387e9804c8096c9f546dc2d6dd0750df3925bc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ebb9da1423dd7ce62cf93527b4774bf1", "guid": "bfdfe7dc352907fc980b868725387e980a2d299229cbaa4cda8a3456b0825901"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985052305f17d5de0e00a1e0bac8c047fd", "guid": "bfdfe7dc352907fc980b868725387e98614036019a119e2c496a1780c1831ba6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989da0f5cac1d52aa674aa1a9b657168da", "guid": "bfdfe7dc352907fc980b868725387e981092c4a471eabdba3bfc34bb6170670a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d8aabf4724bd0ec0b99bda8dd9c1b1a", "guid": "bfdfe7dc352907fc980b868725387e9837e073ea8d2c1653e43e3ec412ee3dfd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ed4b2add8b16c4849f3636ffcb632d9", "guid": "bfdfe7dc352907fc980b868725387e9839842381553c1f64e103d359c4ff65e4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981e6f71e254c084987e4049ecb7452d07", "guid": "bfdfe7dc352907fc980b868725387e980828c2fc4603ec085fbbfcbbc1e70de5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98549625c901dbabed683a68511a7de358", "guid": "bfdfe7dc352907fc980b868725387e98df306d8040b4e94a9956669d302f1313"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bade4c1b31902a9be7200ce3459bd8d1", "guid": "bfdfe7dc352907fc980b868725387e982f5e3f93daa49bd83d65d2b327e1181d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984025c65319dc53442dbd138d065d09ad", "guid": "bfdfe7dc352907fc980b868725387e982d6ead541eb2736515ad9c89a0153346"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9856adc82db4b31a370d4e27a6389abed1", "guid": "bfdfe7dc352907fc980b868725387e9839374b0f7c9044eb4cfe1d13f8003a82"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985b6718cc948839696885f1a4c11682d5", "guid": "bfdfe7dc352907fc980b868725387e98148806f809a355cff04d1246658e9b25"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d17a11aba796d9c79ef6fc854a81918b", "guid": "bfdfe7dc352907fc980b868725387e986439d20e69053ad6910c52fbc976f4f8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989ffb047a45a571096680f1dd3861f21c", "guid": "bfdfe7dc352907fc980b868725387e98aaccb4331fe7fbc4283098d653b4ae92"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983cf667d3e9d5f1a1cec2b7ed335e1fc6", "guid": "bfdfe7dc352907fc980b868725387e98738ef01efe7818081e266bc89f346ad6"}], "guid": "bfdfe7dc352907fc980b868725387e98c99eeea129bf861060b5550c3bb1c716", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9824f5d6ec5088eecbb83955c855580732", "guid": "bfdfe7dc352907fc980b868725387e9850450d5f98fe57430c6d67839e7b9e2d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ee33ff2b9f8b1d223313a7a24cd5501", "guid": "bfdfe7dc352907fc980b868725387e9804ef75d54372c7e1db40cf45b64b0000"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987cbcfbbfed448f33604b899a4f76dac9", "guid": "bfdfe7dc352907fc980b868725387e9864280b41a76e5d0726bc40a5024b91f6"}], "guid": "bfdfe7dc352907fc980b868725387e985ebc0c6ae06d6c78325484bf0ae19b3e", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98fead6764a645823e70408bd9f12ec3eb", "targetReference": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4"}], "guid": "bfdfe7dc352907fc980b868725387e9813d589e768da9a3c37309218e9429509", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4", "name": "GoogleUtilities-GoogleUtilities_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ca49ca851f2777b997a3e74ccb860358", "name": "GoogleUtilities.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}