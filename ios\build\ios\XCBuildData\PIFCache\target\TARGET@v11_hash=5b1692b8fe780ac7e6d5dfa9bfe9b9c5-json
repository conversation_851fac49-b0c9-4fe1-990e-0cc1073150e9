{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981587e1f75d3bd5b2ac803182fd3a4da1", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/video_player_avfoundation/video_player_avfoundation-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "video_player_avfoundation", "PRODUCT_NAME": "video_player_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98398bac938fb47b84c16f19633c15c92f", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98862fdd069cb245182a4577a512605515", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/video_player_avfoundation/video_player_avfoundation-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "video_player_avfoundation", "PRODUCT_NAME": "video_player_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9839d79a52f24f54321de12379511903ab", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98862fdd069cb245182a4577a512605515", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/video_player_avfoundation/video_player_avfoundation-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "video_player_avfoundation", "PRODUCT_NAME": "video_player_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9894fef8acb7de742f96aaab49fd64da62", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98dff7fd70423d1beecade34b599857589", "guid": "bfdfe7dc352907fc980b868725387e982ef6da7a3a60632d8f8022694bd66dd7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ca95f3dae0eec98e0c8848e3d3206680", "guid": "bfdfe7dc352907fc980b868725387e98d5b97975f2350a08cf53ffeffcac67ae", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c447af29c65ab1983c63804799d600ad", "guid": "bfdfe7dc352907fc980b868725387e982a831a1daaca5b26d69021efadf726c9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b2a392787df06157a971ba55276354fc", "guid": "bfdfe7dc352907fc980b868725387e9893021a663ec0153aef3852a58d009c9a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e5dd105228cfe6417f10fc541324b063", "guid": "bfdfe7dc352907fc980b868725387e986796d36a2bdd3edb6b59e7129fd5a2bd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f3872f657ac3e7a36a66d916a7534ec", "guid": "bfdfe7dc352907fc980b868725387e982e3cc8a1179bcd8b86b68167afee1e90", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9857cdcd73aa71e8bde2ede81c5ae1eca6", "guid": "bfdfe7dc352907fc980b868725387e98c0be046fcf8cb428c9d30ae21d942726", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9817f355dab1136066a86611ba84f210b8", "guid": "bfdfe7dc352907fc980b868725387e9823853b970b232b2ee98e8e4fc95cac83", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981cfb3c6be4f854876e74f484a771df4c", "guid": "bfdfe7dc352907fc980b868725387e98f79b7fe35423f642e91efea7931e4532", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983f02b7e46e950b02c84c1fc28da18116", "guid": "bfdfe7dc352907fc980b868725387e984f6b3b7d4f466465da9e74bca70c619a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985937bb923e16d6830004942fd93f7801", "guid": "bfdfe7dc352907fc980b868725387e98a21439c050c6574e9eda2a7c51d789c8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9869abb30adedbcf8fc4ae0cf4c41007fc", "guid": "bfdfe7dc352907fc980b868725387e987ef5d73bb1bcb9d251b3abc88056c8c2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98573c6dcd4d1fed7a17b3f5c022baacb9", "guid": "bfdfe7dc352907fc980b868725387e9834bceb04a1a4262410f8691a86e50ac0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980975e1ab92bb1ffdc56c9ec7c031646d", "guid": "bfdfe7dc352907fc980b868725387e9819a72032caea2c8a2a1d4d8a68690ede", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e2d5ba8715cf93d882ff2443131fa6f4", "guid": "bfdfe7dc352907fc980b868725387e989314e081c3f5bfab1279e3c2c1139a24", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e983145ad27ab9a5d703e29269b9b2b583a", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e988e768d6f946c9fec1b3d638e4265524c", "guid": "bfdfe7dc352907fc980b868725387e98446126075935aea39a9e1063fe80734a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a2ffa3fa15d2c70c527c03ab8a35065", "guid": "bfdfe7dc352907fc980b868725387e9852b28a641c87eaad7787397417bf81c7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d336aefa0f4c1c10dda985768ee28490", "guid": "bfdfe7dc352907fc980b868725387e9850004e812d7475a48e09f9d39d1fcd0d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d079ef88c1f410d0f52545c1cd8cb460", "guid": "bfdfe7dc352907fc980b868725387e9849968048093435d2a565774782e9f29d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98871d3ff9ccad37343a46457cbc165f93", "guid": "bfdfe7dc352907fc980b868725387e981550500afbebd6a16545417108df5269"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9857df0ea6dc99e3a5769ea1eed5c18aee", "guid": "bfdfe7dc352907fc980b868725387e989a62207c5988db84ddcac0078af53d6b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9806df1b035e249aff9ba621406b977556", "guid": "bfdfe7dc352907fc980b868725387e98d0e36eae7c250ccd059d9dcc1ce416eb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e924fc7f0d0faa8432169bf8265fa372", "guid": "bfdfe7dc352907fc980b868725387e98aaf08e1539082f8b542f01b83c7e8d18"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9824035bb4b5b35e9445583d72dedc9737", "guid": "bfdfe7dc352907fc980b868725387e98c04b1033e4e3e2b45fd298d396b5d875"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e7ad7216eeed73e5c09f6d659bc917b0", "guid": "bfdfe7dc352907fc980b868725387e98b2e8dea6cd344be14bd1567b9270e89b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980eed7d1854ad8c0d2cf83ba67ac10e55", "guid": "bfdfe7dc352907fc980b868725387e98a89106363ecf1c82c77a5e97d941ae89"}], "guid": "bfdfe7dc352907fc980b868725387e9884539da3bdf2ed4ad426ca202aaf6bc7", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9824f5d6ec5088eecbb83955c855580732", "guid": "bfdfe7dc352907fc980b868725387e98c1dae7e29df157eddda815ae5462108c"}], "guid": "bfdfe7dc352907fc980b868725387e98119e723bd2257d68f10fcea70feb1342", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e981c10b7a3ff26395a834064a0172d6124", "targetReference": "bfdfe7dc352907fc980b868725387e98e4af9c7b061f7f7eb180b825e303ea04"}], "guid": "bfdfe7dc352907fc980b868725387e98a3435bfc3b1be550d78f0653eca9c848", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98e4af9c7b061f7f7eb180b825e303ea04", "name": "video_player_avfoundation-video_player_avfoundation_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e988a0a5b40b007f81bee1472e4d0fb23da", "name": "video_player_avfoundation", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98b5f237537920ce49888f6f7f73c80a6c", "name": "video_player_avfoundation.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}