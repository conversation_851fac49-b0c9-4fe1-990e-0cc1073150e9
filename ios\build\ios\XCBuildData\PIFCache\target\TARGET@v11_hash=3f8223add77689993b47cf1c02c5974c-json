{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985a19014320a6bfe4aa96591527b5c5f7", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_inappwebview_ios/flutter_inappwebview_ios-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_inappwebview_ios/flutter_inappwebview_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_inappwebview_ios/flutter_inappwebview_ios.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_inappwebview_ios", "PRODUCT_NAME": "flutter_inappwebview_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b89882dc28a6d5c860523e82f554cc84", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982bb0fdc3ca6c72446d8e10f638d87585", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_inappwebview_ios/flutter_inappwebview_ios-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_inappwebview_ios/flutter_inappwebview_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_inappwebview_ios/flutter_inappwebview_ios.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_inappwebview_ios", "PRODUCT_NAME": "flutter_inappwebview_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e29cd749ce03643e9abbafc9d7ee4c45", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982bb0fdc3ca6c72446d8e10f638d87585", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_inappwebview_ios/flutter_inappwebview_ios-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_inappwebview_ios/flutter_inappwebview_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_inappwebview_ios/flutter_inappwebview_ios.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_inappwebview_ios", "PRODUCT_NAME": "flutter_inappwebview_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e982fd3c6a7f4d84d77d3348746052d187a", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9822b58e2d405b4c7cd9b6414aede49e32", "guid": "bfdfe7dc352907fc980b868725387e98807bebc10b9b2347b3c38ab76dcd39c8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa172541e0c2b525123a3a1ef3b8fd0f", "guid": "bfdfe7dc352907fc980b868725387e98830df83e0ec997753ec519538ef6f60c", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98e2f978f0bcd85da9c3f3f0ea66389fce", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e987ac6c6250b81f03843feadcbf5b42757", "guid": "bfdfe7dc352907fc980b868725387e98ccdfea99e0cc660b9001c8a5d5adb33a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eb62503b6a1c5c71d83af8110c8ce266", "guid": "bfdfe7dc352907fc980b868725387e98dc5de76e264055f43c861d8493e01121"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb9894b737c9c690c35a421d1722104b", "guid": "bfdfe7dc352907fc980b868725387e9871791bf8d2f68b9f58e2fe1d470f4886"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bea8c6c0ed7f6fce46dab8c79eb1d8ed", "guid": "bfdfe7dc352907fc980b868725387e98250472815ebabfbe2170b8bbf902db8f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9822e4e39472717bae8dc479f535c390c3", "guid": "bfdfe7dc352907fc980b868725387e9875bed1f957181399dbba774b75f2fadd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9875ab10ed09be541abc435b25cce51c2d", "guid": "bfdfe7dc352907fc980b868725387e9883888c5b2799e1a0437df6bc3e5fbba7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9822081394adefc99f83b94b7cdec35e5e", "guid": "bfdfe7dc352907fc980b868725387e98e6c0000aa9967ac168d5c88601b76b16"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988b9fd9e07d2a4edda22c8299dcb82b5e", "guid": "bfdfe7dc352907fc980b868725387e9808eca2f1587502e4fb0253dd70590083"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f06b6d006426b8feb731eb0867857e87", "guid": "bfdfe7dc352907fc980b868725387e98a72d821a485fdcedd26adedfb0a2b23d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a478d475b77be4ac5829d895e0d031f8", "guid": "bfdfe7dc352907fc980b868725387e98ca4096acac43a317bda5ba0c47bb8bda"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b62ed72e26ec047393383be1698be60f", "guid": "bfdfe7dc352907fc980b868725387e982ad692615143ac287df3354fda01c162"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984112a276eb04114618d6e53a336d7476", "guid": "bfdfe7dc352907fc980b868725387e9864b3dbf6d6ced697454ea174127f7c6a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988311a7fc9d75ae675212cf048269624e", "guid": "bfdfe7dc352907fc980b868725387e986fa224351bf07d506906474ad5f24fc3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c4127afb2521c77a3156109a601ccfa6", "guid": "bfdfe7dc352907fc980b868725387e980c4b58a8297e745a2a277b5c4d9247fc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989ad156fe72642150dff7ca7350edd363", "guid": "bfdfe7dc352907fc980b868725387e9812491126bbc5062ff02880e7b428066a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a1c4ddb0d13863f84e201ee36ee70f5", "guid": "bfdfe7dc352907fc980b868725387e987c7759cedb074971adacad0051eaa2c5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ce79b1b4510d70ae25d5d67a236345b", "guid": "bfdfe7dc352907fc980b868725387e98ded215c4320567521f37c44a3f5f8d43"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989ad55c3fdf8f0025c1e279eed5d55f09", "guid": "bfdfe7dc352907fc980b868725387e98d1f5a1d290b761132be798590f0e63cb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985779ae827d162b1ceee6e36af16ff1b8", "guid": "bfdfe7dc352907fc980b868725387e982fca89ef90efb3c5d03b926ccf48972b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ac850f547bb52d2823f913c364956829", "guid": "bfdfe7dc352907fc980b868725387e98724a5382285ba30611c6c80c7223a6cf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c29da280ff5dacae019a1a9ca72755e1", "guid": "bfdfe7dc352907fc980b868725387e98314e1d0592601eec0305df273487779b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98105f5783b7d4bdb2a9b39a472a6b05e4", "guid": "bfdfe7dc352907fc980b868725387e982da34c67b6738b27c0b8164aa29b2f6b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c8dfbfe0b6f6d0b3c536ea8777639b3c", "guid": "bfdfe7dc352907fc980b868725387e983f12e6082182025f7e041ef080820aca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9888a5d799f5b8d0326568bab9d2ad286c", "guid": "bfdfe7dc352907fc980b868725387e98cbe2590344c31ed3a21c3773426e3fe4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9891150af9d71dd2bf6aafd62d4fc8c346", "guid": "bfdfe7dc352907fc980b868725387e98b35df1c3f81bed4424b8b446948a1c70"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98984effa68403962e9b423cb1a5de0337", "guid": "bfdfe7dc352907fc980b868725387e989eb0d23d507b408da8575a7e972a2cf2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9832896ad3f4792f5a542387cb1843de77", "guid": "bfdfe7dc352907fc980b868725387e985359bf3f299bad8a26efcc1796d7c1cd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983cd43bbc95cf3f377f28822e0b40262d", "guid": "bfdfe7dc352907fc980b868725387e98462265d7e73c937aba0c1a0c7a05f7e6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e965c05fd936227c1a6791166481ebc4", "guid": "bfdfe7dc352907fc980b868725387e989cc343ddd199dcedcf3100c93f46534f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981cefd1f5664d9cddfe61dceb5a6b24c8", "guid": "bfdfe7dc352907fc980b868725387e98ab5fcee13c8c4444d60bddb6a295be5e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c1a153d0cd308e7f1ad49de788ec0743", "guid": "bfdfe7dc352907fc980b868725387e981f3185aeafddcb4f6ad2369d0f8b8b8a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985971e11b7855a428c2e3d0551c38dd63", "guid": "bfdfe7dc352907fc980b868725387e988129f147513ddb8d2cd36defbe96ac9b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9874736ec49521278c728173d053bc6af1", "guid": "bfdfe7dc352907fc980b868725387e98a160350763ead235ae8e93e475392e1f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9876f8420eb36c307b3836fbd55b084fe3", "guid": "bfdfe7dc352907fc980b868725387e98164e73f85bbfb94e6fd4ff8e92faf005"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9829a772508f36611f8df42f919fcd98a9", "guid": "bfdfe7dc352907fc980b868725387e982ca5c8128c4301080ce2ab76a87de2a5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9885bfb358ee0b5a1738fe0128e0d1c6ff", "guid": "bfdfe7dc352907fc980b868725387e98a3aa5c4326493008dfe5627384906f85"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f0e5e2647c0cec06b327a5e88720bca1", "guid": "bfdfe7dc352907fc980b868725387e983ef3a1fa9beea16913e59206e92a27fa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9857a89cbd66f4f246008c966ea307dc64", "guid": "bfdfe7dc352907fc980b868725387e9894d4fc4cdf98039d9ddf9524d12d795d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981fa9a995dcb4cf5ab5d9a8c462e5f09a", "guid": "bfdfe7dc352907fc980b868725387e98db847b3345cc1944e6e7659ddef1e272"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bc08ef45304bed80ca85a94175344ff7", "guid": "bfdfe7dc352907fc980b868725387e98ce1c4d48c89aab4d580616508036dd9d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9848d1663429239d18480ce5d500a7abc0", "guid": "bfdfe7dc352907fc980b868725387e9819971fed787079059c7146de03897424"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9859c666879905b894ddcb008ba14a1f7f", "guid": "bfdfe7dc352907fc980b868725387e985694432eba2505c3088b40c4577e6121"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d4db0216ec5f00009acd6a2997e1c114", "guid": "bfdfe7dc352907fc980b868725387e98e100eee6ab91604739cd83bc225305cd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9816a1bdf24d6249443864841a53fca201", "guid": "bfdfe7dc352907fc980b868725387e98f05e2e6f5f20f2097468207b2b08f057"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cd030e3ee9a5d2caa9750676b32d7da3", "guid": "bfdfe7dc352907fc980b868725387e9829d94119edcc321aea742c339acb7459"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9898bba90ed834b2acf73ef022bd5760f0", "guid": "bfdfe7dc352907fc980b868725387e980facc7b14738d485db14bdf99debf547"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ad47970ef8c1abb977bbf5a62e983b94", "guid": "bfdfe7dc352907fc980b868725387e9875aab928f91efc50bca0b3a4b53323ed"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9864b5bb087a35979fd1a5790b01bfc947", "guid": "bfdfe7dc352907fc980b868725387e98cdd19bdba7752c5cd9ec9adbc95dfd75"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989caea4355b39b1ce4a3d8a79a9d42cfa", "guid": "bfdfe7dc352907fc980b868725387e98e5de14b5482be462d4bb44c08a0b4e92"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984d13d986a716b53c5ab3cf27a8314ee7", "guid": "bfdfe7dc352907fc980b868725387e983cea68a78c71ced38e5925a70d2e4d0a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9842d6ada46a22c8790f8e1d6a228e74eb", "guid": "bfdfe7dc352907fc980b868725387e98811167c16fb4f59c13d4f45c4b0bf823"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986ed3277ea1e0361efaf3d494c3e74ea7", "guid": "bfdfe7dc352907fc980b868725387e982fae6087faf90913c54ca9ba872a78b1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a44ea31f4aeb721e383f362c1683b389", "guid": "bfdfe7dc352907fc980b868725387e981dda166fea1d00755baa7c2e29f91792"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98089ce18e023f254235190ea568690fe5", "guid": "bfdfe7dc352907fc980b868725387e98c27886c8f0e54a0a2e68beffe517c4c0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ff2f62ae05a419141f3f90a9424bbeda", "guid": "bfdfe7dc352907fc980b868725387e989da49c25cceb0f746dc7b6d52a264248"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e4415e48e9b6a13bc74be2f6d0c25599", "guid": "bfdfe7dc352907fc980b868725387e98c2daa5cb5599c651b6c709acea822f42"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98879cbc8d5dffbd2cf59ad89763137d6d", "guid": "bfdfe7dc352907fc980b868725387e98f995a9f501a217d614b8f4b2cecf3390"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985e9feb4a5f2efeb0bcdbe290b84736c7", "guid": "bfdfe7dc352907fc980b868725387e98d2ff23ba143638be7746ca5b910f7369"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f889312da3878c70d8c59cc6e7deb769", "guid": "bfdfe7dc352907fc980b868725387e98bbffa023bc2ad159c246eb78792c3849"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981255ed1d282badd21b38db495acfbe99", "guid": "bfdfe7dc352907fc980b868725387e98aa8696a741ccd4b6741719a926fea194"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e45ae5afef9aa4c393f851a26ec92c94", "guid": "bfdfe7dc352907fc980b868725387e980b0a11f669d8564f8815d6d3a179b046"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9885675ae373ad7e2bcfc7772af6817362", "guid": "bfdfe7dc352907fc980b868725387e98ce47c641f0458341bf50d7b25f1c6a2f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9852a6c28bd23ecc2e5d685bcfc08ee852", "guid": "bfdfe7dc352907fc980b868725387e98f9e52fc7467b0174f0e055e0eba47775"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98214931898b3790f8462bc2e886174f67", "guid": "bfdfe7dc352907fc980b868725387e98d2b0721788df1c5d87ce43d2ad84dff9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b472d1250b5c90a93aa2c471c25e864e", "guid": "bfdfe7dc352907fc980b868725387e98a22b91bc30195e74d25116f730383a0f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9868a83d9ec488c8426095d63a4dc7279f", "guid": "bfdfe7dc352907fc980b868725387e98cf64948e2e73b70435172d32ba723704"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a1aa2dd7f39b4c84809f515b1e79dbf", "guid": "bfdfe7dc352907fc980b868725387e981ad231c682034a6053a672fe7e0b2c42"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fa577ceae3f475732ad7754646490df2", "guid": "bfdfe7dc352907fc980b868725387e989ccdfd00db0fd04284ddf335fafa86f5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ff2acfc2ad36928432bbaffcc7f42de9", "guid": "bfdfe7dc352907fc980b868725387e986aa0ed2968449bb085643a863f0937ad"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a37a80ef0ffcde69e4e65543a1dd11ee", "guid": "bfdfe7dc352907fc980b868725387e98e588ceaedef830278f4fcdfa03d515c1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984edae2ff251e01d63330eec119b833bf", "guid": "bfdfe7dc352907fc980b868725387e98fc667a1b02350a9be46c6509f23aef81"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982db79023718d9bd9e1e839e1d1252e8f", "guid": "bfdfe7dc352907fc980b868725387e98162e2a5689a129dba61a30062c656c93"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981ec602da108f8154148a295ceb666d55", "guid": "bfdfe7dc352907fc980b868725387e9874c29c73a36beaebd65b0cb0fb9225af"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9839f820a23375712e72e52ccf915dd8f4", "guid": "bfdfe7dc352907fc980b868725387e98d432c3f46b28ab7c360d9d055f1a6ac9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f4138d421de801a1b36190cc06404cf6", "guid": "bfdfe7dc352907fc980b868725387e98a94054347508856afb9f5fd629bd1851"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98306367a6d44c3d910ae986301f8c66db", "guid": "bfdfe7dc352907fc980b868725387e98eeaa4a3d7df0ff0514f95cf919a6efc0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9826ecc0051aa9c0381097f15054e7ead1", "guid": "bfdfe7dc352907fc980b868725387e985b9dbcf06d35ab48c4feae2b62c0a9b5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9852892c49503feda430d68d0b79e412f8", "guid": "bfdfe7dc352907fc980b868725387e98f0cb55ef7a1593e9205541db820b22c1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bc1593d04b7f4da892ca9e816d49435f", "guid": "bfdfe7dc352907fc980b868725387e98193b45a979ab1a1e15db8ad8f20b7024"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da80b59870de884f6c410e605e9688a9", "guid": "bfdfe7dc352907fc980b868725387e98b33dff1b8addf2e809060b9f0431620b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987942808eefa723f44ba8224d50d0e3d7", "guid": "bfdfe7dc352907fc980b868725387e98c37a16228db23d06e86e00dd29019c33"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989673e37e35cc9f7a50871a7db642a0fd", "guid": "bfdfe7dc352907fc980b868725387e981e2e1346a1633ce1ee4eb4cb55b57c7d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986bb506583e3e2639f76a2e60589f9418", "guid": "bfdfe7dc352907fc980b868725387e985d9ebef983dfa659514f8975005289dd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f8e9a0ead2dac043562fd7bb6103a71e", "guid": "bfdfe7dc352907fc980b868725387e984c9519493ccdac0b918d35fb0cf523af"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983fd31266961bf60d8906b1735256cd7d", "guid": "bfdfe7dc352907fc980b868725387e98e80213b22df3304548a02d49a2d8e3f3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ea44cca2da30d2de2de59eb826ce9a5c", "guid": "bfdfe7dc352907fc980b868725387e98f08ee4bfef07c27371e0706f6620feb2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c3ca01e0161ad624079a0d037591eea", "guid": "bfdfe7dc352907fc980b868725387e9875e616075cc95c8cd36fbd21fd6b0440"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986519b36468f77457de2010316d6fa4b4", "guid": "bfdfe7dc352907fc980b868725387e989658d9408f6bba8d3ca6ca5e9dbc8c90"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9888f205412f8da0777146ffe3071b20d0", "guid": "bfdfe7dc352907fc980b868725387e981a727837450fac18a64627732dc82897"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984680320e43cd837ed9f8d02a02dda7f6", "guid": "bfdfe7dc352907fc980b868725387e98be4bfcca04bf0482d361a80c377ffc42"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9864141a6f8f318c47ba44a18c0c7059e0", "guid": "bfdfe7dc352907fc980b868725387e9885397fa3e0a6ac3fe2c9b9c7b909826c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9855fbb5b339dafa2a582c71aadfad86c2", "guid": "bfdfe7dc352907fc980b868725387e98d61c6522170340c11f804d594d516906"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f4f8c87384c3e135d1967f2f6044f8b2", "guid": "bfdfe7dc352907fc980b868725387e984e695630728ab22705d1b42df7f49906"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98005a320325bfe89cbfe4d1d9aefa6ed8", "guid": "bfdfe7dc352907fc980b868725387e9805b0bcf4ef09ecebd888d0e223785cff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9806a65f1368813460cc0b5f744f694f42", "guid": "bfdfe7dc352907fc980b868725387e98a601a1a8dda4277e4adcc83c0e68fe33"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98138845599c01cf97a1b6fe140207ff9a", "guid": "bfdfe7dc352907fc980b868725387e98e207a2aef02fa50a6ec65bb21c712e24"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98afc9165441b3512888bd6524d0ac6f06", "guid": "bfdfe7dc352907fc980b868725387e98472398837d89d36c21b64cc493a94a41"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ab03b9c7e773b0eb939f2023e0c4ba1c", "guid": "bfdfe7dc352907fc980b868725387e98e72d95288ea46299e61700c842e7b4da"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9812634057484ca93805b042a7d2ffe095", "guid": "bfdfe7dc352907fc980b868725387e98d0061c648c5ae49d828560fcae9a0534"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989f4e59d2989660624dca03e6524aac97", "guid": "bfdfe7dc352907fc980b868725387e98225b5fe7185fdcd8126bbe8d691c29ef"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9844c30dafeef79301245a8f1f6ca77fe5", "guid": "bfdfe7dc352907fc980b868725387e98262a5ae95bec7b8f6379fdf1257d87a8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a107138863fa28bd1b4bad2dc06b5aa5", "guid": "bfdfe7dc352907fc980b868725387e98e3e492a5e14907a00207c7702af8d212"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e20897fa030f7562f2a2b0e5fe980ac0", "guid": "bfdfe7dc352907fc980b868725387e98cda12c54392375845b2f8302144b7245"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a8c47691bc2169db4f7ff0d50a54ad3f", "guid": "bfdfe7dc352907fc980b868725387e98af0decb17c191742bee91d1b04138c8e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821ad3ec68b90a8e398c4544f4d02545f", "guid": "bfdfe7dc352907fc980b868725387e98ba174d72bbb3036585382e9428ba9077"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb04aaa45aa4057786e5fcc6ad5b5c26", "guid": "bfdfe7dc352907fc980b868725387e98be57fb2fa056f7111daf382f49270451"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98879d5f0b37560825baf4f3631b8c6884", "guid": "bfdfe7dc352907fc980b868725387e98c14d819d66ed9375efcc6a539b85b7ec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec8d5d8ac2eac7754da2094de37e19de", "guid": "bfdfe7dc352907fc980b868725387e98e308fbcee206f934ffb00475cc523a36"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986842e984b61d033c475101ece4c9fcf5", "guid": "bfdfe7dc352907fc980b868725387e98b3132a0220e94aab07f737db82c3cfe4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ecbbc5a1445d1ccc832c44e420ddd23", "guid": "bfdfe7dc352907fc980b868725387e988893378490c2052a8a464aa57090531f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98342c8c0b06ac88d588618f67196991db", "guid": "bfdfe7dc352907fc980b868725387e98154b1d25eb1a439b8767d19ef39e1ae6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987dd8a3bd15bf10e7d1bd6d21aac6034d", "guid": "bfdfe7dc352907fc980b868725387e98b0674a3442b1a2a48f3f4262ef7237b7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ebdd472d6591fa229b1ed53ea7a963b", "guid": "bfdfe7dc352907fc980b868725387e98ebf7b3e19e4965170d45392782771cb7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9893b65f648b2b6c54973baee8436dcbb3", "guid": "bfdfe7dc352907fc980b868725387e98852d58f5e479ee1b5233ae013ab7002a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9891009dd8bf7aa973fae3353036d825ee", "guid": "bfdfe7dc352907fc980b868725387e981fe38c3f705316063a59f18ad70ac314"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982e2a3ad5543102e6013763fc8f9ecb49", "guid": "bfdfe7dc352907fc980b868725387e983c25e1b3ec34195ddd1a48092e7d5c26"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e10c8a509f937b1f395dbfd0f8284931", "guid": "bfdfe7dc352907fc980b868725387e98a68eaafbd6e7d960af0028a8386b3330"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986304c9597fc7cbac0f52c19a9eecc08e", "guid": "bfdfe7dc352907fc980b868725387e983f3d27c22745d72b49edccf768470f8a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98843e86459481f4b47ffe199193f037f2", "guid": "bfdfe7dc352907fc980b868725387e98e8f28ae119a17ae79c19b0db5ad09cbb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984e8aa972574ce10cc48c3acfab0e7a5d", "guid": "bfdfe7dc352907fc980b868725387e98bc6a9ba2dd8868dbc2fb45beea927d56"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ab7de5b7325f5a12d1482ecbd24cefe0", "guid": "bfdfe7dc352907fc980b868725387e98d7d69d6c5dce8ac5c9d18c643e30de06"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98682c3fa3602fc394abf2f309fb53598f", "guid": "bfdfe7dc352907fc980b868725387e98e3d136553c1a88705b7f7d040ca3a677"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987930706b7273fc95dbdb172a900fa45a", "guid": "bfdfe7dc352907fc980b868725387e98ee9a4010577dcfb314226dce4f361e27"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98662abf1c9b7064339d94f60ce8c509c4", "guid": "bfdfe7dc352907fc980b868725387e98e41af1ee7dc191c44cc159e5c2478f05"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98840c27a55c8974c99c0bf9116d8f4eaf", "guid": "bfdfe7dc352907fc980b868725387e981410763db0b310f0024e0807fdafd422"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98038d0358f30a839c79d4d4caab9f1ba3", "guid": "bfdfe7dc352907fc980b868725387e98b96aa6334ad332103ecc32edb480d0f1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e6fc3b278530c6f7de4c57cbb411f714", "guid": "bfdfe7dc352907fc980b868725387e98693ab963407f58e1d2533433981cc8a9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fdfe96c2adc0c2119cdd312cb771a00d", "guid": "bfdfe7dc352907fc980b868725387e98530872d8b12748c7cc42dfe53eb608f3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae05b02d06db82fcd63e666481ee0461", "guid": "bfdfe7dc352907fc980b868725387e986550f864d69eb5b789ef74c2f45eaf60"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9838730b1fb8a3d5a2618503811bdba46e", "guid": "bfdfe7dc352907fc980b868725387e984ca6c988a2f9d3d9b69702cd0b293872"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e5fec4195df089daf926ed8547874acb", "guid": "bfdfe7dc352907fc980b868725387e98c0aaaa1042ab55ac68bc3d509c3c0620"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987a44c48b9383e038ced9570ce59346ee", "guid": "bfdfe7dc352907fc980b868725387e9887ca8566a609060d63341628c7144b4b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985b6273a776f5050b57d91f064903a215", "guid": "bfdfe7dc352907fc980b868725387e98c83e1006198954f0fa914a878343b98c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d01f56d1e3a366a4ad34d89bcc8b0fbd", "guid": "bfdfe7dc352907fc980b868725387e98315f7552f7ef320c380e76f948f8d0ed"}], "guid": "bfdfe7dc352907fc980b868725387e98cac5758aaab041b306b3055c248885f4", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9824f5d6ec5088eecbb83955c855580732", "guid": "bfdfe7dc352907fc980b868725387e98027d34dd1e1770c79dd54f5404987fd6"}], "guid": "bfdfe7dc352907fc980b868725387e98d8f70b5ff677ed02b77232961e037957", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98b7d49ad1fe66522e6eab9248dd2331d6", "targetReference": "bfdfe7dc352907fc980b868725387e98feffba4bc77d9f3d84a98c192cefdc8b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98143ab0d46847fc3f6ebc06cef261100b", "guid": "bfdfe7dc352907fc980b868725387e98ec5268a1792f6b5cd4753bb67be7b719"}], "guid": "bfdfe7dc352907fc980b868725387e98ba929dd1b11c60f9a4f8fd06e4eaa3e7", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e985f0ec3a68eeed5241cb87afb05bcc380", "name": "OrderedSet"}, {"guid": "bfdfe7dc352907fc980b868725387e98feffba4bc77d9f3d84a98c192cefdc8b", "name": "flutter_inappwebview_ios-flutter_inappwebview_ios_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98a562549a031aeda8bf3440b79b3420bc", "name": "flutter_inappwebview_ios", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9810acd6d3a97e7ef91b90dda5618dc5c0", "name": "flutter_inappwebview_ios.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}