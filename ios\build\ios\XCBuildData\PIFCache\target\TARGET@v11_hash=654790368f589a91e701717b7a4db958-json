{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985a19014320a6bfe4aa96591527b5c5f7", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Daleel/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Daleel/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_inappwebview_ios/flutter_inappwebview_ios-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_inappwebview_ios/flutter_inappwebview_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_inappwebview_ios/flutter_inappwebview_ios.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_inappwebview_ios", "PRODUCT_NAME": "flutter_inappwebview_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b89882dc28a6d5c860523e82f554cc84", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982bb0fdc3ca6c72446d8e10f638d87585", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Daleel/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Daleel/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_inappwebview_ios/flutter_inappwebview_ios-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_inappwebview_ios/flutter_inappwebview_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_inappwebview_ios/flutter_inappwebview_ios.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_inappwebview_ios", "PRODUCT_NAME": "flutter_inappwebview_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e29cd749ce03643e9abbafc9d7ee4c45", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982bb0fdc3ca6c72446d8e10f638d87585", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Daleel/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Daleel/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_inappwebview_ios/flutter_inappwebview_ios-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_inappwebview_ios/flutter_inappwebview_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_inappwebview_ios/flutter_inappwebview_ios.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_inappwebview_ios", "PRODUCT_NAME": "flutter_inappwebview_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e982fd3c6a7f4d84d77d3348746052d187a", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9822b58e2d405b4c7cd9b6414aede49e32", "guid": "bfdfe7dc352907fc980b868725387e98807bebc10b9b2347b3c38ab76dcd39c8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f2e623eba5feca071f3f8d6e79236656", "guid": "bfdfe7dc352907fc980b868725387e98830df83e0ec997753ec519538ef6f60c", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98e2f978f0bcd85da9c3f3f0ea66389fce", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98628587559eba4b3a3aa441b2591418a6", "guid": "bfdfe7dc352907fc980b868725387e98ccdfea99e0cc660b9001c8a5d5adb33a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f987a3f2e77e651887fc7404b1e28195", "guid": "bfdfe7dc352907fc980b868725387e98dc5de76e264055f43c861d8493e01121"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9879c4eb52407f6c1c170d551deb5f0b73", "guid": "bfdfe7dc352907fc980b868725387e9871791bf8d2f68b9f58e2fe1d470f4886"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ab3fb4516bbd508a6001e5b4cf5917d", "guid": "bfdfe7dc352907fc980b868725387e98250472815ebabfbe2170b8bbf902db8f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98158fcc77b67e6f013603b6b5a9c89eac", "guid": "bfdfe7dc352907fc980b868725387e9875bed1f957181399dbba774b75f2fadd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b70ba9526d71c4d071558661b4f7ffba", "guid": "bfdfe7dc352907fc980b868725387e9883888c5b2799e1a0437df6bc3e5fbba7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d612af2305f9e44252d980e4d44ae9c6", "guid": "bfdfe7dc352907fc980b868725387e98e6c0000aa9967ac168d5c88601b76b16"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a796eec34f0912963a7022cc1a2e6f86", "guid": "bfdfe7dc352907fc980b868725387e9808eca2f1587502e4fb0253dd70590083"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec154dc1d15e085c33600bb7e7daa1cf", "guid": "bfdfe7dc352907fc980b868725387e98a72d821a485fdcedd26adedfb0a2b23d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98612ab9ebc0e3626845dc2c4ae3ff020c", "guid": "bfdfe7dc352907fc980b868725387e98ca4096acac43a317bda5ba0c47bb8bda"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f0888944250ed626ceabffa0bee24b6f", "guid": "bfdfe7dc352907fc980b868725387e982ad692615143ac287df3354fda01c162"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9884481b3c26b87bf39b8508a174ca8e07", "guid": "bfdfe7dc352907fc980b868725387e9864b3dbf6d6ced697454ea174127f7c6a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bccc255952bab63b1c8f333f35425b02", "guid": "bfdfe7dc352907fc980b868725387e986fa224351bf07d506906474ad5f24fc3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9800b40ffebabc68aa3a2a1146f419920f", "guid": "bfdfe7dc352907fc980b868725387e980c4b58a8297e745a2a277b5c4d9247fc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982ca9fd45d63797298ce0b49e22ccc287", "guid": "bfdfe7dc352907fc980b868725387e9812491126bbc5062ff02880e7b428066a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c6aff7053d9246f2a8ff181f3cbb29db", "guid": "bfdfe7dc352907fc980b868725387e987c7759cedb074971adacad0051eaa2c5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98872c5a6f47405b52963268cabc39cff4", "guid": "bfdfe7dc352907fc980b868725387e98ded215c4320567521f37c44a3f5f8d43"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980c5e5fea8ff80306af0acf08b35880b8", "guid": "bfdfe7dc352907fc980b868725387e98d1f5a1d290b761132be798590f0e63cb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98079bdc13bd618cd20a8ac6a4a0e33fc4", "guid": "bfdfe7dc352907fc980b868725387e982fca89ef90efb3c5d03b926ccf48972b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9882d00ac39880b91a44bf898135db1438", "guid": "bfdfe7dc352907fc980b868725387e98724a5382285ba30611c6c80c7223a6cf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98734a20ffdd53ec93ba871c76e6a0ce24", "guid": "bfdfe7dc352907fc980b868725387e98314e1d0592601eec0305df273487779b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9895f9351ad9de85390a67e0985866f204", "guid": "bfdfe7dc352907fc980b868725387e982da34c67b6738b27c0b8164aa29b2f6b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ad4d090397afe60c05bebc60a2169cb6", "guid": "bfdfe7dc352907fc980b868725387e983f12e6082182025f7e041ef080820aca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98015bcb02cc38029ff425bf56c77fe532", "guid": "bfdfe7dc352907fc980b868725387e98cbe2590344c31ed3a21c3773426e3fe4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984f78f10ee23d9722226b6e796baee0bb", "guid": "bfdfe7dc352907fc980b868725387e98b35df1c3f81bed4424b8b446948a1c70"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ce77525e2e54d0b221e261b85c8a6a91", "guid": "bfdfe7dc352907fc980b868725387e989eb0d23d507b408da8575a7e972a2cf2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9832896ad3f4792f5a542387cb1843de77", "guid": "bfdfe7dc352907fc980b868725387e985359bf3f299bad8a26efcc1796d7c1cd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9822d77bd0916ef9a931c5818513863a30", "guid": "bfdfe7dc352907fc980b868725387e98462265d7e73c937aba0c1a0c7a05f7e6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983fcb7c9f53bc5017813f47fbe2a18883", "guid": "bfdfe7dc352907fc980b868725387e989cc343ddd199dcedcf3100c93f46534f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a45eaa3fa10b247900e47f21fd20f48c", "guid": "bfdfe7dc352907fc980b868725387e98ab5fcee13c8c4444d60bddb6a295be5e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983d1d0f0a9ca00e070148c6faa0b29634", "guid": "bfdfe7dc352907fc980b868725387e981f3185aeafddcb4f6ad2369d0f8b8b8a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982191d4000e5bbfee6225e0099696f5aa", "guid": "bfdfe7dc352907fc980b868725387e988129f147513ddb8d2cd36defbe96ac9b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf2e063a83d4af6ff3b1bf8b5ba5ae74", "guid": "bfdfe7dc352907fc980b868725387e98a160350763ead235ae8e93e475392e1f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98175fecc204a4d22aa4622bf908e06766", "guid": "bfdfe7dc352907fc980b868725387e98164e73f85bbfb94e6fd4ff8e92faf005"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9893a582f39842863a3345cacc073ebe58", "guid": "bfdfe7dc352907fc980b868725387e982ca5c8128c4301080ce2ab76a87de2a5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982bd077dea0c400fd0326809ce0de6713", "guid": "bfdfe7dc352907fc980b868725387e98a3aa5c4326493008dfe5627384906f85"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982724da1645a3accefa25ab9b6b18b9c5", "guid": "bfdfe7dc352907fc980b868725387e983ef3a1fa9beea16913e59206e92a27fa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98627e41895c7c4e2e4378465e826aa0c8", "guid": "bfdfe7dc352907fc980b868725387e9894d4fc4cdf98039d9ddf9524d12d795d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983a2f63dc08075aeba9ba31e881d98a49", "guid": "bfdfe7dc352907fc980b868725387e98db847b3345cc1944e6e7659ddef1e272"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d63348cb34a529a9474a53a1f5ff2e6d", "guid": "bfdfe7dc352907fc980b868725387e98ce1c4d48c89aab4d580616508036dd9d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98663bbed0de65a7bc1e18efe475d2f8a0", "guid": "bfdfe7dc352907fc980b868725387e9819971fed787079059c7146de03897424"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe61851014b535cee8e69cd35ecd83e6", "guid": "bfdfe7dc352907fc980b868725387e985694432eba2505c3088b40c4577e6121"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f4efc45def4195eec06b04b4e3a1ab9", "guid": "bfdfe7dc352907fc980b868725387e98e100eee6ab91604739cd83bc225305cd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98288216101e2ab59878b5b685677778e8", "guid": "bfdfe7dc352907fc980b868725387e98f05e2e6f5f20f2097468207b2b08f057"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986b8d642bbe0ed2ac7b785d3454af12dd", "guid": "bfdfe7dc352907fc980b868725387e9829d94119edcc321aea742c339acb7459"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9861249a589ac87f1be5569f892632bae8", "guid": "bfdfe7dc352907fc980b868725387e980facc7b14738d485db14bdf99debf547"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983f3f46b741c72800e419679d95c82fe8", "guid": "bfdfe7dc352907fc980b868725387e9875aab928f91efc50bca0b3a4b53323ed"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f10846163c85f1d877b5d2149f49992", "guid": "bfdfe7dc352907fc980b868725387e98cdd19bdba7752c5cd9ec9adbc95dfd75"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d42db521fb997b666739ef83eed775aa", "guid": "bfdfe7dc352907fc980b868725387e98e5de14b5482be462d4bb44c08a0b4e92"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9804b455f882bcc352ced810484c7bb2c5", "guid": "bfdfe7dc352907fc980b868725387e983cea68a78c71ced38e5925a70d2e4d0a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e99c384ce8211f84f8d8cd2b619692bb", "guid": "bfdfe7dc352907fc980b868725387e98811167c16fb4f59c13d4f45c4b0bf823"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f5eff2f503acf8c7524fc60dff32912", "guid": "bfdfe7dc352907fc980b868725387e982fae6087faf90913c54ca9ba872a78b1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98198f4eb26c324238f4a417200cd75f7c", "guid": "bfdfe7dc352907fc980b868725387e981dda166fea1d00755baa7c2e29f91792"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98042315f03848b78840723d3e847e1025", "guid": "bfdfe7dc352907fc980b868725387e98c27886c8f0e54a0a2e68beffe517c4c0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98727dd940cd1c86750a9e77a24e066f06", "guid": "bfdfe7dc352907fc980b868725387e989da49c25cceb0f746dc7b6d52a264248"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9888b0bd32c4dceb59c039b30b8a6a69d6", "guid": "bfdfe7dc352907fc980b868725387e98c2daa5cb5599c651b6c709acea822f42"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9834cb6da4d4a68f5823879a7457475265", "guid": "bfdfe7dc352907fc980b868725387e98f995a9f501a217d614b8f4b2cecf3390"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982be97741c403e45d3b28bcaa5e7d1348", "guid": "bfdfe7dc352907fc980b868725387e98d2ff23ba143638be7746ca5b910f7369"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ffbd57de540efcff91a62d2a666349d8", "guid": "bfdfe7dc352907fc980b868725387e98bbffa023bc2ad159c246eb78792c3849"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b6e8bdd4da690cfb24c09f968c48ee94", "guid": "bfdfe7dc352907fc980b868725387e98aa8696a741ccd4b6741719a926fea194"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98744b216198626f14dac22d8ff631b529", "guid": "bfdfe7dc352907fc980b868725387e980b0a11f669d8564f8815d6d3a179b046"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb607bd2553fa74972fc86cf7c8ccc42", "guid": "bfdfe7dc352907fc980b868725387e98ce47c641f0458341bf50d7b25f1c6a2f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fa28775a9864ffebf3905972fc340991", "guid": "bfdfe7dc352907fc980b868725387e98f9e52fc7467b0174f0e055e0eba47775"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e3ac4230ffaaaa70f28a15b717e2a905", "guid": "bfdfe7dc352907fc980b868725387e98d2b0721788df1c5d87ce43d2ad84dff9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981f58b37f660d53c15913f31c6b138577", "guid": "bfdfe7dc352907fc980b868725387e98a22b91bc30195e74d25116f730383a0f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98453690c612f38bd881ea0d86a4c3d9fe", "guid": "bfdfe7dc352907fc980b868725387e98cf64948e2e73b70435172d32ba723704"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b62b9a1c50bb80331073b8175f82629", "guid": "bfdfe7dc352907fc980b868725387e981ad231c682034a6053a672fe7e0b2c42"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98311404e5e5c1cdb633b7a41cd4e218d3", "guid": "bfdfe7dc352907fc980b868725387e989ccdfd00db0fd04284ddf335fafa86f5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f5d843d4a1dd9ac6483429aa80a61b1f", "guid": "bfdfe7dc352907fc980b868725387e986aa0ed2968449bb085643a863f0937ad"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ff78acb5d2657c1ccd8db8d4e590cf6", "guid": "bfdfe7dc352907fc980b868725387e98e588ceaedef830278f4fcdfa03d515c1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984a1972ceafdce8c87a839530fe3a9e52", "guid": "bfdfe7dc352907fc980b868725387e98fc667a1b02350a9be46c6509f23aef81"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985d872973537ed5de4d5cb1b02ff575b2", "guid": "bfdfe7dc352907fc980b868725387e98162e2a5689a129dba61a30062c656c93"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986ff6f8a167f47d49923b80ac6b21f7d2", "guid": "bfdfe7dc352907fc980b868725387e9874c29c73a36beaebd65b0cb0fb9225af"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aba91c1238b4ba5924ca62d426a90f42", "guid": "bfdfe7dc352907fc980b868725387e98d432c3f46b28ab7c360d9d055f1a6ac9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9835558f4f538507e443f746a5cce99ccb", "guid": "bfdfe7dc352907fc980b868725387e98a94054347508856afb9f5fd629bd1851"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98772ca64068beff5da5e40547cadd8f1d", "guid": "bfdfe7dc352907fc980b868725387e98eeaa4a3d7df0ff0514f95cf919a6efc0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ea17d4999379ed6488fd0b795a735cd8", "guid": "bfdfe7dc352907fc980b868725387e985b9dbcf06d35ab48c4feae2b62c0a9b5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b9003a93cc9c2c8022f166f1efd839d1", "guid": "bfdfe7dc352907fc980b868725387e98f0cb55ef7a1593e9205541db820b22c1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e027036edf37c877baec46235ef1f5ae", "guid": "bfdfe7dc352907fc980b868725387e98193b45a979ab1a1e15db8ad8f20b7024"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9877388956a25e1c1d58355806fcb55369", "guid": "bfdfe7dc352907fc980b868725387e98b33dff1b8addf2e809060b9f0431620b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981bbd9f1e6075a685cc1c92e54fa3e2f9", "guid": "bfdfe7dc352907fc980b868725387e98c37a16228db23d06e86e00dd29019c33"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98964c01f733b2fe8926b113f8940104d8", "guid": "bfdfe7dc352907fc980b868725387e981e2e1346a1633ce1ee4eb4cb55b57c7d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9836e020ca85454aea386c55f2b2760bb4", "guid": "bfdfe7dc352907fc980b868725387e985d9ebef983dfa659514f8975005289dd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aced4d956e070fb47d813bbe0b6e59fd", "guid": "bfdfe7dc352907fc980b868725387e984c9519493ccdac0b918d35fb0cf523af"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d0571664bd62770b75e1bf4d5c2a2525", "guid": "bfdfe7dc352907fc980b868725387e98e80213b22df3304548a02d49a2d8e3f3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe230e42bf02bba53ca52374543f8bc6", "guid": "bfdfe7dc352907fc980b868725387e98f08ee4bfef07c27371e0706f6620feb2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98790511db18df09c8f4d8496a14fd6748", "guid": "bfdfe7dc352907fc980b868725387e9875e616075cc95c8cd36fbd21fd6b0440"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982b1b97f5474f562bea888577eaeaa11d", "guid": "bfdfe7dc352907fc980b868725387e989658d9408f6bba8d3ca6ca5e9dbc8c90"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986f5ac868656671aac3733d1ae4ff791c", "guid": "bfdfe7dc352907fc980b868725387e981a727837450fac18a64627732dc82897"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9869d8089c49c2a138475bf16141fafeb2", "guid": "bfdfe7dc352907fc980b868725387e98be4bfcca04bf0482d361a80c377ffc42"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b6d8d93da733a3d312172cb67e31c861", "guid": "bfdfe7dc352907fc980b868725387e9885397fa3e0a6ac3fe2c9b9c7b909826c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f394b0c1007423320db541719fd1a562", "guid": "bfdfe7dc352907fc980b868725387e98d61c6522170340c11f804d594d516906"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985dee945be3995319b83d8aac3a8c2816", "guid": "bfdfe7dc352907fc980b868725387e984e695630728ab22705d1b42df7f49906"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985b9304782859b9b3b6d5d56e4a8148ef", "guid": "bfdfe7dc352907fc980b868725387e9805b0bcf4ef09ecebd888d0e223785cff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9862b3b954d42d29798a9d342287fdc17c", "guid": "bfdfe7dc352907fc980b868725387e98a601a1a8dda4277e4adcc83c0e68fe33"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a9df3dafdfc27b98e7cba0f486ae798e", "guid": "bfdfe7dc352907fc980b868725387e98e207a2aef02fa50a6ec65bb21c712e24"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f61328dd627a2316368752550d9ad5b", "guid": "bfdfe7dc352907fc980b868725387e98472398837d89d36c21b64cc493a94a41"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985b69f2edc1441f399498cc9d30e50d5b", "guid": "bfdfe7dc352907fc980b868725387e98e72d95288ea46299e61700c842e7b4da"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e90d91750a9c3469072950cfcf9d871c", "guid": "bfdfe7dc352907fc980b868725387e98d0061c648c5ae49d828560fcae9a0534"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c46b9e602ebb198d2722d55195f78faa", "guid": "bfdfe7dc352907fc980b868725387e98225b5fe7185fdcd8126bbe8d691c29ef"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987900a8038792a7a6f35621b6422109a1", "guid": "bfdfe7dc352907fc980b868725387e98262a5ae95bec7b8f6379fdf1257d87a8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98277592e29dcffccd4d527acc1b73c59e", "guid": "bfdfe7dc352907fc980b868725387e98e3e492a5e14907a00207c7702af8d212"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981917ec3d5097fbb62b610f6d4a287835", "guid": "bfdfe7dc352907fc980b868725387e98cda12c54392375845b2f8302144b7245"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eddb64d401bd93ef7e1c2a0c0ece7a63", "guid": "bfdfe7dc352907fc980b868725387e98af0decb17c191742bee91d1b04138c8e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98db1de9115d9fc4fced8e586dfecd0ddd", "guid": "bfdfe7dc352907fc980b868725387e98ba174d72bbb3036585382e9428ba9077"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984f87e0166677379b8835446b72ee2a52", "guid": "bfdfe7dc352907fc980b868725387e98be57fb2fa056f7111daf382f49270451"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985a364f6cc6bffc94fbe63b9bae7fb188", "guid": "bfdfe7dc352907fc980b868725387e98c14d819d66ed9375efcc6a539b85b7ec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da81686b4a6503dfaed4c473225c60e8", "guid": "bfdfe7dc352907fc980b868725387e98e308fbcee206f934ffb00475cc523a36"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983061b9ca811598e3d74f6e30a9fd4667", "guid": "bfdfe7dc352907fc980b868725387e98b3132a0220e94aab07f737db82c3cfe4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ed5efc5275d8f93f0e7e13c74713d2b9", "guid": "bfdfe7dc352907fc980b868725387e988893378490c2052a8a464aa57090531f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c53579ef7189b94992041e4a6f3aa5ce", "guid": "bfdfe7dc352907fc980b868725387e98154b1d25eb1a439b8767d19ef39e1ae6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98520d6eac35e62d75686d49bce0578462", "guid": "bfdfe7dc352907fc980b868725387e98b0674a3442b1a2a48f3f4262ef7237b7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9881d9fe2851a0106f86dc8a16e219de44", "guid": "bfdfe7dc352907fc980b868725387e98ebf7b3e19e4965170d45392782771cb7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98438d057ee623b9cbfaeb17b3382063f1", "guid": "bfdfe7dc352907fc980b868725387e98852d58f5e479ee1b5233ae013ab7002a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982d01f9a896426b3b320759f5635b6966", "guid": "bfdfe7dc352907fc980b868725387e981fe38c3f705316063a59f18ad70ac314"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a0a9eb8ab01b9c7c21ab18996c85639b", "guid": "bfdfe7dc352907fc980b868725387e983c25e1b3ec34195ddd1a48092e7d5c26"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988a3052f28b29521a1eaa2da848141aa2", "guid": "bfdfe7dc352907fc980b868725387e98a68eaafbd6e7d960af0028a8386b3330"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c40985c0776217629b0ea15d299bf415", "guid": "bfdfe7dc352907fc980b868725387e983f3d27c22745d72b49edccf768470f8a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b314f3f01ff1c37c441e40cb58efb5b4", "guid": "bfdfe7dc352907fc980b868725387e98e8f28ae119a17ae79c19b0db5ad09cbb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987242fd26b24bf0f2a450cc124b5668cb", "guid": "bfdfe7dc352907fc980b868725387e98bc6a9ba2dd8868dbc2fb45beea927d56"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e1d36956e7551dece4abfdb5a3777dc", "guid": "bfdfe7dc352907fc980b868725387e98d7d69d6c5dce8ac5c9d18c643e30de06"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986609b449e15e64c484b48d8a735c7023", "guid": "bfdfe7dc352907fc980b868725387e98e3d136553c1a88705b7f7d040ca3a677"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e622a4c1e657747e615a19fc87ebbc24", "guid": "bfdfe7dc352907fc980b868725387e98ee9a4010577dcfb314226dce4f361e27"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980270f31ecb0da32aadbf156fd218f25a", "guid": "bfdfe7dc352907fc980b868725387e98e41af1ee7dc191c44cc159e5c2478f05"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9882b233d288a733fd6425b919c6c0aec4", "guid": "bfdfe7dc352907fc980b868725387e981410763db0b310f0024e0807fdafd422"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98644a12c0e02dc9634ea8edbee140ab27", "guid": "bfdfe7dc352907fc980b868725387e98b96aa6334ad332103ecc32edb480d0f1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e6c10e5b25a6ef6dbe83734542857a7", "guid": "bfdfe7dc352907fc980b868725387e98693ab963407f58e1d2533433981cc8a9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3a7157b8d272dde7eef9c364cf6d6a4", "guid": "bfdfe7dc352907fc980b868725387e98530872d8b12748c7cc42dfe53eb608f3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cfc3d8641d242c79d50208e87e2c151c", "guid": "bfdfe7dc352907fc980b868725387e986550f864d69eb5b789ef74c2f45eaf60"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98810966241a550e684f0851588d17b94e", "guid": "bfdfe7dc352907fc980b868725387e984ca6c988a2f9d3d9b69702cd0b293872"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98711819e91e349d7fe08ab136e8340a19", "guid": "bfdfe7dc352907fc980b868725387e98c0aaaa1042ab55ac68bc3d509c3c0620"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ccb2e7b5ef97f61b49811830d63a8cdf", "guid": "bfdfe7dc352907fc980b868725387e9887ca8566a609060d63341628c7144b4b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f4f303b546063e69d2f0a63f906b6dcc", "guid": "bfdfe7dc352907fc980b868725387e98c83e1006198954f0fa914a878343b98c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985a1c7518cb3fa5a46c81342a8081101c", "guid": "bfdfe7dc352907fc980b868725387e98315f7552f7ef320c380e76f948f8d0ed"}], "guid": "bfdfe7dc352907fc980b868725387e98cac5758aaab041b306b3055c248885f4", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9824f5d6ec5088eecbb83955c855580732", "guid": "bfdfe7dc352907fc980b868725387e98027d34dd1e1770c79dd54f5404987fd6"}], "guid": "bfdfe7dc352907fc980b868725387e98d8f70b5ff677ed02b77232961e037957", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98b7d49ad1fe66522e6eab9248dd2331d6", "targetReference": "bfdfe7dc352907fc980b868725387e98feffba4bc77d9f3d84a98c192cefdc8b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982ce797a352c424f0ec4bc4d5f726dae2", "guid": "bfdfe7dc352907fc980b868725387e98ec5268a1792f6b5cd4753bb67be7b719"}], "guid": "bfdfe7dc352907fc980b868725387e98ba929dd1b11c60f9a4f8fd06e4eaa3e7", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e985f0ec3a68eeed5241cb87afb05bcc380", "name": "OrderedSet"}, {"guid": "bfdfe7dc352907fc980b868725387e98feffba4bc77d9f3d84a98c192cefdc8b", "name": "flutter_inappwebview_ios-flutter_inappwebview_ios_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98a562549a031aeda8bf3440b79b3420bc", "name": "flutter_inappwebview_ios", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9810acd6d3a97e7ef91b90dda5618dc5c0", "name": "flutter_inappwebview_ios.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}