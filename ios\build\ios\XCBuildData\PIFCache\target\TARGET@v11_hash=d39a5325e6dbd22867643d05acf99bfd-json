{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a89c28e4d3b77d8f50dbb860d175d3b9", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98fb642b9e6833c9a9da0b313d08d10a2a", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986fd625a143c41d3295ce7ca7f3acf405", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d2e9b178b571525b4ebb5903a88e8d2e", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986fd625a143c41d3295ce7ca7f3acf405", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e989455cff1ec8fb362472ac0aaf93db75e", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c572348d98f59ed30382e31803ce35e6", "guid": "bfdfe7dc352907fc980b868725387e98b4ac5b3ada89c84ede52c11e52a23978", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98a394cd9c99154be401ab01de05e58eb0", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d071d15272311d750edbba3c8197248e", "guid": "bfdfe7dc352907fc980b868725387e9835350d83b8731ebaf7cb331877653b76"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983330cbd5bb80c175df6eff5ddbbb4c34", "guid": "bfdfe7dc352907fc980b868725387e98c42dded2a012ca21d628fd5ef7244b48"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9838efcbbe9c0af92cc8839313acacd36e", "guid": "bfdfe7dc352907fc980b868725387e989dd074f0ae8046dc51fe913c4783dbc5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98580f2a26f2387ea9b3f8f4633d5e7050", "guid": "bfdfe7dc352907fc980b868725387e98c00468db39f820642163380ef25081f6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9862509bd7d094c3ca2077d0186ccd9399", "guid": "bfdfe7dc352907fc980b868725387e98121a7e081d17cd620facb838fcb33bed"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b5d3cfb8d7f4298caf573b7f9d497ce5", "guid": "bfdfe7dc352907fc980b868725387e982dde39d1de98ba32fb91b7226b76ef50"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9832ee0a2ca9de548ea43aa2911af9ef23", "guid": "bfdfe7dc352907fc980b868725387e98d676cb41e31d5283d0ce1ee9cd151c25"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bcacfa5f2dabf6da1e0120ec48770251", "guid": "bfdfe7dc352907fc980b868725387e98ec10f17c40735d35dc7207f6df86965c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf47d52835cc0da3a42815501dcef467", "guid": "bfdfe7dc352907fc980b868725387e98069e15993a8165debb90e4dc0fcc1b97"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cae0980145ac1fb5deb34612bf219143", "guid": "bfdfe7dc352907fc980b868725387e980e52910444988197aa85286238fcc4cd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983963d07a4f4fc82929f7297ebb692921", "guid": "bfdfe7dc352907fc980b868725387e9864ebd5f7626cedb0dee942d02f6dd7e0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ade3b40d122acd9203892596834931a1", "guid": "bfdfe7dc352907fc980b868725387e98148652e3a8be53457a42d92da9d67f8c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987a0a6e53032bdd328cf6ea34fdca05ec", "guid": "bfdfe7dc352907fc980b868725387e9887ee85dcb0c2aee0ac20fae8b0bfde90"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983684a771e40021dbede469402d7a8c61", "guid": "bfdfe7dc352907fc980b868725387e98c718ace2f6841dbb64e1cd88af75f1dd"}], "guid": "bfdfe7dc352907fc980b868725387e986a78e001e55ec68cafe7b6a8514211b2", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9824f5d6ec5088eecbb83955c855580732", "guid": "bfdfe7dc352907fc980b868725387e98436a01274fc4b999c3766680e8ab66fd"}], "guid": "bfdfe7dc352907fc980b868725387e98ecd3902687848f9dde819d907c4512a1", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98e330e21021a00fcb5ca57c31a108989f", "targetReference": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f"}], "guid": "bfdfe7dc352907fc980b868725387e980bd55feab4231d3c859ceb4d13fe4fbb", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f", "name": "FirebaseCoreInternal-FirebaseCoreInternal_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e983d86e87924acfad2934921ce7ad9fbea", "name": "FirebaseCoreInternal.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}