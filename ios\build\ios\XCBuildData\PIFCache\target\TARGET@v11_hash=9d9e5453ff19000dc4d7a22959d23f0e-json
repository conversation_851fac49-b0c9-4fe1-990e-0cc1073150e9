{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982a4901f77847d14ee58b39a0c1194841", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseFirestore", "PRODUCT_NAME": "FirebaseFirestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e988f93bec7d7927d3b30e72549c83f4362", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984f8f9c12cfd29ae671269c39b4880737", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseFirestore", "PRODUCT_NAME": "FirebaseFirestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985cb1776105366eefd3f9ad9c662d164c", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984f8f9c12cfd29ae671269c39b4880737", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseFirestore", "PRODUCT_NAME": "FirebaseFirestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b80392260f75d31ea12ba16518ae2b6e", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9870ecdd4ad82ccb4086052af2ebc32ff9", "guid": "bfdfe7dc352907fc980b868725387e98f23d8eff90d06fccb14dd819dad13d18", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9869505f9f6cf2798f145479630f836b54", "guid": "bfdfe7dc352907fc980b868725387e9815eb43695c6263800d87394335dd598d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98794fc7ae8016110780b22b23fe3fc739", "guid": "bfdfe7dc352907fc980b868725387e98e19856fa41bf909f45cb5f5db5fb535a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9857e502c3593c3d364956a28222056b88", "guid": "bfdfe7dc352907fc980b868725387e986d2a7857ceabf944b0b62455adde8435", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9802f83d855a50ab725cec5da3b54c3e3b", "guid": "bfdfe7dc352907fc980b868725387e98fb02c9b32e0d4b6c435ba9fc2bbc0f06", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b0d15feeddc46cf4fd8d67a463abe148", "guid": "bfdfe7dc352907fc980b868725387e98606397ada3551b5015ff570a0b18b0ad", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9872a7d40148a4437c54665cdb16d64d50", "guid": "bfdfe7dc352907fc980b868725387e988954c0d123435fe4d4995315e3d6cbcd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98646ab249d8dd258ec503aaa3ec9c3381", "guid": "bfdfe7dc352907fc980b868725387e982ea2d03a87a8e644ed57d42dc5ea0847", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ab25b8e2eae500eb06e102af4dce8c3", "guid": "bfdfe7dc352907fc980b868725387e9862e8367c93f654f2e6910e47a8c6ec88", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b402f914ddfce940309d7c68c8774aa2", "guid": "bfdfe7dc352907fc980b868725387e983fb70b28d659a82724b5b0e283a5588e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98abc7a6df8f69257b93b497b7b5d67ef2", "guid": "bfdfe7dc352907fc980b868725387e98651144be5f48bdb8af96c36509d28959", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9842cf6a33ae5ab5d37d6bd5622f3a4243", "guid": "bfdfe7dc352907fc980b868725387e9845e6d7b6400e91d6caec8bec34d7302f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98495c72404b9477a3b7fc7f19fdf1bd04", "guid": "bfdfe7dc352907fc980b868725387e98205d7bdf5d4e7db568846f8d311405ba", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989bac9f6624cde918eee61c764a4ae432", "guid": "bfdfe7dc352907fc980b868725387e98cc8ca25c37adda1ed0d3ddf927cb957e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe8d730a597d556a6f0110e6fea17557", "guid": "bfdfe7dc352907fc980b868725387e985df1861c835fcb2e51af1059956f73da", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a9999faca83e1dd570c97b1b9dc58ec1", "guid": "bfdfe7dc352907fc980b868725387e98ee3393ccddec09f43cef692d97b15827", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9810e54cf65e4d94579835fbe98ec55bab", "guid": "bfdfe7dc352907fc980b868725387e98d0c3e09c9f1627ae46a3d64efc5ccc56", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9868e5912f33792c208ac9cc29c0181c97", "guid": "bfdfe7dc352907fc980b868725387e98fd9475e9cd3b1481149fced2ba30f141", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b053a2b06bcf6e2274508d2f9de9c7fb", "guid": "bfdfe7dc352907fc980b868725387e9870d7bf6ebdc961ced8d0fa75adaf83af", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b66b765eefad0e00054401ec2263acb4", "guid": "bfdfe7dc352907fc980b868725387e989ed7b24ddc8c2f21af0a97b9487f11a9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b8a9ffbf46a4bc63e23cc3765ae82c56", "guid": "bfdfe7dc352907fc980b868725387e98c960c36ebeed3108858067993187c258", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988633eb3dbd431a73132e108606ddb49f", "guid": "bfdfe7dc352907fc980b868725387e98682baa01af0e6282e318db509e6c1fe1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9875afe18113e52f0fbd978338c11cfe58", "guid": "bfdfe7dc352907fc980b868725387e981b3574ca655a84eb2a1e961e47ebc9fc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9898f91a06b891f7fcba4c01f7b2eacc5a", "guid": "bfdfe7dc352907fc980b868725387e9838a21272f8ca043fb4673ac86f5e9960", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ef69bba9c329098b114b45cc2f30610", "guid": "bfdfe7dc352907fc980b868725387e98891320ffaeb9c262bf2924b00642d21b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9840274b3bcf9274f0459bc3baf175b775", "guid": "bfdfe7dc352907fc980b868725387e988280d9b1e84df4e63bbf9bd56533fc4e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a4b978ed2fcfdc943562cd484807720c", "guid": "bfdfe7dc352907fc980b868725387e9880d00bea1be92ca63e7e03eebb6452b7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fba092f4545961ea7e9e4050baad0eae", "guid": "bfdfe7dc352907fc980b868725387e9877ded0fd412688c63d6009582b9ac7e3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f29ea37c8ba1e806ea6ccc8fc2c8f00b", "guid": "bfdfe7dc352907fc980b868725387e98522a70642b0b9eb7c631d2401d9b4411", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a1f588154f30a883cf1cf764e14c219a", "guid": "bfdfe7dc352907fc980b868725387e98d4255f74e0bd75a0f7f501f149f351fb", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98108a54b6798be6db8554f85523df2b3b", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9841a37584b547cc92b401cf2172ff5992", "guid": "bfdfe7dc352907fc980b868725387e988b4dd0c5c2aca7d2eca48d38b15c6902"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98628e4b5b2818aaaa07b461ef905f9638", "guid": "bfdfe7dc352907fc980b868725387e9867a900d316a438576daccced1074efed"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98038a07e335d2f6e19d90219bf0c78d12", "guid": "bfdfe7dc352907fc980b868725387e98d16829a42cea87f2d5efde5393994305"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e096d141f2033b688d28114080876e90", "guid": "bfdfe7dc352907fc980b868725387e98d37ecf2c0f868fd0c5a8aadfcdf6cec7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981da74dea4b83e3d1e838b6930da61943", "guid": "bfdfe7dc352907fc980b868725387e98e3c5e9f25b9e1f5ff2e8449164a62f43"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986ed8c96d13eb05f854741e20d6cb6c39", "guid": "bfdfe7dc352907fc980b868725387e98b94d956ed9c116c427174ab4403f986a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987466e1c16d4f99b75b18bea7974e22ea", "guid": "bfdfe7dc352907fc980b868725387e98a0a60c87b2cd4520f31041fe37f95396"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9809e15375318f6bc04e94ccd783e5a3f8", "guid": "bfdfe7dc352907fc980b868725387e98ad1b2a32b750bc13c870eed59fa3aa47"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98014f9099f8bf773c2555031cccf491ce", "guid": "bfdfe7dc352907fc980b868725387e989eb175e37943a43770abb59d933c1de5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c9d1d5446c08c7cc68e0612fa025071f", "guid": "bfdfe7dc352907fc980b868725387e98b73134f608a49608e54d388e608d544f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98246caa1f86505b3b84a155eca8122cd0", "guid": "bfdfe7dc352907fc980b868725387e9880fccdfa6a90dd0094682ef935e8f71e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b2609d084fdb1e31e3a48245c1ad487e", "guid": "bfdfe7dc352907fc980b868725387e98e5a00f15c25cc1f6efcad6edc770f05d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9890c260d6c0ebbd385eb3e3073d8e5014", "guid": "bfdfe7dc352907fc980b868725387e9878397aa653f4f3a11b0e93baa6c3923e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982b55479730ff0156d98cc9e3213ef603", "guid": "bfdfe7dc352907fc980b868725387e98a182039531a8c487eb010a8b767c1899"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c8c59c4118aa31e10f4d17cd1aa01f3", "guid": "bfdfe7dc352907fc980b868725387e984890e66e60902fe89ace3a0769505eb1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ab32d660dc72b319ca3f03a823462a7c", "guid": "bfdfe7dc352907fc980b868725387e98aa49cb813519fd6048a684245dade9ce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f91d821116e2808ca435a5c64f5e4cb3", "guid": "bfdfe7dc352907fc980b868725387e988d816efc2b596087e393d2419bd9589b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c728d2003c6d4ece396d9451e8ea3b6b", "guid": "bfdfe7dc352907fc980b868725387e9808695998c37df4d230be28ef5b373c85"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c422aad976a90eb652e62f21aa87ece3", "guid": "bfdfe7dc352907fc980b868725387e9808f6b35d8db593c534e1a305515ec6fb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9838b0dc148652d4be9cd6361594f3a851", "guid": "bfdfe7dc352907fc980b868725387e9845b3b190182a212721e4f9180038a4af"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981f01d0a1f0b2e54bfd03a798ce41b2ff", "guid": "bfdfe7dc352907fc980b868725387e98305c25647401de728feee911db1324d1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d50cf01b77c5d1bd13cf8d0d3b38a307", "guid": "bfdfe7dc352907fc980b868725387e9892cb782c6eace4f558ea95ecf81f9c3a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc342adedaa55e67653fcdd01d0d322c", "guid": "bfdfe7dc352907fc980b868725387e988fdaf520a6d4cc6c56578a754ab6c09b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984e4e7c4d54f9927f6aa53e287329004f", "guid": "bfdfe7dc352907fc980b868725387e986f64fc9aad824f3839097eab342c0759"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982c160a9c6a4da2983d3d7e492b48f08c", "guid": "bfdfe7dc352907fc980b868725387e9839c582b3fa1558d7ed4d5e91bb9d05d2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ca32bc4566aba8390c6fbe31718fe782", "guid": "bfdfe7dc352907fc980b868725387e98e926117b62ed7ea7cfe3484d7caafabe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98381ca98b999341ce7d40a9822e9d9158", "guid": "bfdfe7dc352907fc980b868725387e98a3f289afe9eb8ed7054129b4965cd72a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980cd031f24c9bd37d8e1bdc433662abdf", "guid": "bfdfe7dc352907fc980b868725387e98275f9b180bac8122517aaa0c3d57b491"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fac7ee9f6585f06eb1b81a0b2d6af669", "guid": "bfdfe7dc352907fc980b868725387e980a800ceb0aefcd9e0d7ad8d1dac8d701"}], "guid": "bfdfe7dc352907fc980b868725387e98199a2d9d0c1b4bee9c1293b36cc7e0d7", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9824f5d6ec5088eecbb83955c855580732", "guid": "bfdfe7dc352907fc980b868725387e98c86d864a3aa6fe3e2613c1103abbabc9"}], "guid": "bfdfe7dc352907fc980b868725387e98dce4edfdd822067c83c2140cce23de51", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98bf2456883a6fc362861ea887da816526", "targetReference": "bfdfe7dc352907fc980b868725387e9823d9e39e74dec872f3c67d98403b9340"}], "guid": "bfdfe7dc352907fc980b868725387e982d311d3819497b13f47f79e7c6df5541", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e982fcb5e27d041e48b96b3ab14ce32d5f2", "name": "FirebaseCoreExtension"}, {"guid": "bfdfe7dc352907fc980b868725387e9823d9e39e74dec872f3c67d98403b9340", "name": "FirebaseFirestore-FirebaseFirestore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98919212c22943df12241906dd601cdff4", "name": "FirebaseFirestoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e982a62e2c60acb8d344a6411a0606a13d4", "name": "FirebaseSharedSwift"}], "guid": "bfdfe7dc352907fc980b868725387e98c075cc473fa5680b867d51f1363214ff", "name": "FirebaseFirestore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9809dfd848e2259e061e90089e1647f5b7", "name": "FirebaseFirestore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}