<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>Squeak.ipa</key>
	<array>
		<dict>
			<key>architectures</key>
			<array>
				<string>arm64</string>
			</array>
			<key>buildNumber</key>
			<string>11</string>
			<key>certificate</key>
			<dict>
				<key>SHA1</key>
				<string>DDBED7FCC9902674B69DF42448089D8D0A9808CB</string>
				<key>dateExpires</key>
				<string>07/12/2025</string>
				<key>type</key>
				<string>Cloud Managed Apple Distribution</string>
			</dict>
			<key>embeddedBinaries</key>
			<array>
				<dict>
					<key>architectures</key>
					<array>
						<string>arm64</string>
					</array>
					<key>buildNumber</key>
					<string>1.0</string>
					<key>certificate</key>
					<dict>
						<key>SHA1</key>
						<string>DDBED7FCC9902674B69DF42448089D8D0A9808CB</string>
						<key>dateExpires</key>
						<string>07/12/2025</string>
						<key>type</key>
						<string>Cloud Managed Apple Distribution</string>
					</dict>
					<key>name</key>
					<string>App.framework</string>
					<key>symbols</key>
					<true/>
					<key>team</key>
					<dict>
						<key>id</key>
						<string>T2CM2V6HRB</string>
						<key>name</key>
						<string>Ahmed Omar</string>
					</dict>
					<key>versionNumber</key>
					<string>1.0</string>
				</dict>
				<dict>
					<key>architectures</key>
					<array>
						<string>arm64</string>
					</array>
					<key>buildNumber</key>
					<string>1</string>
					<key>certificate</key>
					<dict>
						<key>SHA1</key>
						<string>DDBED7FCC9902674B69DF42448089D8D0A9808CB</string>
						<key>dateExpires</key>
						<string>07/12/2025</string>
						<key>type</key>
						<string>Cloud Managed Apple Distribution</string>
					</dict>
					<key>name</key>
					<string>FBLPromises.framework</string>
					<key>symbols</key>
					<true/>
					<key>team</key>
					<dict>
						<key>id</key>
						<string>T2CM2V6HRB</string>
						<key>name</key>
						<string>Ahmed Omar</string>
					</dict>
					<key>versionNumber</key>
					<string>2.4.0</string>
				</dict>
				<dict>
					<key>architectures</key>
					<array>
						<string>arm64</string>
					</array>
					<key>buildNumber</key>
					<string>1</string>
					<key>certificate</key>
					<dict>
						<key>SHA1</key>
						<string>DDBED7FCC9902674B69DF42448089D8D0A9808CB</string>
						<key>dateExpires</key>
						<string>07/12/2025</string>
						<key>type</key>
						<string>Cloud Managed Apple Distribution</string>
					</dict>
					<key>name</key>
					<string>FirebaseAppCheckInterop.framework</string>
					<key>symbols</key>
					<true/>
					<key>team</key>
					<dict>
						<key>id</key>
						<string>T2CM2V6HRB</string>
						<key>name</key>
						<string>Ahmed Omar</string>
					</dict>
					<key>versionNumber</key>
					<string>11.5.0</string>
				</dict>
				<dict>
					<key>architectures</key>
					<array>
						<string>arm64</string>
					</array>
					<key>buildNumber</key>
					<string>1</string>
					<key>certificate</key>
					<dict>
						<key>SHA1</key>
						<string>DDBED7FCC9902674B69DF42448089D8D0A9808CB</string>
						<key>dateExpires</key>
						<string>07/12/2025</string>
						<key>type</key>
						<string>Cloud Managed Apple Distribution</string>
					</dict>
					<key>name</key>
					<string>FirebaseCore.framework</string>
					<key>symbols</key>
					<true/>
					<key>team</key>
					<dict>
						<key>id</key>
						<string>T2CM2V6HRB</string>
						<key>name</key>
						<string>Ahmed Omar</string>
					</dict>
					<key>versionNumber</key>
					<string>11.4.0</string>
				</dict>
				<dict>
					<key>architectures</key>
					<array>
						<string>arm64</string>
					</array>
					<key>buildNumber</key>
					<string>1</string>
					<key>certificate</key>
					<dict>
						<key>SHA1</key>
						<string>DDBED7FCC9902674B69DF42448089D8D0A9808CB</string>
						<key>dateExpires</key>
						<string>07/12/2025</string>
						<key>type</key>
						<string>Cloud Managed Apple Distribution</string>
					</dict>
					<key>name</key>
					<string>FirebaseCoreExtension.framework</string>
					<key>symbols</key>
					<true/>
					<key>team</key>
					<dict>
						<key>id</key>
						<string>T2CM2V6HRB</string>
						<key>name</key>
						<string>Ahmed Omar</string>
					</dict>
					<key>versionNumber</key>
					<string>11.4.1</string>
				</dict>
				<dict>
					<key>architectures</key>
					<array>
						<string>arm64</string>
					</array>
					<key>buildNumber</key>
					<string>1</string>
					<key>certificate</key>
					<dict>
						<key>SHA1</key>
						<string>DDBED7FCC9902674B69DF42448089D8D0A9808CB</string>
						<key>dateExpires</key>
						<string>07/12/2025</string>
						<key>type</key>
						<string>Cloud Managed Apple Distribution</string>
					</dict>
					<key>name</key>
					<string>FirebaseCoreInternal.framework</string>
					<key>symbols</key>
					<true/>
					<key>team</key>
					<dict>
						<key>id</key>
						<string>T2CM2V6HRB</string>
						<key>name</key>
						<string>Ahmed Omar</string>
					</dict>
					<key>versionNumber</key>
					<string>11.5.0</string>
				</dict>
				<dict>
					<key>architectures</key>
					<array>
						<string>arm64</string>
					</array>
					<key>buildNumber</key>
					<string>1</string>
					<key>certificate</key>
					<dict>
						<key>SHA1</key>
						<string>DDBED7FCC9902674B69DF42448089D8D0A9808CB</string>
						<key>dateExpires</key>
						<string>07/12/2025</string>
						<key>type</key>
						<string>Cloud Managed Apple Distribution</string>
					</dict>
					<key>name</key>
					<string>FirebaseFirestore.framework</string>
					<key>symbols</key>
					<true/>
					<key>team</key>
					<dict>
						<key>id</key>
						<string>T2CM2V6HRB</string>
						<key>name</key>
						<string>Ahmed Omar</string>
					</dict>
					<key>versionNumber</key>
					<string>11.4.0</string>
				</dict>
				<dict>
					<key>architectures</key>
					<array>
						<string>arm64</string>
					</array>
					<key>buildNumber</key>
					<string>1</string>
					<key>certificate</key>
					<dict>
						<key>SHA1</key>
						<string>DDBED7FCC9902674B69DF42448089D8D0A9808CB</string>
						<key>dateExpires</key>
						<string>07/12/2025</string>
						<key>type</key>
						<string>Cloud Managed Apple Distribution</string>
					</dict>
					<key>name</key>
					<string>FirebaseFirestoreInternal.framework</string>
					<key>symbols</key>
					<true/>
					<key>team</key>
					<dict>
						<key>id</key>
						<string>T2CM2V6HRB</string>
						<key>name</key>
						<string>Ahmed Omar</string>
					</dict>
					<key>versionNumber</key>
					<string>11.4.0</string>
				</dict>
				<dict>
					<key>architectures</key>
					<array>
						<string>arm64</string>
					</array>
					<key>buildNumber</key>
					<string>1</string>
					<key>certificate</key>
					<dict>
						<key>SHA1</key>
						<string>DDBED7FCC9902674B69DF42448089D8D0A9808CB</string>
						<key>dateExpires</key>
						<string>07/12/2025</string>
						<key>type</key>
						<string>Cloud Managed Apple Distribution</string>
					</dict>
					<key>name</key>
					<string>FirebaseInstallations.framework</string>
					<key>symbols</key>
					<true/>
					<key>team</key>
					<dict>
						<key>id</key>
						<string>T2CM2V6HRB</string>
						<key>name</key>
						<string>Ahmed Omar</string>
					</dict>
					<key>versionNumber</key>
					<string>11.4.0</string>
				</dict>
				<dict>
					<key>architectures</key>
					<array>
						<string>arm64</string>
					</array>
					<key>buildNumber</key>
					<string>1</string>
					<key>certificate</key>
					<dict>
						<key>SHA1</key>
						<string>DDBED7FCC9902674B69DF42448089D8D0A9808CB</string>
						<key>dateExpires</key>
						<string>07/12/2025</string>
						<key>type</key>
						<string>Cloud Managed Apple Distribution</string>
					</dict>
					<key>name</key>
					<string>FirebaseMessaging.framework</string>
					<key>symbols</key>
					<true/>
					<key>team</key>
					<dict>
						<key>id</key>
						<string>T2CM2V6HRB</string>
						<key>name</key>
						<string>Ahmed Omar</string>
					</dict>
					<key>versionNumber</key>
					<string>11.4.0</string>
				</dict>
				<dict>
					<key>architectures</key>
					<array>
						<string>arm64</string>
					</array>
					<key>buildNumber</key>
					<string>1</string>
					<key>certificate</key>
					<dict>
						<key>SHA1</key>
						<string>DDBED7FCC9902674B69DF42448089D8D0A9808CB</string>
						<key>dateExpires</key>
						<string>07/12/2025</string>
						<key>type</key>
						<string>Cloud Managed Apple Distribution</string>
					</dict>
					<key>name</key>
					<string>FirebaseSharedSwift.framework</string>
					<key>symbols</key>
					<true/>
					<key>team</key>
					<dict>
						<key>id</key>
						<string>T2CM2V6HRB</string>
						<key>name</key>
						<string>Ahmed Omar</string>
					</dict>
					<key>versionNumber</key>
					<string>11.5.0</string>
				</dict>
				<dict>
					<key>architectures</key>
					<array>
						<string>arm64</string>
					</array>
					<key>buildNumber</key>
					<string>1.0</string>
					<key>certificate</key>
					<dict>
						<key>SHA1</key>
						<string>DDBED7FCC9902674B69DF42448089D8D0A9808CB</string>
						<key>dateExpires</key>
						<string>07/12/2025</string>
						<key>type</key>
						<string>Cloud Managed Apple Distribution</string>
					</dict>
					<key>name</key>
					<string>Flutter.framework</string>
					<key>symbols</key>
					<true/>
					<key>team</key>
					<dict>
						<key>id</key>
						<string>T2CM2V6HRB</string>
						<key>name</key>
						<string>Ahmed Omar</string>
					</dict>
					<key>versionNumber</key>
					<string>1.0</string>
				</dict>
				<dict>
					<key>architectures</key>
					<array>
						<string>arm64</string>
					</array>
					<key>buildNumber</key>
					<string>1</string>
					<key>certificate</key>
					<dict>
						<key>SHA1</key>
						<string>DDBED7FCC9902674B69DF42448089D8D0A9808CB</string>
						<key>dateExpires</key>
						<string>07/12/2025</string>
						<key>type</key>
						<string>Cloud Managed Apple Distribution</string>
					</dict>
					<key>name</key>
					<string>GoogleDataTransport.framework</string>
					<key>symbols</key>
					<true/>
					<key>team</key>
					<dict>
						<key>id</key>
						<string>T2CM2V6HRB</string>
						<key>name</key>
						<string>Ahmed Omar</string>
					</dict>
					<key>versionNumber</key>
					<string>10.1.0</string>
				</dict>
				<dict>
					<key>architectures</key>
					<array>
						<string>arm64</string>
					</array>
					<key>buildNumber</key>
					<string>1</string>
					<key>certificate</key>
					<dict>
						<key>SHA1</key>
						<string>DDBED7FCC9902674B69DF42448089D8D0A9808CB</string>
						<key>dateExpires</key>
						<string>07/12/2025</string>
						<key>type</key>
						<string>Cloud Managed Apple Distribution</string>
					</dict>
					<key>name</key>
					<string>GoogleUtilities.framework</string>
					<key>symbols</key>
					<true/>
					<key>team</key>
					<dict>
						<key>id</key>
						<string>T2CM2V6HRB</string>
						<key>name</key>
						<string>Ahmed Omar</string>
					</dict>
					<key>versionNumber</key>
					<string>8.0.2</string>
				</dict>
				<dict>
					<key>architectures</key>
					<array>
						<string>arm64</string>
					</array>
					<key>buildNumber</key>
					<string>1</string>
					<key>certificate</key>
					<dict>
						<key>SHA1</key>
						<string>DDBED7FCC9902674B69DF42448089D8D0A9808CB</string>
						<key>dateExpires</key>
						<string>07/12/2025</string>
						<key>type</key>
						<string>Cloud Managed Apple Distribution</string>
					</dict>
					<key>name</key>
					<string>Mantle.framework</string>
					<key>symbols</key>
					<true/>
					<key>team</key>
					<dict>
						<key>id</key>
						<string>T2CM2V6HRB</string>
						<key>name</key>
						<string>Ahmed Omar</string>
					</dict>
					<key>versionNumber</key>
					<string>2.2.0</string>
				</dict>
				<dict>
					<key>architectures</key>
					<array>
						<string>arm64</string>
					</array>
					<key>buildNumber</key>
					<string>1</string>
					<key>certificate</key>
					<dict>
						<key>SHA1</key>
						<string>DDBED7FCC9902674B69DF42448089D8D0A9808CB</string>
						<key>dateExpires</key>
						<string>07/12/2025</string>
						<key>type</key>
						<string>Cloud Managed Apple Distribution</string>
					</dict>
					<key>name</key>
					<string>OrderedSet.framework</string>
					<key>symbols</key>
					<true/>
					<key>team</key>
					<dict>
						<key>id</key>
						<string>T2CM2V6HRB</string>
						<key>name</key>
						<string>Ahmed Omar</string>
					</dict>
					<key>versionNumber</key>
					<string>6.0.3</string>
				</dict>
				<dict>
					<key>architectures</key>
					<array>
						<string>arm64</string>
					</array>
					<key>buildNumber</key>
					<string>1</string>
					<key>certificate</key>
					<dict>
						<key>SHA1</key>
						<string>DDBED7FCC9902674B69DF42448089D8D0A9808CB</string>
						<key>dateExpires</key>
						<string>07/12/2025</string>
						<key>type</key>
						<string>Cloud Managed Apple Distribution</string>
					</dict>
					<key>name</key>
					<string>SDWebImage.framework</string>
					<key>symbols</key>
					<true/>
					<key>team</key>
					<dict>
						<key>id</key>
						<string>T2CM2V6HRB</string>
						<key>name</key>
						<string>Ahmed Omar</string>
					</dict>
					<key>versionNumber</key>
					<string>5.20.0</string>
				</dict>
				<dict>
					<key>architectures</key>
					<array>
						<string>arm64</string>
					</array>
					<key>buildNumber</key>
					<string>1</string>
					<key>certificate</key>
					<dict>
						<key>SHA1</key>
						<string>DDBED7FCC9902674B69DF42448089D8D0A9808CB</string>
						<key>dateExpires</key>
						<string>07/12/2025</string>
						<key>type</key>
						<string>Cloud Managed Apple Distribution</string>
					</dict>
					<key>name</key>
					<string>SDWebImageWebPCoder.framework</string>
					<key>symbols</key>
					<true/>
					<key>team</key>
					<dict>
						<key>id</key>
						<string>T2CM2V6HRB</string>
						<key>name</key>
						<string>Ahmed Omar</string>
					</dict>
					<key>versionNumber</key>
					<string>0.14.6</string>
				</dict>
				<dict>
					<key>architectures</key>
					<array>
						<string>arm64</string>
					</array>
					<key>buildNumber</key>
					<string>1</string>
					<key>certificate</key>
					<dict>
						<key>SHA1</key>
						<string>DDBED7FCC9902674B69DF42448089D8D0A9808CB</string>
						<key>dateExpires</key>
						<string>07/12/2025</string>
						<key>type</key>
						<string>Cloud Managed Apple Distribution</string>
					</dict>
					<key>name</key>
					<string>absl.framework</string>
					<key>symbols</key>
					<true/>
					<key>team</key>
					<dict>
						<key>id</key>
						<string>T2CM2V6HRB</string>
						<key>name</key>
						<string>Ahmed Omar</string>
					</dict>
					<key>versionNumber</key>
					<string>1.20240116.2</string>
				</dict>
				<dict>
					<key>architectures</key>
					<array>
						<string>arm64</string>
					</array>
					<key>buildNumber</key>
					<string>1</string>
					<key>certificate</key>
					<dict>
						<key>SHA1</key>
						<string>DDBED7FCC9902674B69DF42448089D8D0A9808CB</string>
						<key>dateExpires</key>
						<string>07/12/2025</string>
						<key>type</key>
						<string>Cloud Managed Apple Distribution</string>
					</dict>
					<key>name</key>
					<string>app_links.framework</string>
					<key>symbols</key>
					<true/>
					<key>team</key>
					<dict>
						<key>id</key>
						<string>T2CM2V6HRB</string>
						<key>name</key>
						<string>Ahmed Omar</string>
					</dict>
					<key>versionNumber</key>
					<string>0.0.2</string>
				</dict>
				<dict>
					<key>architectures</key>
					<array>
						<string>arm64</string>
					</array>
					<key>buildNumber</key>
					<string>1</string>
					<key>certificate</key>
					<dict>
						<key>SHA1</key>
						<string>DDBED7FCC9902674B69DF42448089D8D0A9808CB</string>
						<key>dateExpires</key>
						<string>07/12/2025</string>
						<key>type</key>
						<string>Cloud Managed Apple Distribution</string>
					</dict>
					<key>name</key>
					<string>connectivity_plus.framework</string>
					<key>symbols</key>
					<true/>
					<key>team</key>
					<dict>
						<key>id</key>
						<string>T2CM2V6HRB</string>
						<key>name</key>
						<string>Ahmed Omar</string>
					</dict>
					<key>versionNumber</key>
					<string>0.0.1</string>
				</dict>
				<dict>
					<key>architectures</key>
					<array>
						<string>arm64</string>
					</array>
					<key>buildNumber</key>
					<string>1</string>
					<key>certificate</key>
					<dict>
						<key>SHA1</key>
						<string>DDBED7FCC9902674B69DF42448089D8D0A9808CB</string>
						<key>dateExpires</key>
						<string>07/12/2025</string>
						<key>type</key>
						<string>Cloud Managed Apple Distribution</string>
					</dict>
					<key>name</key>
					<string>flutter_image_compress_common.framework</string>
					<key>symbols</key>
					<true/>
					<key>team</key>
					<dict>
						<key>id</key>
						<string>T2CM2V6HRB</string>
						<key>name</key>
						<string>Ahmed Omar</string>
					</dict>
					<key>versionNumber</key>
					<string>1.0.0</string>
				</dict>
				<dict>
					<key>architectures</key>
					<array>
						<string>arm64</string>
					</array>
					<key>buildNumber</key>
					<string>1</string>
					<key>certificate</key>
					<dict>
						<key>SHA1</key>
						<string>DDBED7FCC9902674B69DF42448089D8D0A9808CB</string>
						<key>dateExpires</key>
						<string>07/12/2025</string>
						<key>type</key>
						<string>Cloud Managed Apple Distribution</string>
					</dict>
					<key>name</key>
					<string>flutter_inappwebview_ios.framework</string>
					<key>symbols</key>
					<true/>
					<key>team</key>
					<dict>
						<key>id</key>
						<string>T2CM2V6HRB</string>
						<key>name</key>
						<string>Ahmed Omar</string>
					</dict>
					<key>versionNumber</key>
					<string>0.0.1</string>
				</dict>
				<dict>
					<key>architectures</key>
					<array>
						<string>arm64</string>
					</array>
					<key>buildNumber</key>
					<string>1</string>
					<key>certificate</key>
					<dict>
						<key>SHA1</key>
						<string>DDBED7FCC9902674B69DF42448089D8D0A9808CB</string>
						<key>dateExpires</key>
						<string>07/12/2025</string>
						<key>type</key>
						<string>Cloud Managed Apple Distribution</string>
					</dict>
					<key>name</key>
					<string>flutter_local_notifications.framework</string>
					<key>symbols</key>
					<true/>
					<key>team</key>
					<dict>
						<key>id</key>
						<string>T2CM2V6HRB</string>
						<key>name</key>
						<string>Ahmed Omar</string>
					</dict>
					<key>versionNumber</key>
					<string>0.0.1</string>
				</dict>
				<dict>
					<key>architectures</key>
					<array>
						<string>arm64</string>
					</array>
					<key>buildNumber</key>
					<string>1</string>
					<key>certificate</key>
					<dict>
						<key>SHA1</key>
						<string>DDBED7FCC9902674B69DF42448089D8D0A9808CB</string>
						<key>dateExpires</key>
						<string>07/12/2025</string>
						<key>type</key>
						<string>Cloud Managed Apple Distribution</string>
					</dict>
					<key>name</key>
					<string>grpc.framework</string>
					<key>symbols</key>
					<true/>
					<key>team</key>
					<dict>
						<key>id</key>
						<string>T2CM2V6HRB</string>
						<key>name</key>
						<string>Ahmed Omar</string>
					</dict>
					<key>versionNumber</key>
					<string>1.65.5</string>
				</dict>
				<dict>
					<key>architectures</key>
					<array>
						<string>arm64</string>
					</array>
					<key>buildNumber</key>
					<string>1</string>
					<key>certificate</key>
					<dict>
						<key>SHA1</key>
						<string>DDBED7FCC9902674B69DF42448089D8D0A9808CB</string>
						<key>dateExpires</key>
						<string>07/12/2025</string>
						<key>type</key>
						<string>Cloud Managed Apple Distribution</string>
					</dict>
					<key>name</key>
					<string>grpcpp.framework</string>
					<key>symbols</key>
					<true/>
					<key>team</key>
					<dict>
						<key>id</key>
						<string>T2CM2V6HRB</string>
						<key>name</key>
						<string>Ahmed Omar</string>
					</dict>
					<key>versionNumber</key>
					<string>1.65.5</string>
				</dict>
				<dict>
					<key>architectures</key>
					<array>
						<string>arm64</string>
					</array>
					<key>buildNumber</key>
					<string>1</string>
					<key>certificate</key>
					<dict>
						<key>SHA1</key>
						<string>DDBED7FCC9902674B69DF42448089D8D0A9808CB</string>
						<key>dateExpires</key>
						<string>07/12/2025</string>
						<key>type</key>
						<string>Cloud Managed Apple Distribution</string>
					</dict>
					<key>name</key>
					<string>image_picker_ios.framework</string>
					<key>symbols</key>
					<true/>
					<key>team</key>
					<dict>
						<key>id</key>
						<string>T2CM2V6HRB</string>
						<key>name</key>
						<string>Ahmed Omar</string>
					</dict>
					<key>versionNumber</key>
					<string>0.0.1</string>
				</dict>
				<dict>
					<key>architectures</key>
					<array>
						<string>arm64</string>
					</array>
					<key>buildNumber</key>
					<string>1</string>
					<key>certificate</key>
					<dict>
						<key>SHA1</key>
						<string>DDBED7FCC9902674B69DF42448089D8D0A9808CB</string>
						<key>dateExpires</key>
						<string>07/12/2025</string>
						<key>type</key>
						<string>Cloud Managed Apple Distribution</string>
					</dict>
					<key>name</key>
					<string>leveldb.framework</string>
					<key>symbols</key>
					<true/>
					<key>team</key>
					<dict>
						<key>id</key>
						<string>T2CM2V6HRB</string>
						<key>name</key>
						<string>Ahmed Omar</string>
					</dict>
					<key>versionNumber</key>
					<string>1.22.6</string>
				</dict>
				<dict>
					<key>architectures</key>
					<array>
						<string>arm64</string>
					</array>
					<key>buildNumber</key>
					<string>1</string>
					<key>certificate</key>
					<dict>
						<key>SHA1</key>
						<string>DDBED7FCC9902674B69DF42448089D8D0A9808CB</string>
						<key>dateExpires</key>
						<string>07/12/2025</string>
						<key>type</key>
						<string>Cloud Managed Apple Distribution</string>
					</dict>
					<key>name</key>
					<string>libwebp.framework</string>
					<key>symbols</key>
					<true/>
					<key>team</key>
					<dict>
						<key>id</key>
						<string>T2CM2V6HRB</string>
						<key>name</key>
						<string>Ahmed Omar</string>
					</dict>
					<key>versionNumber</key>
					<string>1.3.2</string>
				</dict>
				<dict>
					<key>architectures</key>
					<array>
						<string>arm64</string>
					</array>
					<key>buildNumber</key>
					<string>1</string>
					<key>certificate</key>
					<dict>
						<key>SHA1</key>
						<string>DDBED7FCC9902674B69DF42448089D8D0A9808CB</string>
						<key>dateExpires</key>
						<string>07/12/2025</string>
						<key>type</key>
						<string>Cloud Managed Apple Distribution</string>
					</dict>
					<key>name</key>
					<string>nanopb.framework</string>
					<key>symbols</key>
					<true/>
					<key>team</key>
					<dict>
						<key>id</key>
						<string>T2CM2V6HRB</string>
						<key>name</key>
						<string>Ahmed Omar</string>
					</dict>
					<key>versionNumber</key>
					<string>3.30910.0</string>
				</dict>
				<dict>
					<key>architectures</key>
					<array>
						<string>arm64</string>
					</array>
					<key>buildNumber</key>
					<string>1</string>
					<key>certificate</key>
					<dict>
						<key>SHA1</key>
						<string>DDBED7FCC9902674B69DF42448089D8D0A9808CB</string>
						<key>dateExpires</key>
						<string>07/12/2025</string>
						<key>type</key>
						<string>Cloud Managed Apple Distribution</string>
					</dict>
					<key>name</key>
					<string>network_info_plus.framework</string>
					<key>symbols</key>
					<true/>
					<key>team</key>
					<dict>
						<key>id</key>
						<string>T2CM2V6HRB</string>
						<key>name</key>
						<string>Ahmed Omar</string>
					</dict>
					<key>versionNumber</key>
					<string>0.0.1</string>
				</dict>
				<dict>
					<key>architectures</key>
					<array>
						<string>arm64</string>
					</array>
					<key>buildNumber</key>
					<string>1</string>
					<key>certificate</key>
					<dict>
						<key>SHA1</key>
						<string>DDBED7FCC9902674B69DF42448089D8D0A9808CB</string>
						<key>dateExpires</key>
						<string>07/12/2025</string>
						<key>type</key>
						<string>Cloud Managed Apple Distribution</string>
					</dict>
					<key>name</key>
					<string>openssl_grpc.framework</string>
					<key>symbols</key>
					<true/>
					<key>team</key>
					<dict>
						<key>id</key>
						<string>T2CM2V6HRB</string>
						<key>name</key>
						<string>Ahmed Omar</string>
					</dict>
					<key>versionNumber</key>
					<string>0.0.36</string>
				</dict>
				<dict>
					<key>architectures</key>
					<array>
						<string>arm64</string>
					</array>
					<key>buildNumber</key>
					<string>1</string>
					<key>certificate</key>
					<dict>
						<key>SHA1</key>
						<string>DDBED7FCC9902674B69DF42448089D8D0A9808CB</string>
						<key>dateExpires</key>
						<string>07/12/2025</string>
						<key>type</key>
						<string>Cloud Managed Apple Distribution</string>
					</dict>
					<key>name</key>
					<string>package_info_plus.framework</string>
					<key>symbols</key>
					<true/>
					<key>team</key>
					<dict>
						<key>id</key>
						<string>T2CM2V6HRB</string>
						<key>name</key>
						<string>Ahmed Omar</string>
					</dict>
					<key>versionNumber</key>
					<string>0.4.5</string>
				</dict>
				<dict>
					<key>architectures</key>
					<array>
						<string>arm64</string>
					</array>
					<key>buildNumber</key>
					<string>1</string>
					<key>certificate</key>
					<dict>
						<key>SHA1</key>
						<string>DDBED7FCC9902674B69DF42448089D8D0A9808CB</string>
						<key>dateExpires</key>
						<string>07/12/2025</string>
						<key>type</key>
						<string>Cloud Managed Apple Distribution</string>
					</dict>
					<key>name</key>
					<string>path_provider_foundation.framework</string>
					<key>symbols</key>
					<true/>
					<key>team</key>
					<dict>
						<key>id</key>
						<string>T2CM2V6HRB</string>
						<key>name</key>
						<string>Ahmed Omar</string>
					</dict>
					<key>versionNumber</key>
					<string>0.0.1</string>
				</dict>
				<dict>
					<key>architectures</key>
					<array>
						<string>arm64</string>
					</array>
					<key>buildNumber</key>
					<string>1</string>
					<key>certificate</key>
					<dict>
						<key>SHA1</key>
						<string>DDBED7FCC9902674B69DF42448089D8D0A9808CB</string>
						<key>dateExpires</key>
						<string>07/12/2025</string>
						<key>type</key>
						<string>Cloud Managed Apple Distribution</string>
					</dict>
					<key>name</key>
					<string>pointer_interceptor_ios.framework</string>
					<key>symbols</key>
					<true/>
					<key>team</key>
					<dict>
						<key>id</key>
						<string>T2CM2V6HRB</string>
						<key>name</key>
						<string>Ahmed Omar</string>
					</dict>
					<key>versionNumber</key>
					<string>0.0.1</string>
				</dict>
				<dict>
					<key>architectures</key>
					<array>
						<string>arm64</string>
					</array>
					<key>buildNumber</key>
					<string>1</string>
					<key>certificate</key>
					<dict>
						<key>SHA1</key>
						<string>DDBED7FCC9902674B69DF42448089D8D0A9808CB</string>
						<key>dateExpires</key>
						<string>07/12/2025</string>
						<key>type</key>
						<string>Cloud Managed Apple Distribution</string>
					</dict>
					<key>name</key>
					<string>printing.framework</string>
					<key>symbols</key>
					<true/>
					<key>team</key>
					<dict>
						<key>id</key>
						<string>T2CM2V6HRB</string>
						<key>name</key>
						<string>Ahmed Omar</string>
					</dict>
					<key>versionNumber</key>
					<string>1.0.0</string>
				</dict>
				<dict>
					<key>architectures</key>
					<array>
						<string>arm64</string>
					</array>
					<key>buildNumber</key>
					<string>1</string>
					<key>certificate</key>
					<dict>
						<key>SHA1</key>
						<string>DDBED7FCC9902674B69DF42448089D8D0A9808CB</string>
						<key>dateExpires</key>
						<string>07/12/2025</string>
						<key>type</key>
						<string>Cloud Managed Apple Distribution</string>
					</dict>
					<key>name</key>
					<string>share_plus.framework</string>
					<key>symbols</key>
					<true/>
					<key>team</key>
					<dict>
						<key>id</key>
						<string>T2CM2V6HRB</string>
						<key>name</key>
						<string>Ahmed Omar</string>
					</dict>
					<key>versionNumber</key>
					<string>0.0.1</string>
				</dict>
				<dict>
					<key>architectures</key>
					<array>
						<string>arm64</string>
					</array>
					<key>buildNumber</key>
					<string>1</string>
					<key>certificate</key>
					<dict>
						<key>SHA1</key>
						<string>DDBED7FCC9902674B69DF42448089D8D0A9808CB</string>
						<key>dateExpires</key>
						<string>07/12/2025</string>
						<key>type</key>
						<string>Cloud Managed Apple Distribution</string>
					</dict>
					<key>name</key>
					<string>shared_preferences_foundation.framework</string>
					<key>symbols</key>
					<true/>
					<key>team</key>
					<dict>
						<key>id</key>
						<string>T2CM2V6HRB</string>
						<key>name</key>
						<string>Ahmed Omar</string>
					</dict>
					<key>versionNumber</key>
					<string>0.0.1</string>
				</dict>
				<dict>
					<key>architectures</key>
					<array>
						<string>arm64</string>
					</array>
					<key>buildNumber</key>
					<string>1</string>
					<key>certificate</key>
					<dict>
						<key>SHA1</key>
						<string>DDBED7FCC9902674B69DF42448089D8D0A9808CB</string>
						<key>dateExpires</key>
						<string>07/12/2025</string>
						<key>type</key>
						<string>Cloud Managed Apple Distribution</string>
					</dict>
					<key>name</key>
					<string>url_launcher_ios.framework</string>
					<key>symbols</key>
					<true/>
					<key>team</key>
					<dict>
						<key>id</key>
						<string>T2CM2V6HRB</string>
						<key>name</key>
						<string>Ahmed Omar</string>
					</dict>
					<key>versionNumber</key>
					<string>0.0.1</string>
				</dict>
				<dict>
					<key>architectures</key>
					<array>
						<string>arm64</string>
					</array>
					<key>buildNumber</key>
					<string>1</string>
					<key>certificate</key>
					<dict>
						<key>SHA1</key>
						<string>DDBED7FCC9902674B69DF42448089D8D0A9808CB</string>
						<key>dateExpires</key>
						<string>07/12/2025</string>
						<key>type</key>
						<string>Cloud Managed Apple Distribution</string>
					</dict>
					<key>name</key>
					<string>video_player_avfoundation.framework</string>
					<key>symbols</key>
					<true/>
					<key>team</key>
					<dict>
						<key>id</key>
						<string>T2CM2V6HRB</string>
						<key>name</key>
						<string>Ahmed Omar</string>
					</dict>
					<key>versionNumber</key>
					<string>0.0.1</string>
				</dict>
				<dict>
					<key>architectures</key>
					<array>
						<string>arm64</string>
					</array>
					<key>buildNumber</key>
					<string>1</string>
					<key>certificate</key>
					<dict>
						<key>SHA1</key>
						<string>DDBED7FCC9902674B69DF42448089D8D0A9808CB</string>
						<key>dateExpires</key>
						<string>07/12/2025</string>
						<key>type</key>
						<string>Cloud Managed Apple Distribution</string>
					</dict>
					<key>name</key>
					<string>wakelock_plus.framework</string>
					<key>symbols</key>
					<true/>
					<key>team</key>
					<dict>
						<key>id</key>
						<string>T2CM2V6HRB</string>
						<key>name</key>
						<string>Ahmed Omar</string>
					</dict>
					<key>versionNumber</key>
					<string>0.0.1</string>
				</dict>
				<dict>
					<key>architectures</key>
					<array>
						<string>arm64</string>
					</array>
					<key>buildNumber</key>
					<string>1</string>
					<key>certificate</key>
					<dict>
						<key>SHA1</key>
						<string>DDBED7FCC9902674B69DF42448089D8D0A9808CB</string>
						<key>dateExpires</key>
						<string>07/12/2025</string>
						<key>type</key>
						<string>Cloud Managed Apple Distribution</string>
					</dict>
					<key>name</key>
					<string>workmanager.framework</string>
					<key>symbols</key>
					<true/>
					<key>team</key>
					<dict>
						<key>id</key>
						<string>T2CM2V6HRB</string>
						<key>name</key>
						<string>Ahmed Omar</string>
					</dict>
					<key>versionNumber</key>
					<string>0.0.1</string>
				</dict>
			</array>
			<key>entitlements</key>
			<dict>
				<key>application-identifier</key>
				<string>T2CM2V6HRB.com.softicare.squeak</string>
				<key>aps-environment</key>
				<string>production</string>
				<key>beta-reports-active</key>
				<true/>
				<key>com.apple.developer.associated-domains</key>
				<array>
					<string>applinks:squeakvetcare.web.app</string>
					<string>webcredentials:squeakvetcare.web.app</string>
				</array>
				<key>com.apple.developer.team-identifier</key>
				<string>T2CM2V6HRB</string>
				<key>get-task-allow</key>
				<false/>
			</dict>
			<key>name</key>
			<string>Runner.app</string>
			<key>profile</key>
			<dict>
				<key>UUID</key>
				<string>5217e1cb-07c3-4fe9-8259-4ae4b312f2cf</string>
				<key>dateExpires</key>
				<string>07/12/2025</string>
				<key>name</key>
				<string>iOS Team Store Provisioning Profile: com.softicare.squeak</string>
			</dict>
			<key>symbols</key>
			<true/>
			<key>team</key>
			<dict>
				<key>id</key>
				<string>T2CM2V6HRB</string>
				<key>name</key>
				<string>Ahmed Omar</string>
			</dict>
			<key>versionNumber</key>
			<string>1.0.0</string>
		</dict>
	</array>
</dict>
</plist>
