// lib/screens/notification_screen.dart
import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';
import 'package:squeak/lib/models/notification_models.dart'; // Adjust import path
import 'package:squeak/lib/widgets/notification_card.dart'; // Adjust import path
import 'package:squeak/lib/widgets/notification_dialog.dart'; // Adjust import path

class NotificationScreen extends StatefulWidget {
  const NotificationScreen({Key? key}) : super(key: key);

  @override
  State<NotificationScreen> createState() => _NotificationScreenState();
}

class _NotificationScreenState extends State<NotificationScreen> {
  List<NotificationEntities> _notifications = [];
  bool _isLoading = true;
  NotificationEntities? _selectedNotification;

  @override
  void initState() {
    super.initState();
    _fetchNotifications();
  }

  Future<void> _fetchNotifications() async {
    setState(() {
      _isLoading = true;
    });

    // Simulate fetching data from an API
    await Future.delayed(const Duration(seconds: 2));

    _notifications = [
      NotificationEntities(
        id: "1",
        title: "New Appointment Confirmed!",
        message: "Your appointment with <PERSON><PERSON> on July 20th at 10:00 AM is confirmed.",
        eventType: NotificationType.NewAppointmentOrReservation,
        eventTypeId: "appt-123",
        logo: "",
        notificationEvents: [
          NotificationEventEntities(
            id: "event-1",
            isRead: false,
            isView: false,
            viewAt: DateTime.now(),
            sendAt: DateTime.now(),
            readedAt: DateTime.now(),
            notificationStatues: 0,
            note: "",
          ),
        ],
        createdAt: DateTime.now().subtract(const Duration(minutes: 5)),
        isActive: true,
        isDeleted: false,
      ),
      NotificationEntities(
        id: "2",
        title: "New Comment on Your Post",
        message: "John Doe commented on your post 'My lovely cat Whiskers'.",
        eventType: NotificationType.NewCommentOnYourPost,
        eventTypeId: "post-456",
        logo: "",
        notificationEvents: [
          NotificationEventEntities(
            id: "event-2",
            isRead: false,
            isView: false,
            viewAt: DateTime.now(),
            sendAt: DateTime.now(),
            readedAt: DateTime.now(),
            notificationStatues: 0,
            note: "",
          ),
        ],
        createdAt: DateTime.now().subtract(const Duration(minutes: 30)),
        isActive: true,
        isDeleted: false,
      ),
      NotificationEntities(
        id: "3",
        title: "Follow Request from Jane Doe",
        message: "Jane Doe wants to follow you.",
        eventType: NotificationType.FollowRequest,
        eventTypeId: "user-789",
        logo: "",
        notificationEvents: [
          NotificationEventEntities(
            id: "event-3",
            isRead: true,
            isView: true,
            viewAt: DateTime.now(),
            sendAt: DateTime.now(),
            readedAt: DateTime.now(),
            notificationStatues: 0,
            note: "",
          ),
        ],
        createdAt: DateTime.now().subtract(const Duration(days: 2)),
        isActive: true,
        isDeleted: false,
      ),
      NotificationEntities(
        id: "4",
        title: "Vaccination Reminder for Buddy",
        message: "Buddy's annual rabies vaccination is due next week.",
        eventType: NotificationType.VaccinationReminder,
        eventTypeId: "pet-101",
        logo: "",
        notificationEvents: [
          NotificationEventEntities(
            id: "event-4",
            isRead: false,
            isView: false,
            viewAt: DateTime.now(),
            sendAt: DateTime.now(),
            readedAt: DateTime.now(),
            notificationStatues: 0,
            note: "",
          ),
        ],
        createdAt: DateTime.now().subtract(const Duration(days: 7)),
        isActive: true,
        isDeleted: false,
      ),
      NotificationEntities(
        id: "5",
        title: "Appointment Completed: Grooming",
        message: "Your pet's grooming appointment on July 10th has been completed.",
        eventType: NotificationType.AppointmentCompleted,
        eventTypeId: "appt-124",
        logo: "",
        notificationEvents: [
          NotificationEventEntities(
            id: "event-5",
            isRead: true,
            isView: true,
            viewAt: DateTime.now(),
            sendAt: DateTime.now(),
            readedAt: DateTime.now(),
            notificationStatues: 0,
            note: "",
          ),
        ],
        createdAt: DateTime.now().subtract(const Duration(days: 10)),
        isActive: true,
        isDeleted: false,
      ),
      NotificationEntities(
        id: "6",
        title: "New Pet Added to Your Profile",
        message: "You successfully added 'Luna' to your pet profiles.",
        eventType: NotificationType.NewPetAdded,
        eventTypeId: "pet-102",
        logo: "",
        notificationEvents: [
          NotificationEventEntities(
            id: "event-6",
            isRead: false,
            isView: false,
            viewAt: DateTime.now(),
            sendAt: DateTime.now(),
            readedAt: DateTime.now(),
            notificationStatues: 0,
            note: "",
          ),
        ],
        createdAt: DateTime.now().subtract(const Duration(days: 1)),
        isActive: true,
        isDeleted: false,
      ),
      NotificationEntities(
        id: "7",
        title: "New Post: 'Summer Pet Care Tips'",
        message: "A new article on 'Summer Pet Care Tips' has been published.",
        eventType: NotificationType.NewPostAdded,
        eventTypeId: "article-200",
        logo: "",
        notificationEvents: [
          NotificationEventEntities(
            id: "event-7",
            isRead: false,
            isView: false,
            viewAt: DateTime.now(),
            sendAt: DateTime.now(),
            readedAt: DateTime.now(),
            notificationStatues: 0,
            note: "",
          ),
        ],
        createdAt: DateTime.now().subtract(const Duration(minutes: 15)),
        isActive: true,
        isDeleted: false,
      ),
      NotificationEntities(
        id: "8",
        title: "Reservation Reminder: Boarding",
        message: "Your pet's boarding reservation starts tomorrow at 9 AM.",
        eventType: NotificationType.ReservationReminder,
        eventTypeId: "res-300",
        logo: "",
        notificationEvents: [
          NotificationEventEntities(
            id: "event-8",
            isRead: false,
            isView: false,
            viewAt: DateTime.now(),
            sendAt: DateTime.now(),
            readedAt: DateTime.now(),
            notificationStatues: 0,
            note: "",
          ),
        ],
        createdAt: DateTime.now().subtract(const Duration(hours: 1)),
        isActive: true,
        isDeleted: false,
      ),
      NotificationEntities(
        id: "9",
        title: "Wallet Reward: \$10 Bonus!",
        message: "Congratulations! A \$10 reward has been added to your wallet.",
        eventType: NotificationType.AddWalletReward,
        eventTypeId: "reward-500",
        logo: "",
        notificationEvents: [
          NotificationEventEntities(
            id: "event-9",
            isRead: false,
            isView: false,
            viewAt: DateTime.now(),
            sendAt: DateTime.now(),
            readedAt: DateTime.now(),
            notificationStatues: 0,
            note: "",
          ),
        ],
        createdAt: DateTime.now().subtract(const Duration(days: 3)),
        isActive: true,
        isDeleted: false,
      ),
      NotificationEntities(
        id: "10",
        title: "New Reaction on Your Post",
        message: "Someone reacted with a ❤️ to your post 'Happy Dog Day!'",
        eventType: NotificationType.NewReactionOnPost,
        eventTypeId: "post-457",
        logo: "",
        notificationEvents: [
          NotificationEventEntities(
            id: "event-10",
            isRead: false,
            isView: false,
            viewAt: DateTime.now(),
            sendAt: DateTime.now(),
            readedAt: DateTime.now(),
            notificationStatues: 0,
            note: "",
          ),
        ],
        createdAt: DateTime.now().subtract(const Duration(minutes: 45)),
        isActive: true,
        isDeleted: false,
      ),
    ];

    setState(() {
      _isLoading = false;
    });
  }

  void _showNotificationDialog(NotificationEntities notification) {
    setState(() {
      _selectedNotification = notification;
    });
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return NotificationDialog(
          notification: notification,
          onClose: () {
            Navigator.of(context).pop();
            setState(() {
              _selectedNotification = null;
            });
          },
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true, // Allows body to go behind transparent app bar
      appBar: AppBar(
        backgroundColor: Colors.transparent, // Transparent app bar
        elevation: 0,
        title: const Text(
          'Notifications',
          style: TextStyle(
            color: Colors.white,
            fontSize: 28,
            fontWeight: FontWeight.bold,
            shadows: [
              Shadow(
                blurRadius: 8.0,
                color: Colors.black54,
                offset: Offset(2.0, 2.0),
              ),
            ],
          ),
        ),
        centerTitle: true,
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color(0xFF4A148C), // Dark Purple
              Color(0xFF311B92), // Dark Indigo
              Color(0xFF1A237E), // Dark Blue
            ],
          ),
        ),
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: _isLoading
                ? _buildShimmerList()
                : _notifications.isEmpty
                    ? _buildEmptyState()
                    : ListView.builder(
                        itemCount: _notifications.length,
                        itemBuilder: (context, index) {
                          return Padding(
                            padding: const EdgeInsets.symmetric(vertical: 8.0),
                            child: NotificationCard(
                              notification: _notifications[index],
                              onTap: () => _showNotificationDialog(_notifications[index]),
                            ),
                          );
                        },
                      ),
          ),
        ),
      ),
    );
  }

  Widget _buildShimmerList() {
    return Shimmer.fromColors(
      baseColor: Colors.white.withOpacity(0.1),
      highlightColor: Colors.white.withOpacity(0.3),
      child: ListView.builder(
        itemCount: 6, // Number of shimmer items
        itemBuilder: (context, index) {
          return Padding(
            padding: const EdgeInsets.symmetric(vertical: 8.0),
            child: Container(
              height: 100,
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.1),
                borderRadius: BorderRadius.circular(16),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Image.network(
            'https://firebasestorage.googleapis.com/v0/b/squeak-c005f.appspot.com/o/rb_1220.png?alt=media&token=8c71b107-7849-475e-91d8-feab8b7a4f27', // Placeholder image
            height: 150,
            width: 150,
            color: Colors.white.withOpacity(0.7), // Tint for dark background
            colorBlendMode: BlendMode.modulate,
          ),
          const SizedBox(height: 24),
          const Text(
            'No Notifications Yet!',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            'It looks like your inbox is empty. Check back later for updates.',
            style: TextStyle(
              fontSize: 16,
              color: Colors.white.withOpacity(0.8),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
