// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyAwhHjpQvkqMoFhDfUK17NbVZekVjzraJo',
    appId: '1:205107241099:web:742c2df429f0b6951d748e',
    messagingSenderId: '205107241099',
    projectId: 'squeak-c005f',
    authDomain: 'squeak-c005f.firebaseapp.com',
    storageBucket: 'squeak-c005f.appspot.com',
    measurementId: 'G-23NW16VYQS',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyDwEglyUZAt_RlZrPV-9JisoUOwomx09R0',
    appId: '1:205107241099:android:312e685d4db90d561d748e',
    messagingSenderId: '205107241099',
    projectId: 'squeak-c005f',
    storageBucket: 'squeak-c005f.appspot.com',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyCGEwNxaFNT1hbiTBgxwI-JQLYLHEX9Wlo',
    appId: '1:205107241099:ios:dfb091fbcb882e6a1d748e',
    messagingSenderId: '205107241099',
    projectId: 'squeak-c005f',
    storageBucket: 'squeak-c005f.appspot.com',
    iosBundleId: 'com.softicare.squeak',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyCGEwNxaFNT1hbiTBgxwI-JQLYLHEX9Wlo',
    appId: '1:205107241099:ios:64f6cd9f8b5e8f971d748e',
    messagingSenderId: '205107241099',
    projectId: 'squeak-c005f',
    storageBucket: 'squeak-c005f.appspot.com',
    iosBundleId: 'com.example.squeak',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyAwhHjpQvkqMoFhDfUK17NbVZekVjzraJo',
    appId: '1:205107241099:web:456e911deb0dd3fb1d748e',
    messagingSenderId: '205107241099',
    projectId: 'squeak-c005f',
    authDomain: 'squeak-c005f.firebaseapp.com',
    storageBucket: 'squeak-c005f.appspot.com',
    measurementId: 'G-GG5QEKX7B1',
  );
}
