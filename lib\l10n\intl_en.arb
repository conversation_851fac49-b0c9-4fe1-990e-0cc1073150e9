{"login": "<PERSON><PERSON>", "register": "Register", "email": "Email Address", "address": "Address", "enterUrEmail": "Enter your email address", "enterUrEmailOPhone": "Please Enter your email or phone number", "enterUrEmailORPhone": "Enter your email or phone number", "enterUrEmailOPhoneValid": "Please Enter a valid email or phone number", "checkYourEmail": "Check Your Email", "receiveEmail": "Enter your email to\n Receive the instruction to reset your password", "sendMeNow": "Send me now", "enterUrPassword": "Enter your password", "enterUrAddress": "Enter your address", "enterCharPassword": "Password more than 6 charts", "password": "Password", "changePassword": "Change Password", "save": "Save", "edit": "Edit", "myPets": "My pets", "addPetService": "Service", "myFavorites": "My favourites", "inviteFriends": "Invite friends", "help": "Support", "settings": "Settings", "signOut": "Sign Out", "skip": "<PERSON><PERSON>", "male": "Male", "female": "Female", "verification": "Verification", "sentVerification": "We have sent a verification code to", "receiveCode": "Enter the received code", "enterCode": "Enter code your sent", "enterNewPassword": "Enter your new password", "enterConfirmNewPass": "Enter your confirm new password", "confirmNewPassword": "Confirm New Password", "newPassword": "New Password", "confirmPassword": "Confirm Password", "comparePassword": "Enter confirm your password", "notEqualPassword": "confirm password not equal password", "terms": "By signing up you agree to our Terms of use and Privacy Policy", "signUpAsADoctor": "Sign up as a Doctor", "alreadyHaveAccount": "Already have account?", "fullName": "Full Name", "phone": "Phone", "enterPhone": "Enter your phone", "haveNotAccount": "Don't have an account?", "updateProfile": "Update Profile", "logout": "LogOut", "forgotPass": "Forgot password?", "enterAValidEm": "Enter a valid email", "signUp": "Sign Up", "enterName": "Enter your Full Name", "enterCharNamePls": "name more than 6 charts ", "createPost": "Create Post ", "labelPost": "What\\'s on your mind ?", "addRecord": "<PERSON><PERSON>", "addPet": "Add Pet", "petName": "Pet Name", "NameOfRecord": "Name Of Record", "DateOfRecord": "Data", "TimeOfRecord": "Time", "yourAppointments": "Appointments", "AddServiceSupplier": "Add Service Supplier", "ServiceName": "Service Name", "ServicePhone": "Service Phone", "ServiceLocation": "Service location", "ServiceImage": "Add Service Image", "search": "Search", "searchValid": " Add Service", "searchFor": "Search For Service", "VerifyPhone": " Verify Phone", "addComment": "Add  Your Comment . . . .", "addReplayComment": "Add  Reply  Comment . . . .", "notifications": "Notifications", "followConfirmation": "Follow Confirmation", "unfollowConfirmation": "Unfollow Confirmation", "unfollow": "Unfollow", "cancelFollow": "Cancel", "publish": "Publish", "enterYourText": "What are you thinking .... ?", "species": "Species", "yourClinic": "Clinics", "name": "Name", "speciality": "Speciality", "addressCity": "Location", "profile": "Your Profile", "darkMode": "Dark Mode", "langMode": "language Mode", "removeClinic": "Remove Clinic", "addAvailabilities": "Add available times", "openAt": "Open At", "closeIn": "Close In", "booking": "Booking", "addAppointment": "Add Appointment", "chosePet": "<PERSON><PERSON>", "time": "Time", "date": "Date", "updateAppointment": "Update Appointment", "generalInformation": "General Information", "doctorName": "Doctor Name", "editComment": "Edit Comment . . . .", "editCommentPost": "Edit Comment", "editReplyCommentPost": "Edit Reply Comment", "reply": "Reply", "view1Reply": "View 1 reply", "viewReplies": "View {count} replies", "replies": "replies", "comments": "Comments", "deleteComment": "Delete Comment", "commentReply": "Comment Reply", "yourUpcomingAppointments": "Your Upcoming Appointments", "breed": "breed", "gender": "gender", "uniqueCode": "Unique Code", "location": "location", "city": "city", "removePost": "Remove post", "send": "Send", "clinicDoctor": "Clinic Doctors", "gaber": "testEN", "appointmentStatus": "Done", "appointmentReserved": "Reserved", "appointmentCanceled": "Canceled", "appointmentDone": "Done", "appointmentExamination": "clinic exam", "appointmentButtonCancel": "Cancel", "appointmentButtonEdit": "Edit", "appointmentButtonCall": "Call", "appointmentModalTitle": "Cancel Confirmation", "appointmentModalDescription": "Are you sure you want to cancel Appointment to", "appointmentModalButtonYes": "Yes", "appointmentModalButtonNo": "No", "appointmentButtonBooking": "Book again", "updateVersionModuleTitle": "Update App ?", "updateVersionModuleContent": "Your app needs to be updated\n", "updateVersionModuleContent2": "This version of the application is outdated.\nPlease go to the store to update", "updateVersionModuleButtonIgnore": "IGNORE", "updateVersionModuleButtonLater": "LATER", "updateVersionModuleButtonUpdateNow": "UPDATE NOW", "noAppointment": "No Appointments", "addPetChoose": "Choose..", "versionNumber": "Version", "HomePageSearchText": "Search", "HomePageSearchText2": "Search For Your Doctor", "clinicFollowedBefore": "You have already followed this clinic before", "offlineMessagesTitle": "No Internet Connection", "offlineMessagesContent": " check your internet connection and try again", "startAppointment": "Start Appointment", "reviewTitle": "Your Feedback", "reviewDescription": "Help us to provide the best care for your beloved pets.", "CleanlinessOfClinic": "Cleanliness of the Clinic", "DoctorService": "Doctor's Service", "submit": "Submit", "reviewMessage": "Your review has been submitted successfully.", "reviewMessage2": "You can view your review by Appointment Screen.", "clinic": "Clinic", "invoiceNo": "Invoice No", "taxId": "Tax ID", "petDetails": "Pet Details", "itemName": "Item Name", "price": "price", "pet": "Pet", "speciesOne": "Species", "sex": "Sex", "ownerDetails": "Owner Details", "qty": "Qty", "total": "Total", "totalAmount": "Total Invoice", "paid": "Paid", "vat": "vat", "returnPolicy": "Return & Exchange Policy: Items can be returned within 3 days of purchase with receipt. Medical products are non-refundable.", "paymentType": "Type", "payment": "Payment", "value": "Value", "Orloginasadoctor": "Or login as a doctor", "PASSWORD_MIN_LENGTH": "Password must be at least 6 characters long", "email_hint": "Enter your email address", "email_validation": "Please enter a valid email address", "email_valid": "Please enter email address", "phone_hint": "Enter your phone number", "phone_validation": "Please enter your phone number", "name_hint": "Enter your full name", "name_validation": "Please enter your name", "title_hint": "Enter the title", "title_validation": "Please enter a title", "message_hint": "Enter your message", "message_validation": "Please enter a message", "address_hint": "Enter your address", "address_validation": "Please enter your address", "service_hint": "Select a service", "service_validation": "Please select a service", "password_hint": "Enter your password", "password_validation": "Please enter a password", "phone_or_password_hint": "Enter your phone number or password", "phone_or_password_validation": "Please enter your phone number or password", "birthdate_hint": "Enter your birthdate", "birthdate_validation": "Please enter your birthdate", "pet_birthdate_hint": "Enter your pet's birthdate", "pet_birthdate_validation": "Please enter your pet's birthdate", "filter_hint_pets": "Filter by pet", "filter_hint_State": "Filter by status", "appointmentsAll": "All", "appointmentsReserved": "Reserved", "appointmentsAttended": "Attended", "appointmentsStartExamination": "Start Examination", "appointmentsEndExamination": "End Examination", "appointmentsFinished": "Finished", "appointmentsCanceled": "Canceled", "crNumber": "CR", "swapPet": "Swipe left or right to select a pet", "goToClinicsFromPetsScreen": "Go to clinics", "filesAndPrescription": "Files And Prescription", "bill": "Bill", "rate": "Rate", "prescription": "Prescription", "files": "Files", "followCode": "Follow Code", "reminderOtherHintText": "Please enter your reminder type", "other_valid": "Please enter valid title", "other_valid_more_than_30_char": "the range is from 1 - 30 char", "spayed": "Spayed", "notSpayed": "Unspayed", "editPet": "Edit Pet", "enterPetName": "Enter pet name", "enterPetNameValidation": "Please enter pet name", "birthdate": "Date of birth", "birthdateValidation": "Please enter date of birth", "deletePhoto": "Delete Photo", "changePhoto": "Change Photo", "delete": "Delete", "cancel": "Cancel", "deleteConfirmation": "Delete Confirmation", "petDeletedSuccessfully": "Pet Deleted Successfully", "selectPetType": "Select Pet Type", "noPetsFound": "No Pets Found", "addYourFirstPet": "Add Your First Pet", "about": "About", "couldNotOpenLink": "Could not open link", "version": "Version", "failedToLoadVersion": "Failed to load version", "trademarkInfo": "Trademark Information", "privacyPolicy": "Privacy Policy", "copyright": "© 2023 Quad Insight. All rights reserved", "lastUpdated": "Last updated", "infoWeCollect": "Information We Collect", "infoWeCollectDesc": "We may collect the following types of information:", "personalInfo": "Personal identification information (Name, email address, phone number, etc.)", "howWeUse": "How We Use Your Information", "howWeUseDesc": "Your information is used to provide and improve our services.", "infoProtection": "Information Protection", "infoProtectionDesc": "We take data security seriously and implement appropriate measures to safeguard your personal information against unauthorized access, alteration, or disclosure.", "sharingInfo": "Sharing Your Information", "sharingInfoDesc": "We do not sell or trade your personal information. However, we may share certain information with trusted third-party service providers to support and improve our services. These providers are required to adhere to strict data protection standards and are only permitted to use the information for the purposes outlined in our privacy policy.", "dataSecurity": "Data Security and Privacy", "dataSecurityDesc": "Your privacy and the security of your clinic's data are of utmost importance to us. We implement industry-standard security measures to protect your data against unauthorized access, alteration, and disclosure. Our systems are regularly updated to address potential vulnerabilities and ensure the highest level of protection.", "userControl": "User Control Over Data", "userControlDesc": "You have full control over the data you share with us. You can update, modify, or delete your personal and clinic information at any time through our platform. We provide clear settings and options for managing your data to ensure you are always in control of what information is stored and shared.", "dataEncryption": "Data Encryption", "dataEncryptionDesc": "All data transmitted between our application and our servers is encrypted using secure protocols (such as HTTPS). Additionally, sensitive information, including your clinic's data and payment information, is encrypted and securely stored to prevent unauthorized access.", "compliance": "Compliance with Data Protection Regulations", "complianceDesc": "We comply with relevant data protection laws and regulations.", "dataRetention": "Data Retention Policy", "dataRetentionDesc": "We retain your data only for as long as necessary to provide you with our services and for legitimate business purposes, such as maintaining records for financial, legal, or compliance reasons. Once the data is no longer needed, we take steps to securely delete or anonymize it.", "securityAudits": "Regular Security Audits", "securityAuditsDesc": "We conduct regular security audits to identify and address potential security risks. Our team is dedicated to continuously monitoring and enhancing our security measures to ensure the safety and privacy of your clinic's data.", "contactPrivacy": "Contact Us About Privacy", "contactPrivacyDesc": "If you have any concerns or questions about how we handle your data or if you would like to exercise your data rights, please contact our privacy <NAME_EMAIL>. We are here to ensure that you feel confident and secure in using our services.", "updateNotification": "Update Notification", "updateNotificationDesc": "We may update our privacy policy from time to time. When we do, we will notify you by posting the new policy on this page. It is your responsibility to review this privacy policy periodically for any changes.", "yourConsent": "Your Consent", "yourConsentDesc": "By using our app, you consent to our privacy policy.", "contactUs": "Contact Us", "contactUsDesc": "If you have any questions about this privacy policy, please contact <NAME_EMAIL>.", "copyright2": "© 2024 VetICare. All rights reserved.", "emailCopied": "Email copied to clipboard", "personalization": "Personalization", "language": "Language", "setLanguage": "Set the app language", "chooseViewMode": "Choose view mode", "notificationsAlert": "Notifications Alert", "other": "Other", "shareApp": "Share App", "helpShare": "Help us to share the app", "contactProblem": "Contact us in case of any problem", "logoutConfirm": "Do you want to log out?", "yes": "Yes", "no": "No", "arabic": "Arabic", "english": "English", "yourProfile": "Your profile", "updateSuccess": "Update successfully", "accept": "Accept", "ignore": "Ignore", "expiredRequest": "The follow request you\\'re looking for is no longer available. '\n                      'It may have been cancelled, expired, or already accepted.", "followRequest": "Follow Request", "followRequestUnAvailable": "Follow Request Unavailable", "petsVetICare": "Pets in vetIcare clinic", "noPetsInVetICare": "No pets are available in this clinic, or they have already been added to SQueak", "addToSqueak": "Add to squeak", "linkAnotherPet": "<PERSON> another pet", "yourPets": "Your SQueak Pet", "compeleteSqueakRegister": "Complete the register from vetIcare invitation", "selectPet": "Please select a pet to start service", "pleaseSelectBoarding": "Please select boarding Type name", "pleaseSelectCage": "Please select <PERSON>", "boardingType": "Boarding Type", "selectBoarding": "Select Boarding Type name", "boardingTypeNoteHour": "Note that you select boarding price in hours", "boardingTypeNoteDay": "Note that you select boarding price in days", "boardingCage": "Cage", "cageSelect": "Select Cage", "entryDate": "Entry Date", "exitDate": "Exit Date", "boardingPrice": "Initial cost", "boardingBy": "Boarding By", "selectBoardingBy": "Select Doctor", "ShareImagesPet": "Share Images Pet", "findPotentialMates": "Search for pets..."}