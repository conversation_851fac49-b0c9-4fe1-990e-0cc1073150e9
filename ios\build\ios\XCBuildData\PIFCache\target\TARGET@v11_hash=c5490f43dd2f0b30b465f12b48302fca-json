{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985264b225f9bd1d08909682c4caf3a1c7", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseFirestore", "PRODUCT_NAME": "FirebaseFirestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980f14a361f308cd6cad4572f4f9841138", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984ee1858ca73a4aa8c9828ca60cb9f5d4", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseFirestore", "PRODUCT_NAME": "FirebaseFirestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9813df31908c1eda77abdf83fb45e2462c", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984ee1858ca73a4aa8c9828ca60cb9f5d4", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseFirestore", "PRODUCT_NAME": "FirebaseFirestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d1414beeeb45f01d9fc32414233e16e3", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a63a4e87885f1d907594f9d645c57334", "guid": "bfdfe7dc352907fc980b868725387e9878096d6c74c7247619487771f8a2fac0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ecf7cd576e786721b419aa88c0de2ee", "guid": "bfdfe7dc352907fc980b868725387e9831d0dc72381b3f97e7b8d81df4dae4e1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98737aa1e47d84411cd80edc27952f4277", "guid": "bfdfe7dc352907fc980b868725387e982e97933e8907e0dd3e47a408ffd719de", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9885b9d468435223a7d23cd6677573b7df", "guid": "bfdfe7dc352907fc980b868725387e98454d3d42e9aa7ef24d765b38c6560184", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c8c4caab3d20005b79dbccc4c5b1c1e9", "guid": "bfdfe7dc352907fc980b868725387e98e0ca5d5c8e25d2a108249cf346951cf2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9892d44315d8597df4cd831330b362895b", "guid": "bfdfe7dc352907fc980b868725387e98b217214a54534728d063b12d564247ed", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9835b26752b69ab7dfe6eb21a36668640a", "guid": "bfdfe7dc352907fc980b868725387e98e4447d21d2886b0c9067eed64eb9b1d0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981ce083f771afb0bbea0a12e602df0112", "guid": "bfdfe7dc352907fc980b868725387e985330517a8a039c42741f8c1dce9bf235", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e3206a9cac02a89b136eff953642132f", "guid": "bfdfe7dc352907fc980b868725387e987e4a4c42a5a3a2975456ed1e302d9a5f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98062f76af2c6e13624843fa8b5b8311c8", "guid": "bfdfe7dc352907fc980b868725387e987b154ae4fd7ec0df9f3e75f253761fad", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d5ff971bd9960b4d57a7da99b9a75731", "guid": "bfdfe7dc352907fc980b868725387e984b7ebe8387938887389c796d1ad3d1c8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d7bc1de93ee3b522eca3d542f801565a", "guid": "bfdfe7dc352907fc980b868725387e98a28118fa39496416b6f469e8c5a3180f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982dda92441d2f20a3f4d9f7875c491513", "guid": "bfdfe7dc352907fc980b868725387e98139950db984145966725276c55b10802", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98adee1e3e9a8a2647c35652e15fe06342", "guid": "bfdfe7dc352907fc980b868725387e988d6aa2aa6e0999bba53891a55984a12f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9820f86032d041e806ced9701bbdf5c206", "guid": "bfdfe7dc352907fc980b868725387e9889766736405285cc2d7ed7b59de71b88", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981641bc23c74bb6b26079037a1b46bc3d", "guid": "bfdfe7dc352907fc980b868725387e98a3f6486af063fba65ecb7bd483e8721e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98675383affec3070f5a55336af5efb9ec", "guid": "bfdfe7dc352907fc980b868725387e986cf9d9ac9a66eea4fa8903761b72f498", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b5ebd525174fe54bd68c31051bc28370", "guid": "bfdfe7dc352907fc980b868725387e98bc0cb634632ea9eacd19ad3e6385491b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9888d1ab938927eeaa29b540c2760523a9", "guid": "bfdfe7dc352907fc980b868725387e98091a5149776ff2b26fdd2ad46eae7611", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d30dfa8f874ee4e3f963851aa880e105", "guid": "bfdfe7dc352907fc980b868725387e98eb5d161c420516468568e13f07e16886", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981ee508f051a86b3dcad88077615f4194", "guid": "bfdfe7dc352907fc980b868725387e985ab991ffc8ed9c206e71fdc312efc04f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9818751a0e1c4978c8a679dabdabf29115", "guid": "bfdfe7dc352907fc980b868725387e980031812414d241bfbaabb3d42003806b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983a9edbe117901203262ebf8b2d664603", "guid": "bfdfe7dc352907fc980b868725387e9818e0dbfc9353fe562f754f02dadf4a71", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a6bd4771829e54f458e144ab2ed3ba42", "guid": "bfdfe7dc352907fc980b868725387e9835607286b5919651cf262fb775937e43", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981adc349fb964a86cdd71911264424a42", "guid": "bfdfe7dc352907fc980b868725387e9894fbc3c0876ef1cd39ac8bf2b2fe4e8a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e762c2b0abd4132a0cd106e1d199d728", "guid": "bfdfe7dc352907fc980b868725387e989052bdfa77037a838087d8d76cb43e09", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98185cd378d6ece1b073beede17b55ddc2", "guid": "bfdfe7dc352907fc980b868725387e98c2efdf8eaa8589602a045ac8d5232703", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e97f7d822b2b7dff275f13e84e999b7", "guid": "bfdfe7dc352907fc980b868725387e9899398f3f376cfe6274852bb74c5411ac", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bfe9a96fd2567ed3e379e93e482ce68a", "guid": "bfdfe7dc352907fc980b868725387e98be633d5bc3174edffb9aca62718e2a59", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98350ec6989177fdbcaf13efc6d5caef3e", "guid": "bfdfe7dc352907fc980b868725387e985b927a6cab1b991b4277330e4c111589", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98262ee37e6ed06707be39567331339d24", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e981633f746d798640e77ee46d0fe13a771", "guid": "bfdfe7dc352907fc980b868725387e98696526bbbaca0d0999a3f0cb27b13501"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983c9a9987ecd7aa228a07158c9276121b", "guid": "bfdfe7dc352907fc980b868725387e9861abc15aac6c1b594d64565b300f5d86"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9867f62f45d9e6db316267c62654763010", "guid": "bfdfe7dc352907fc980b868725387e984c2c74613a74b0f6c58d2d80e29d8831"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985017fb03b4484c3ce96c7ab8a4f626e5", "guid": "bfdfe7dc352907fc980b868725387e986d31612ac2882b5b74b634833f4c1427"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b001de155cef768df33a86bd01df86aa", "guid": "bfdfe7dc352907fc980b868725387e9831dc4ed29fb206c20d00a00c0e08167b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9857a92aa726f1b96cc9dce62aeeffa84e", "guid": "bfdfe7dc352907fc980b868725387e98bf9b2f582cd8535b00cd07c5c35c9208"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98208a295f534037ace9ed946fe4e4f161", "guid": "bfdfe7dc352907fc980b868725387e9844628f58ddcd357be09f56379e8bc6ee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98989d952c460f08a1a5794eae2b320b6b", "guid": "bfdfe7dc352907fc980b868725387e983b61c8bf9963449cf9ab7d63ed1309f4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988124caed6652c44e97e0b58e383eba6e", "guid": "bfdfe7dc352907fc980b868725387e98bb0b42efcaa652c2a5db2022b4dce943"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec5c32bf85487e82b25ca819ebb545e5", "guid": "bfdfe7dc352907fc980b868725387e98bf03799c452794e6b22856daf3b7342d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9807980240db6509f6ade936613ae9c5f9", "guid": "bfdfe7dc352907fc980b868725387e98ed4d36e693a19eb3c19330e29afad89e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a2816c3cecb901d04abe2bcbfc10ce90", "guid": "bfdfe7dc352907fc980b868725387e9840a8de6fc4481d1c9b996a627a660f85"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989824e9d3c0f280b2c9096774ae5aaa49", "guid": "bfdfe7dc352907fc980b868725387e980e2d34c483f5c2a8c91fe4ac2e81871e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b0af2e04df8d6fc675d669d6194e32d6", "guid": "bfdfe7dc352907fc980b868725387e9886e7566f92d195dcaf026b4b23d51d50"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985b452e5952eef86334d8551e414f63a1", "guid": "bfdfe7dc352907fc980b868725387e989005fdb76353947682d1a472c697da46"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a3736a3bdf5fab44b50a1964bbd3c8b2", "guid": "bfdfe7dc352907fc980b868725387e98e0a8a4af3e28962aa72a4479e4f05bef"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987a4648cc24d684b06f9032aa4adb97bb", "guid": "bfdfe7dc352907fc980b868725387e98a19ea79eda0c273a23157a221335dee1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984dea9058e9e3853bc525df4a575ea57c", "guid": "bfdfe7dc352907fc980b868725387e985af9cdd43c14fefb02a4da6702d80c6c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f2e24b03580ee2ad3bccfda6497e5e84", "guid": "bfdfe7dc352907fc980b868725387e980cd7e4b753a7d344a5687685c2e130f4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9863ea7e34f0920c90154dc6488771ccb8", "guid": "bfdfe7dc352907fc980b868725387e98e1b29342c7820e8d4ad57fb7e0c66b73"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d27a1c8eddd7875479ce775cf141a9b", "guid": "bfdfe7dc352907fc980b868725387e98ea2e3620c4385ecb345e0d69f96edad8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b9816f65b9329580476a3c188bdeb59b", "guid": "bfdfe7dc352907fc980b868725387e98b1f1d40aaeb083a55127b64f23958c25"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a4a57e3d1da6b32de51dcf8767f51d81", "guid": "bfdfe7dc352907fc980b868725387e989d120918bedc6ffd650d8566c41c5323"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c983a14d0fc3a7c4a61e1f30947b5085", "guid": "bfdfe7dc352907fc980b868725387e985191733d1dbde50af2e99bbc61698f70"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983a16106f02d996c2d47a528d6dfc83e8", "guid": "bfdfe7dc352907fc980b868725387e985cc6781c90e4cb6b0f0dae9129cc991f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d4423c98b3d47254b136e21d1072c918", "guid": "bfdfe7dc352907fc980b868725387e9821b89cd1178c04b23d7446f05bae5e6a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae6f1b28ce918986527f910a5592318b", "guid": "bfdfe7dc352907fc980b868725387e98504c9b246e79e7b50a3645d94cc1a885"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98edbc151e372f126c8a68036f9a486eb5", "guid": "bfdfe7dc352907fc980b868725387e9857fade8faacf75144e7be9b0410da5ef"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f5ebd2772e9c0438054bf34ca630ced3", "guid": "bfdfe7dc352907fc980b868725387e98f930d7725ce23c799eb4e8f8815c3efb"}], "guid": "bfdfe7dc352907fc980b868725387e988ddf697a5022153cdc0df7763826c8d0", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9824f5d6ec5088eecbb83955c855580732", "guid": "bfdfe7dc352907fc980b868725387e9804fa6b4ab3d612ac75eb29b1be17d30a"}], "guid": "bfdfe7dc352907fc980b868725387e9872e0b297f90a0e5a90aee75b52e7f638", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9842de2ce555820c27e34ded0ad93814bb", "targetReference": "bfdfe7dc352907fc980b868725387e9823d9e39e74dec872f3c67d98403b9340"}], "guid": "bfdfe7dc352907fc980b868725387e98d2681b9b0645d61385f2323c866724b1", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e982fcb5e27d041e48b96b3ab14ce32d5f2", "name": "FirebaseCoreExtension"}, {"guid": "bfdfe7dc352907fc980b868725387e9823d9e39e74dec872f3c67d98403b9340", "name": "FirebaseFirestore-FirebaseFirestore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98919212c22943df12241906dd601cdff4", "name": "FirebaseFirestoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e982a62e2c60acb8d344a6411a0606a13d4", "name": "FirebaseSharedSwift"}], "guid": "bfdfe7dc352907fc980b868725387e98c075cc473fa5680b867d51f1363214ff", "name": "FirebaseFirestore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9809dfd848e2259e061e90089e1647f5b7", "name": "FirebaseFirestore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}