{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ce382db62f00a4970eeaf421b1b3ff17", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9801733cb838bfa75642186251063e4ebf", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985ddac7597ba44ec9bdf3d220d232603c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9845488f12f05c1e48dfb18a530e44ab5d", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985ddac7597ba44ec9bdf3d220d232603c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e982ab89853703c96f64083233fa2b10e24", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98dd7ea534aacf0a6ab1bd0effc0c630d9", "guid": "bfdfe7dc352907fc980b868725387e98aa9ebfa50eafbb13b554c36f9d46e7d9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9819e6c676a4d5b3718407eb8c62d6549a", "guid": "bfdfe7dc352907fc980b868725387e98131a8006544113de60c0885deb2c24c5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985be4f0340bc5cc133a7c44dc914a8ea7", "guid": "bfdfe7dc352907fc980b868725387e98c85cfc10d35f5c8f421d4e5d58c644b5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba276c1aa4a8d0d22edf99fde2ec0905", "guid": "bfdfe7dc352907fc980b868725387e98f0f0167d41012be45860499ccdee2ad3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da7dd30bd945d13cfad130b0ed086279", "guid": "bfdfe7dc352907fc980b868725387e98dc8c2ab2b3a57f3b13b2edc9be2863a9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98676f20cb6b8ea94aa66e4ba7ea6be522", "guid": "bfdfe7dc352907fc980b868725387e9806f43ac7e9faaabf84cf7063e797da6d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98818b7a54e4ac026937da9f8aefeda4e3", "guid": "bfdfe7dc352907fc980b868725387e989ecf26bd17fcc0183b926cfdf6d45ac8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec388c34c0475cd41280eba352e1a342", "guid": "bfdfe7dc352907fc980b868725387e988cb51ac1a85e8a6ed59eaff885b31a14"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9869bedafe60981cc5ad66f2337d82de9d", "guid": "bfdfe7dc352907fc980b868725387e98eb9c7d20e1e1d9e406e415e0888d04d3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982d8af9d94cc7c7be3cdb895838f841a0", "guid": "bfdfe7dc352907fc980b868725387e9886e19585c2d4dc81e09bc46f2613e05e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981711f259d203b0ca592f0b7e4a59c636", "guid": "bfdfe7dc352907fc980b868725387e98e5da2f4ffc06efc4b273b4dc60bd85eb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98218b1645af01bcdf39f501f61e287933", "guid": "bfdfe7dc352907fc980b868725387e988a54e2623701ae548efde93303fc6978", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e6e4bdd01c5506de98b1a6f67e083da8", "guid": "bfdfe7dc352907fc980b868725387e9883dc6ff3c15c9bc4b10619f18d035ee1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9899ab90cbe579dd6a3930aafab0db8546", "guid": "bfdfe7dc352907fc980b868725387e98bf5b40135bb82469a5b990f979341b48"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d19c48bd6c537353dfb7e587e567ab11", "guid": "bfdfe7dc352907fc980b868725387e98c177749bdd2aed461ec4c7e49ba222ee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98460b7c9266542bcf28e94bb1d8abecaf", "guid": "bfdfe7dc352907fc980b868725387e98f9e9048862c0e3feb47f48ee44f88896"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981424831a2b4dfe8eac4d73daa32e4e1e", "guid": "bfdfe7dc352907fc980b868725387e9860ed26eafa458628bfa778c2c7e40067"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982038a7aad8734bbedf05831ddbc80bfd", "guid": "bfdfe7dc352907fc980b868725387e9857cb332919f49ee82c2c3092edac594d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98280aea887e9396bbc188d0799f11a6bc", "guid": "bfdfe7dc352907fc980b868725387e98a0aedb1e35c3a4c577921d20f3dcd4e3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9864a5e86e29d8893670375cc756db9701", "guid": "bfdfe7dc352907fc980b868725387e986d6f4e2d91bfffe3a3378aed101f3385"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cdd2f5e6a184908356095cb2c628759d", "guid": "bfdfe7dc352907fc980b868725387e98dc30992919d55aa697b694a11e999957", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821f961d9840e05b316deca8c0e051484", "guid": "bfdfe7dc352907fc980b868725387e986356afbdf881b096468a2d620ace1957"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d1b98ca13b1aaf345bef7ad351535a2b", "guid": "bfdfe7dc352907fc980b868725387e98c5d0d1e3231646b8a288ffc7c1685a88", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9809616bb43fb9f161974617798fabf0a2", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9850d91b2490d08ef94e82e8dd4fd83b94", "guid": "bfdfe7dc352907fc980b868725387e98e78024c00837618fe8b98b7033a0ffed"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984abc3991aa22633cb71a3f69a3564f8c", "guid": "bfdfe7dc352907fc980b868725387e9896ec9dc5749de8234cb274b27df445ed"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983669973cbe49dd9cb4087e3b2edea02a", "guid": "bfdfe7dc352907fc980b868725387e987089476f92592f08313d2ae5bb566c41"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986ccf426f8992ea09298a82a621896496", "guid": "bfdfe7dc352907fc980b868725387e98c36fa3a6dc767551dae4722fc43c9166"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98610d57e0b9254baeee3c6d204c08b71f", "guid": "bfdfe7dc352907fc980b868725387e981bb4354af3096642d65bae750ae4112a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985f9e299a8b957d916777c63bb26b4f8d", "guid": "bfdfe7dc352907fc980b868725387e98bb56d66b661f2f8c46ce91c102a38892"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985a885ebd7438e9e7a60c71d5f1cdb04b", "guid": "bfdfe7dc352907fc980b868725387e9878c8365c7afad4b7daa55ac656a5253d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9823712f026aa22269cafe5f492e680a1e", "guid": "bfdfe7dc352907fc980b868725387e98b45236e85dc43ba6984591677f66828e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a61748392f0ba13051d0756af56b312d", "guid": "bfdfe7dc352907fc980b868725387e988a8eaf7508cf8785aaa48891992cbdd6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d700cd86ad71a639748963aa229df9a0", "guid": "bfdfe7dc352907fc980b868725387e981cd30699ab1b40ab4953ea3eded49313"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9808c7e38e3e47800b545285f56387c5d1", "guid": "bfdfe7dc352907fc980b868725387e98fda5ef817c878382f834d1619ad601b8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98567383250999e26eae2744560e4a45d3", "guid": "bfdfe7dc352907fc980b868725387e98f53b161cb58e1cb135c30a162c7bfa8a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e2ea8c12b56ce793dca37ae25123450c", "guid": "bfdfe7dc352907fc980b868725387e9865c1dbbb97a0ccfa4a24d166ae2a5ba0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9845c426e33e84494de5102d2b55d00495", "guid": "bfdfe7dc352907fc980b868725387e98468268bddbdc6a7533191cf65e8ca245"}], "guid": "bfdfe7dc352907fc980b868725387e98721e0614cca460439abea5b54a32fd9b", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9824f5d6ec5088eecbb83955c855580732", "guid": "bfdfe7dc352907fc980b868725387e98969574bccd5429255b343feb4aa0e891"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9848a7bf5f5ee928f3be52fdff6a2dc80a", "guid": "bfdfe7dc352907fc980b868725387e98518c65420b064822b6c6ae8d8e0f6785"}], "guid": "bfdfe7dc352907fc980b868725387e9817780723e7155885de74311ec0c58546", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e980ba764fb417db1d5a5c1e74486c3fadd", "targetReference": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14"}], "guid": "bfdfe7dc352907fc980b868725387e98d23e6df9e1d14bb6f55eba44c22c06b1", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14", "name": "FirebaseCore-FirebaseCore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e988ae261e418baab0fdd0a48d117fe7fa2", "name": "FirebaseCore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}