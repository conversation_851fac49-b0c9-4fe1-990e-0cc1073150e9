{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98336b75fca9078f517c3d2f19cc6d10c4", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a70b36a7efd01a60673909912a8128ef", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e989bd1d371139d0787d48423f670c7cf03", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9873d8835b43d21a029cd14e3943ae513b", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e989bd1d371139d0787d48423f670c7cf03", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98902fcd961a3a2c19f2dd0eb828bf6649", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d484d9d4b5869f6747f43207abeb25a8", "guid": "bfdfe7dc352907fc980b868725387e982f948253141e9d67cf9ca5133d283e54", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982544d2b01c6200bb1b8d34edf8e1f012", "guid": "bfdfe7dc352907fc980b868725387e98cdbe44fc2d4058a0f11db94abfd13c1e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98613ec3af169b5630f98d35606bfa9e1a", "guid": "bfdfe7dc352907fc980b868725387e981a644fecebd9725682288a1b961c79fd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984c39dd49036874dc71e634f494c599d2", "guid": "bfdfe7dc352907fc980b868725387e987ea3439617339d9c79832f8bad568e42", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986b448a7ee88e2dc7511781da19761df3", "guid": "bfdfe7dc352907fc980b868725387e98b2b5ad28c91b5911bf8a07a20bfd5d34", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9864aa24463d98d848ef227327cbe7e364", "guid": "bfdfe7dc352907fc980b868725387e98e03701978f2f9b64d5bced62d3018bf6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d19d54ac4c4a27636388b7c178456341", "guid": "bfdfe7dc352907fc980b868725387e983e6dcb0ea13dc10ae36e7149c850c0c7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9828d96262bb4131fa9c41d25a6ff481aa", "guid": "bfdfe7dc352907fc980b868725387e9808ba1eb92690f1ddb52f4904f8ffce62", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98738325f3b44fdffa5c2e2c5d6f1a967a", "guid": "bfdfe7dc352907fc980b868725387e98f44dd4f19e49cb2eb0f4093204f6a589", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ac4b6e2b6f46eae6d3dace46ab09a6a1", "guid": "bfdfe7dc352907fc980b868725387e98837ad27ba0b6b557d7c6a98ba7aefb4a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b50fe10068d0c38bcccfc6e13c924a43", "guid": "bfdfe7dc352907fc980b868725387e98e4bdbf8fa523d20fae209fad1b3d95c9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981df95defcc9ab49cac45f16d5881c93f", "guid": "bfdfe7dc352907fc980b868725387e98a961028ed0e5c4299f7c9fd907a68a53", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c6bc1445f7ec318559df5803ff5e0e73", "guid": "bfdfe7dc352907fc980b868725387e989fdbe9c7a21be5a301c37264a3d1289f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9869133c7bdb407f61418e02a97dd82360", "guid": "bfdfe7dc352907fc980b868725387e98a894bda8eac820b3f64ca22430df57f7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f5bf200364976cb8b26143804ea5063", "guid": "bfdfe7dc352907fc980b868725387e98cc36b1d14598cd1218f04611577a72ca", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98596c9788bb8f00694589bdb728da9b4d", "guid": "bfdfe7dc352907fc980b868725387e985e5a34b2633d4e88ecc3a08dd2257f96", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983cbcc1064277f497b18f1a16bddc3ab5", "guid": "bfdfe7dc352907fc980b868725387e9835fa36cea4983e89f2341029a707412a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98759cda5242062d560b82f3e8040e2da1", "guid": "bfdfe7dc352907fc980b868725387e9837a7ccb4caa972b9369ddbb86620fb82", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f3bdd15a9e2fa7510ec59d255b6fde17", "guid": "bfdfe7dc352907fc980b868725387e98d9879785c53a6774798db0c02e6cc360", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98728ff31e6615143451787c6a7f9f7f75", "guid": "bfdfe7dc352907fc980b868725387e98524f7424912f664fe493bae7fef35116", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fa12431d49e75a9c9ea79424d87040a6", "guid": "bfdfe7dc352907fc980b868725387e98e47f58dd369e8b86b4a0da514ace8db4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983bcc32d1dae4a93edb1baa765f39af54", "guid": "bfdfe7dc352907fc980b868725387e980c567bf7026ac6c82f27aa7ccfd7ddae", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e988fef69131e6a5b9d88244a3ee19de531", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98bcc8a118ef12df92ab508089ab6417a5", "guid": "bfdfe7dc352907fc980b868725387e987cdf7da02b5e7f9b56dc62c71b1cf788"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb9be6e37ec31366cac05cc88b179622", "guid": "bfdfe7dc352907fc980b868725387e988b6943beba9e92d675b3508bb463aa0f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9872eaa13bd0a9ff7ed978b7729b6a036e", "guid": "bfdfe7dc352907fc980b868725387e9834b7de379326883f2cd638235d921b63"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ca90a8a3cebfd68df151b31c765cf7f2", "guid": "bfdfe7dc352907fc980b868725387e98e116dee7af0f6b96fedccea97183333c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ac7dd99ae5ee174e4c539a92ed686a1e", "guid": "bfdfe7dc352907fc980b868725387e98b332fe0e43b7daf238b288681b604b8a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981227aba1d07cb025081b9b27974e1b1c", "guid": "bfdfe7dc352907fc980b868725387e988aa35238f0bd32412bab2984e0a8e316"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986f83a5a4b0f15e2e4d6250467fe3f4fd", "guid": "bfdfe7dc352907fc980b868725387e98d2622a66accbbe5d8189557ac146b86c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fcb5a82f44722734fe19f2b40599c491", "guid": "bfdfe7dc352907fc980b868725387e981d997b5436c9e4e47538da02635fe5eb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98392249cb4f91b1fdae7e148839c0b1de", "guid": "bfdfe7dc352907fc980b868725387e9875f5f7dbf457973ea85bdb87f78e0ae5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982d592dfdb756c110eac71463cbbdbc9c", "guid": "bfdfe7dc352907fc980b868725387e98c3a0b7eada698858b85a15b943ec4048"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98491dc67d3e24cebb90b9c11f91bd6ccb", "guid": "bfdfe7dc352907fc980b868725387e981668e7410c4a12bdfd070abca58f3ce8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98484dcc8451e925dc88b9adb2747302ea", "guid": "bfdfe7dc352907fc980b868725387e98d36fe5e5a4261c40cdde48016c6d91a7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9815501f6be18b30645c196859b7cf6065", "guid": "bfdfe7dc352907fc980b868725387e9889cbff68a93510be79c9b01f358c27b0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d614af6af47f253451b07d85c5effefe", "guid": "bfdfe7dc352907fc980b868725387e98359e8964d0d451f3a4a710ba07c89eb6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98587e6e74e59a9cafd819ba1359d632d3", "guid": "bfdfe7dc352907fc980b868725387e98300e65482e8aaa902fa2a251d86af37b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ac9b1805204249c9c78cdd1f10163ac", "guid": "bfdfe7dc352907fc980b868725387e98b1b38c05b888e01acef1a149111539ca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9810bc81f641f46e08a80b31817a048df6", "guid": "bfdfe7dc352907fc980b868725387e98d2b588aabd6fba51bfafc84eb9393b65"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a79ec1b7f25378b5b55fd162b6244b10", "guid": "bfdfe7dc352907fc980b868725387e9827b74ff6e6a63f3259e50e226e7a43dd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ea86c2bb4b76e192ca04f9b5057e5b88", "guid": "bfdfe7dc352907fc980b868725387e985b86570f58f414b22c081ae2588494a9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9885371f7c5a7e7ce239a6f184277fce4b", "guid": "bfdfe7dc352907fc980b868725387e98955250eb108f84c2b12b4623ef1e6a84"}], "guid": "bfdfe7dc352907fc980b868725387e984886980985b14c12cef96be62109ae6c", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9824f5d6ec5088eecbb83955c855580732", "guid": "bfdfe7dc352907fc980b868725387e9859084b84c1e5c28fb42713b28fe6eadd"}], "guid": "bfdfe7dc352907fc980b868725387e983c8950756144aca2b9b2b6470003e09e", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98604d74189a8388471685cae968aa99ab", "targetReference": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823"}], "guid": "bfdfe7dc352907fc980b868725387e98b0fa00575b151c42526f06658883e135", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823", "name": "PromisesObjC-FBLPromises_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e981c795e45f8d875aac88217c6a2a95faa", "name": "FBLPromises.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}