{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f687d86e46ef12acd001c7b5d1fd42ba", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/geolocator_apple/geolocator_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/geolocator_apple/geolocator_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/geolocator_apple/geolocator_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "geolocator_apple", "PRODUCT_NAME": "geolocator_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984936d7cb8feef48513f6131e211caece", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a4ca787f9059b74bc415abbff3688925", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/geolocator_apple/geolocator_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/geolocator_apple/geolocator_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/geolocator_apple/geolocator_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "geolocator_apple", "PRODUCT_NAME": "geolocator_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98beaaf634ac9c45a28ea8b53ae8cea921", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a4ca787f9059b74bc415abbff3688925", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/geolocator_apple/geolocator_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/geolocator_apple/geolocator_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/geolocator_apple/geolocator_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "geolocator_apple", "PRODUCT_NAME": "geolocator_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9879f34f1e012c285b844492b857411475", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9886c8cb9ae08d90fbfddf87d6105a8319", "guid": "bfdfe7dc352907fc980b868725387e985176eb7aa89517dd79b622378e05c296", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d5291e3dd30b4af43884237a0fea2ac5", "guid": "bfdfe7dc352907fc980b868725387e98af69fb08e313617716d0d228cf215245", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98190b8ce9daa02bfece57d91e606fa31e", "guid": "bfdfe7dc352907fc980b868725387e985e2e93614b4b998292a11ff2a2c13b6d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98406bab57a0b53a6aae1d4c9866c9ab4d", "guid": "bfdfe7dc352907fc980b868725387e98f39594556e495895558fa1a08b655ba5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe9323840925346d7e3138fb0f9f8650", "guid": "bfdfe7dc352907fc980b868725387e9892d2936f43a59179d3e2fc2a4021e53e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98884395760a3fbe1fd1f3115f13a46a5c", "guid": "bfdfe7dc352907fc980b868725387e98de9bba16cdb7cae0aadac8b6746bb22f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f78ac8f210f5e45454cd7abbd2d57ee5", "guid": "bfdfe7dc352907fc980b868725387e986f2fd5217ee9ef9b9f743d7150a25c60", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba1ed1461e7193ee26db33d7dec49acc", "guid": "bfdfe7dc352907fc980b868725387e9838280bfcd8b736867eaa4077fbc0d61c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b52c294f6cd0de7fb06294add1dc5192", "guid": "bfdfe7dc352907fc980b868725387e985212bf823b2dbcc093385db1f1b30dec", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98785082589c614f0d96164f05b012c57a", "guid": "bfdfe7dc352907fc980b868725387e983b3e9d753ef701196ebaaa81a1e77d66", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d8c6ea04b96023a11cbd9767a42d7dd2", "guid": "bfdfe7dc352907fc980b868725387e985cf8cb83875e6694983c229dac8bc9fb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989196439704107908a1ed8808b6413e87", "guid": "bfdfe7dc352907fc980b868725387e988145d23612ab237ea91016460d73945a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987fbfb433536b59f5d1590d4a821f81e1", "guid": "bfdfe7dc352907fc980b868725387e982690b7c043bffc9f7499a3599fc5db55", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9894c21a3743648c6509b582fe813780d3", "guid": "bfdfe7dc352907fc980b868725387e984bcf2821c445a13f1ca5ddd1387c63d6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984f998a6fb90d719852375e2a0694bd85", "guid": "bfdfe7dc352907fc980b868725387e9814e771116f0efcc61da8fc335aa4ca3b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb48822393d8c199ae5f5674a7983d76", "guid": "bfdfe7dc352907fc980b868725387e9896786428e6a8d6c7862d939cae65f773", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c6e24d7f01d75abbe934ec9a5d11de51", "guid": "bfdfe7dc352907fc980b868725387e98790199ae3165d72360ab21230766b727", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9859ab3ec029f964cc4041f98492a33760", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e984b7efaae0e718567a49795bd402c6a7c", "guid": "bfdfe7dc352907fc980b868725387e98ac5c52be3190abee156605bfd48912ef"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9825c659f9a4696fe426795d597821afc2", "guid": "bfdfe7dc352907fc980b868725387e98e1de61ab1ff97cc94cd06a5371923da8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9847dd3089fa90e24fe587d59bbdb7f60f", "guid": "bfdfe7dc352907fc980b868725387e98d0242bdacab0261eec7e248947d90b7c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cf9c19cfbe84e9c9f88edeafe58e22c8", "guid": "bfdfe7dc352907fc980b868725387e982d942064c23276142327490fa33537b7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985917618dbb289e2f187a66a9e82c32aa", "guid": "bfdfe7dc352907fc980b868725387e98d9f834483786fc0c92f7593bcd6dd83c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985fba2aaa1e15af929c7a530592b5becb", "guid": "bfdfe7dc352907fc980b868725387e987f77bace7e1c1bfd0b3f575d26dfb06f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98050dfb4eb204f40f48c9bd7a6ea0b251", "guid": "bfdfe7dc352907fc980b868725387e9815ed68575bdb30e9bf33141f83bd83b9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f25cdd0084c8e79cb41cf1d6579663dc", "guid": "bfdfe7dc352907fc980b868725387e98590b8f41b31928b0a4926ce46a04415a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9834b497b58b998843ff138c6371985206", "guid": "bfdfe7dc352907fc980b868725387e98f6d629ffc18995e49d3d77a3aef0dcb0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983a9d159e924e944f6f0e27dd49433932", "guid": "bfdfe7dc352907fc980b868725387e98af9f015ea26609a22120bcae2d16071a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9886e8fcc2285595f069a3573a1b60ee45", "guid": "bfdfe7dc352907fc980b868725387e989c0bfab68da1fedec51cf36689157069"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a89302b3fb0901502880ff4d9f937dc0", "guid": "bfdfe7dc352907fc980b868725387e98c5820ac9b4d0a4a76d9404b84c0ec2b9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982cdeb50de9e980be28ad99ecc3421c7f", "guid": "bfdfe7dc352907fc980b868725387e98df9697c7b74a7cf4593f9d66eda4c228"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9882b98e63475dba8459d7b4b61a8948ad", "guid": "bfdfe7dc352907fc980b868725387e98df10a50fc47d08262ba22e5741f99c0e"}], "guid": "bfdfe7dc352907fc980b868725387e989d91e5abbad637a65716b7fef9466ccd", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9824f5d6ec5088eecbb83955c855580732", "guid": "bfdfe7dc352907fc980b868725387e9847d3b3fdbf8c6c03a84819a79f7ae5aa"}], "guid": "bfdfe7dc352907fc980b868725387e988ec00323e57392a3c283b014e048e33b", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98c4b351c20ac090098b0dcaf5fd3c18e2", "targetReference": "bfdfe7dc352907fc980b868725387e98e1aba8ff8dc833f2269ce0a7182533b3"}], "guid": "bfdfe7dc352907fc980b868725387e98053f546dc39b1661db608be4eb007f0a", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98e1aba8ff8dc833f2269ce0a7182533b3", "name": "geolocator_apple-geolocator_apple_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e9821d372cc1e7c7587a12aeda843619e39", "name": "geolocator_apple", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e986ff8f87e011522b1b6328c84d9533927", "name": "geolocator_apple.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}